import React, { useEffect } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ApolloProvider } from '@apollo/client'
import client from './graphql/client'
import { LoginPage, DashboardPage, ProfilePage, NotFoundPage } from './pages'
import { useIsAuthenticated, useLogin, useSetLoading } from './store'
import { getStoredAuthData } from './utils/auth'
import LoadingSpinner from './components/ui/LoadingSpinner'
import ErrorBoundary from './components/ErrorBoundary'

const AppContent: React.FC = () => {
  const isAuthenticated = useIsAuthenticated()
  const login = useLogin()
  const setLoading = useSetLoading()
  const [isInitializing, setIsInitializing] = React.useState(true)

  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true)
      try {
        const storedData = getStoredAuthData()
        if (storedData.user && storedData.token) {
          login(storedData.user, storedData.token)
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error)
      } finally {
        setLoading(false)
        setIsInitializing(false)
      }
    }
    initializeAuth()
  }, [])

  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <BrowserRouter>
      <Routes>
        {/* Root route - redirect based on auth status */}
        <Route
          path="/"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* Public routes */}
        <Route path="/login" element={<LoginPage />} />

        {/* Protected routes */}
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/profile/:userId" element={<ProfilePage />} />

        {/* 404 route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </BrowserRouter>
  )
}

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <ApolloProvider client={client}>
        <AppContent />
      </ApolloProvider>
    </ErrorBoundary>
  )
}

export default App
