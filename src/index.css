@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 text-white font-sans;
    @apply min-h-screen antialiased;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-material-lg;
  }

  .glass-button {
    @apply bg-primary-500/20 hover:bg-primary-500/30 backdrop-blur-sm border border-primary-400/30;
    @apply rounded-lg px-4 py-2 text-primary-100 font-medium transition-all duration-200;
    @apply hover:shadow-material-md hover:scale-105 active:scale-95;
  }

  .material-input {
    @apply bg-surface-800/50 border border-surface-600 rounded-lg px-4 py-3;
    @apply text-white placeholder-surface-400 focus:border-primary-400 focus:ring-2 focus:ring-primary-400/20;
    @apply transition-all duration-200 outline-none;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-300 to-accent-300 bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-material-xl;
  }
}
