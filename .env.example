# ============================================================================
# ENVIRONMENT CONFIGURATION EXAMPLE
# ============================================================================
# Copy this file to .env and customize the values for your deployment
# All values are optional - the application will use sensible defaults
# ============================================================================

# ============================================================================
# API CONFIGURATION
# ============================================================================
# Base API URL - customize for different environments
VITE_API_BASE_URL=https://learn.reboot01.com/api
VITE_GRAPHQL_ENDPOINT=https://learn.reboot01.com/api/graphql-engine/v1/graphql
VITE_AUTH_ENDPOINT=https://learn.reboot01.com/api/auth/signin

# API Timeouts and Retries
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# ============================================================================
# AUTHENTICATION CONFIGURATION
# ============================================================================
# Storage keys for tokens and user data
VITE_AUTH_TOKEN_KEY=auth_token
VITE_AUTH_USER_KEY=auth_user
VITE_AUTH_REFRESH_TOKEN_KEY=refresh_token

# Token settings (in milliseconds)
VITE_AUTH_TOKEN_EXPIRY=86400000
VITE_AUTH_SESSION_TIMEOUT=1800000
VITE_AUTH_REMEMBER_DURATION=2592000000

# Authentication features
VITE_AUTH_AUTO_REFRESH=true

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
# Enable/disable caching
VITE_CACHE_ENABLED=true

# Cache durations (in milliseconds)
VITE_CACHE_USER_DATA=300000
VITE_CACHE_STATISTICS=600000
VITE_CACHE_ANALYTICS=900000
VITE_CACHE_CHARTS=1200000
VITE_CACHE_ACHIEVEMENTS=1800000

# Cache limits
VITE_CACHE_MAX_ENTRIES=100
VITE_CACHE_MAX_MEMORY_MB=50

# ============================================================================
# UI THEME CONFIGURATION
# ============================================================================
# Theme colors (hex values)
VITE_THEME_PRIMARY=#14b8a6
VITE_THEME_SECONDARY=#64748b
VITE_THEME_ACCENT=#d946ef
VITE_THEME_BACKGROUND=#0f172a
VITE_THEME_SURFACE=#1e293b

# Component sizes (Tailwind CSS classes)
VITE_SIZE_AVATAR_XS=w-6 h-6
VITE_SIZE_AVATAR_SM=w-8 h-8
VITE_SIZE_AVATAR_MD=w-12 h-12
VITE_SIZE_AVATAR_LG=w-16 h-16
VITE_SIZE_AVATAR_XL=w-24 h-24
VITE_SIZE_AVATAR_2XL=w-32 h-32

VITE_SIZE_BUTTON_SM=px-3 py-1.5 text-sm rounded-md
VITE_SIZE_BUTTON_MD=px-4 py-2 text-sm rounded-lg
VITE_SIZE_BUTTON_LG=px-6 py-3 text-base rounded-lg
VITE_SIZE_BUTTON_XL=px-8 py-4 text-lg rounded-xl

# ============================================================================
# PERFORMANCE CONFIGURATION
# ============================================================================
# Bundle size warnings (in bytes)
VITE_CHUNK_SIZE_WARNING=500000
VITE_BUNDLE_SIZE_WARNING=2000000

# Performance features
VITE_LAZY_LOADING=true
VITE_SERVICE_WORKER=true
VITE_COMPRESSION=true

# Query settings
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100
VITE_QUERY_TIMEOUT=10000

# ============================================================================
# FEATURE FLAGS
# ============================================================================
# Core features
VITE_FEATURE_ADVANCED_CHARTS=true
VITE_FEATURE_REALTIME_UPDATES=false
VITE_FEATURE_OFFLINE_MODE=false
VITE_FEATURE_PWA=true

# Analytics features
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_ERROR_TRACKING=true
VITE_FEATURE_PERFORMANCE_TRACKING=true
VITE_FEATURE_USER_TRACKING=false

# UI features
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_ANIMATIONS=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_KEYBOARD_SHORTCUTS=true

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
# Security features
VITE_SECURITY_CSP=true
VITE_SECURITY_HTTPS=true
VITE_SECURITY_HSTS=true
VITE_SECURITY_XSS_PROTECTION=true

# Content Security Policy directives
VITE_CSP_DEFAULT_SRC='self'
VITE_CSP_SCRIPT_SRC='self' 'unsafe-inline' 'unsafe-eval'
VITE_CSP_STYLE_SRC='self' 'unsafe-inline'
VITE_CSP_IMG_SRC='self' data: https:
VITE_CSP_CONNECT_SRC='self' https://learn.reboot01.com

# ============================================================================
# APPLICATION METADATA
# ============================================================================
# App information
VITE_APP_NAME=Student Dashboard
VITE_APP_SHORT_NAME=Dashboard
VITE_APP_DESCRIPTION=Professional student analytics dashboard
VITE_APP_VERSION=1.0.0

# PWA settings
VITE_APP_START_URL=/
VITE_APP_DISPLAY=standalone
VITE_APP_BACKGROUND_COLOR=#0f172a
VITE_APP_THEME_COLOR=#14b8a6

# Contact information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_DOCS_URL=https://docs.example.com

# ============================================================================
# AVATAR CONFIGURATION
# ============================================================================
# Avatar storage providers
VITE_AVATAR_BACKBLAZE_URL=https://f002.backblazeb2.com/file/01-edu-system
VITE_AVATAR_BACKBLAZE_API=https://f002.backblazeb2.com/b2api/v1/b2_download_file_by_id
VITE_AVATAR_GITHUB_URL=https://github.com
VITE_AVATAR_GITHUB_SIZE=128
VITE_AVATAR_GRAVATAR_URL=https://www.gravatar.com/avatar
VITE_AVATAR_GRAVATAR_DEFAULT=identicon

# Avatar fallback settings
VITE_AVATAR_FALLBACKS=true
VITE_AVATAR_FALLBACK_INITIALS=true
VITE_AVATAR_FALLBACK_GITHUB=true

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================
# Debug settings (only active in development)
VITE_DEBUG_LOGS=false
VITE_DEBUG_PERFORMANCE=false
VITE_DEBUG_GRAPHQL=false

# Mock settings
VITE_MOCK_DATA=false
VITE_MOCK_DELAY=1000

# Hot reload
VITE_HOT_RELOAD=true

# ============================================================================
# DEPLOYMENT SPECIFIC
# ============================================================================
# Set these for specific deployment environments

# For staging environment:
# VITE_FEATURE_USER_TRACKING=true
# VITE_DEBUG_LOGS=true

# For production environment:
# VITE_FEATURE_ANALYTICS=true
# VITE_SECURITY_CSP=true
# VITE_CACHE_ENABLED=true

# For development environment:
# VITE_CACHE_ENABLED=false
# VITE_DEBUG_LOGS=true
# VITE_SECURITY_CSP=false
