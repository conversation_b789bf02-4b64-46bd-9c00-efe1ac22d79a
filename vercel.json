{"version": 2, "name": "graphql-profile-dashboard", "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/(.*)", "dest": "/frontend/dist/$1"}], "buildCommand": "npm run build", "outputDirectory": "frontend/dist", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}