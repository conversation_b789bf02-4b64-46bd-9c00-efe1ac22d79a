import{r as o,j as e,m as c,A as C}from"./animation-vendor-CTGg7XC5.js";import{j as u,I as p,p as w,w as y,X as b,C as k}from"./ui-vendor-DuyO0ClB.js";import"./react-vendor-Csw2ODfV.js";const S=({userId:A})=>{const[m,i]=o.useState([]),[d,x]=o.useState(!1),[n,l]=o.useState(0);o.useEffect(()=>{const t=setInterval(()=>{if(Math.random()>.7){const s=[{type:"achievement",title:"New XP Gained!",message:"You earned 250 XP from completing a project",icon:u},{type:"info",title:"Audit Available",message:"A new project is ready for your review",icon:p},{type:"success",title:"Project Passed!",message:"Your latest submission was approved",icon:w}],a=s[Math.floor(Math.random()*s.length)],r={id:Date.now().toString(),...a,timestamp:new Date,read:!1};i(h=>[r,...h.slice(0,9)]),l(h=>h+1)}},3e4);return()=>clearInterval(t)},[]);const f=t=>{i(s=>s.map(a=>a.id===t?{...a,read:!0}:a)),l(s=>Math.max(0,s-1))},g=()=>{i(t=>t.map(s=>({...s,read:!0}))),l(0)},j=t=>{i(a=>a.filter(r=>r.id!==t));const s=m.find(a=>a.id===t);s&&!s.read&&l(a=>Math.max(0,a-1))},N=t=>{switch(t){case"success":return w;case"warning":return k;case"info":return p;case"achievement":return u;default:return p}},v=t=>{switch(t){case"success":return"text-green-400 bg-green-500/20 border-green-500/30";case"warning":return"text-yellow-400 bg-yellow-500/20 border-yellow-500/30";case"info":return"text-blue-400 bg-blue-500/20 border-blue-500/30";case"achievement":return"text-purple-400 bg-purple-500/20 border-purple-500/30";default:return"text-gray-400 bg-gray-500/20 border-gray-500/30"}};return e.jsxs("div",{className:"relative",children:[e.jsxs(c.button,{onClick:()=>x(!d),whileHover:{scale:1.05},whileTap:{scale:.95},className:"relative p-2 text-white/70 hover:text-white transition-colors",children:[e.jsx(y,{className:"w-6 h-6"}),n>0&&e.jsx(c.span,{initial:{scale:0},animate:{scale:1},className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold",children:n>9?"9+":n})]}),e.jsx(C,{children:d&&e.jsxs(c.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},className:"absolute right-0 top-12 w-96 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-700 flex items-center justify-between",children:[e.jsx("h3",{className:"text-white font-semibold",children:"Notifications"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[n>0&&e.jsx("button",{onClick:g,className:"text-xs text-primary-400 hover:text-primary-300 transition-colors",children:"Mark all read"}),e.jsx("button",{onClick:()=>x(!1),className:"text-white/60 hover:text-white transition-colors",children:e.jsx(b,{className:"w-4 h-4"})})]})]}),e.jsx("div",{className:"max-h-80 overflow-y-auto",children:m.length===0?e.jsxs("div",{className:"p-8 text-center text-white/60",children:[e.jsx(y,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),e.jsx("p",{children:"No notifications yet"}),e.jsx("p",{className:"text-sm mt-1",children:"We'll notify you of important updates"})]}):e.jsx("div",{className:"space-y-1",children:m.map(t=>{const s=N(t.type),a=v(t.type);return e.jsx(c.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},className:`p-4 border-l-4 ${a} ${t.read?"bg-transparent":"bg-white/5"} hover:bg-white/10 transition-colors cursor-pointer`,onClick:()=>f(t.id),children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(s,{className:"w-5 h-5 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-white font-medium text-sm truncate",children:t.title}),e.jsx("button",{onClick:r=>{r.stopPropagation(),j(t.id)},className:"text-white/40 hover:text-white/60 transition-colors ml-2",children:e.jsx(b,{className:"w-3 h-3"})})]}),e.jsx("p",{className:"text-white/70 text-xs mt-1 line-clamp-2",children:t.message}),e.jsx("p",{className:"text-white/50 text-xs mt-2",children:t.timestamp.toLocaleTimeString()}),t.action&&e.jsx("button",{onClick:r=>{r.stopPropagation(),t.action.onClick()},className:"text-primary-400 hover:text-primary-300 text-xs mt-2 font-medium",children:t.action.label})]})]})},t.id)})})})]})}),d&&e.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>x(!1)})]})};export{S as default};
