import{r as Pa,g as Ma}from"./react-vendor-Csw2ODfV.js";function La(t,e){for(var r=0;r<e.length;r++){const n=e[r];if(typeof n!="string"&&!Array.isArray(n)){for(const i in n)if(i!=="default"&&!(i in t)){const a=Object.getOwnPropertyDescriptor(n,i);a&&Object.defineProperty(t,i,a.get?a:{enumerable:!0,get:()=>n[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var ir=function(t,e){return ir=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},ir(t,e)};function re(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");ir(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var d=function(){return d=Object.assign||function(e){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},d.apply(this,arguments)};function X(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}function ce(t,e,r,n){function i(a){return a instanceof r?a:new r(function(o){o(a)})}return new(r||(r=Promise))(function(a,o){function s(f){try{c(n.next(f))}catch(h){o(h)}}function u(f){try{c(n.throw(f))}catch(h){o(h)}}function c(f){f.done?a(f.value):i(f.value).then(s,u)}c((n=n.apply(t,e||[])).next())})}function fe(t,e){var r={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(f){return u([c,f])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(r=0)),r;)try{if(n=1,i&&(a=c[0]&2?i.return:c[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,c[1])).done)return a;switch(i=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,i=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(a=r.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){r.label=c[1];break}if(c[0]===6&&r.label<a[1]){r.label=a[1],a=c;break}if(a&&r.label<a[2]){r.label=a[2],r.ops.push(c);break}a[2]&&r.ops.pop(),r.trys.pop();continue}c=e.call(t,r)}catch(f){c=[6,f],i=0}finally{n=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function W(t,e,r){if(r||arguments.length===2)for(var n=0,i=e.length,a;n<i;n++)(a||!(n in e))&&(a||(a=Array.prototype.slice.call(e,0,n)),a[n]=e[n]);return t.concat(a||Array.prototype.slice.call(e))}var Bt="Invariant Violation",sn=Object.setPrototypeOf,ja=sn===void 0?function(t,e){return t.__proto__=e,t}:sn,li=function(t){re(e,t);function e(r){r===void 0&&(r=Bt);var n=t.call(this,typeof r=="number"?Bt+": "+r+" (see https://github.com/apollographql/invariant-packages)":r)||this;return n.framesToPop=1,n.name=Bt,ja(n,e.prototype),n}return e}(Error);function ge(t,e){if(!t)throw new li(e)}var hi=["debug","log","warn","error","silent"],Va=hi.indexOf("log");function dt(t){return function(){if(hi.indexOf(t)>=Va){var e=console[t]||console.log;return e.apply(console,arguments)}}}(function(t){t.debug=dt("debug"),t.log=dt("log"),t.warn=dt("warn"),t.error=dt("error")})(ge||(ge={}));var Nr="3.13.8";function K(t){try{return t()}catch{}}const ar=K(function(){return globalThis})||K(function(){return window})||K(function(){return self})||K(function(){return global})||K(function(){return K.constructor("return this")()});var un=new Map;function or(t){var e=un.get(t)||1;return un.set(t,e+1),"".concat(t,":").concat(e,":").concat(Math.random().toString(36).slice(2))}function pi(t,e){e===void 0&&(e=0);var r=or("stringifyForDisplay");return JSON.stringify(t,function(n,i){return i===void 0?r:i},e).split(JSON.stringify(r)).join("<undefined>")}function vt(t){return function(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];if(typeof e=="number"){var i=e;e=Cr(i),e||(e=Rr(i,r),r=[])}t.apply(void 0,[e].concat(r))}}var w=Object.assign(function(e,r){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e||ge(e,Cr(r,n)||Rr(r,n))},{debug:vt(ge.debug),log:vt(ge.log),warn:vt(ge.warn),error:vt(ge.error)});function $(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return new li(Cr(t,e)||Rr(t,e))}var cn=Symbol.for("ApolloErrorMessageHandler_"+Nr);function di(t){if(typeof t=="string")return t;try{return pi(t,2).slice(0,1e3)}catch{return"<non-serializable>"}}function Cr(t,e){if(e===void 0&&(e=[]),!!t)return ar[cn]&&ar[cn](t,e.map(di))}function Rr(t,e){if(e===void 0&&(e=[]),!!t)return"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({version:Nr,message:t,args:e.map(di)})))}function gt(t,e){if(!!!t)throw new Error(e)}function Qa(t){return typeof t=="object"&&t!==null}function qa(t,e){if(!!!t)throw new Error("Unexpected invariant triggered.")}const Ba=/\r\n|[\n\r]/g;function sr(t,e){let r=0,n=1;for(const i of t.body.matchAll(Ba)){if(typeof i.index=="number"||qa(!1),i.index>=e)break;r=i.index+i[0].length,n+=1}return{line:n,column:e+1-r}}function Ua(t){return vi(t.source,sr(t.source,t.start))}function vi(t,e){const r=t.locationOffset.column-1,n="".padStart(r)+t.body,i=e.line-1,a=t.locationOffset.line-1,o=e.line+a,s=e.line===1?r:0,u=e.column+s,c=`${t.name}:${o}:${u}
`,f=n.split(/\r\n|[\n\r]/g),h=f[i];if(h.length>120){const l=Math.floor(u/80),p=u%80,y=[];for(let m=0;m<h.length;m+=80)y.push(h.slice(m,m+80));return c+fn([[`${o} |`,y[0]],...y.slice(1,l+1).map(m=>["|",m]),["|","^".padStart(p)],["|",y[l+1]]])}return c+fn([[`${o-1} |`,f[i-1]],[`${o} |`,h],["|","^".padStart(u)],[`${o+1} |`,f[i+1]]])}function fn(t){const e=t.filter(([n,i])=>i!==void 0),r=Math.max(...e.map(([n])=>n.length));return e.map(([n,i])=>n.padStart(r)+(i?" "+i:"")).join(`
`)}function za(t){const e=t[0];return e==null||"kind"in e||"length"in e?{nodes:e,source:t[1],positions:t[2],path:t[3],originalError:t[4],extensions:t[5]}:e}class Fr extends Error{constructor(e,...r){var n,i,a;const{nodes:o,source:s,positions:u,path:c,originalError:f,extensions:h}=za(r);super(e),this.name="GraphQLError",this.path=c??void 0,this.originalError=f??void 0,this.nodes=ln(Array.isArray(o)?o:o?[o]:void 0);const l=ln((n=this.nodes)===null||n===void 0?void 0:n.map(y=>y.loc).filter(y=>y!=null));this.source=s??(l==null||(i=l[0])===null||i===void 0?void 0:i.source),this.positions=u??l?.map(y=>y.start),this.locations=u&&s?u.map(y=>sr(s,y)):l?.map(y=>sr(y.source,y.start));const p=Qa(f?.extensions)?f?.extensions:void 0;this.extensions=(a=h??p)!==null&&a!==void 0?a:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),f!=null&&f.stack?Object.defineProperty(this,"stack",{value:f.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,Fr):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let e=this.message;if(this.nodes)for(const r of this.nodes)r.loc&&(e+=`

`+Ua(r.loc));else if(this.source&&this.locations)for(const r of this.locations)e+=`

`+vi(this.source,r);return e}toJSON(){const e={message:this.message};return this.locations!=null&&(e.locations=this.locations),this.path!=null&&(e.path=this.path),this.extensions!=null&&Object.keys(this.extensions).length>0&&(e.extensions=this.extensions),e}}function ln(t){return t===void 0||t.length===0?void 0:t}function U(t,e,r){return new Fr(`Syntax Error: ${r}`,{source:t,positions:[e]})}class Wa{constructor(e,r,n){this.start=e.start,this.end=r.end,this.startToken=e,this.endToken=r,this.source=n}get[Symbol.toStringTag](){return"Location"}toJSON(){return{start:this.start,end:this.end}}}class yi{constructor(e,r,n,i,a,o){this.kind=e,this.start=r,this.end=n,this.line=i,this.column=a,this.value=o,this.prev=null,this.next=null}get[Symbol.toStringTag](){return"Token"}toJSON(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}}const mi={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]},Ga=new Set(Object.keys(mi));function hn(t){const e=t?.kind;return typeof e=="string"&&Ga.has(e)}var Ie;(function(t){t.QUERY="query",t.MUTATION="mutation",t.SUBSCRIPTION="subscription"})(Ie||(Ie={}));var ur;(function(t){t.QUERY="QUERY",t.MUTATION="MUTATION",t.SUBSCRIPTION="SUBSCRIPTION",t.FIELD="FIELD",t.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",t.FRAGMENT_SPREAD="FRAGMENT_SPREAD",t.INLINE_FRAGMENT="INLINE_FRAGMENT",t.VARIABLE_DEFINITION="VARIABLE_DEFINITION",t.SCHEMA="SCHEMA",t.SCALAR="SCALAR",t.OBJECT="OBJECT",t.FIELD_DEFINITION="FIELD_DEFINITION",t.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",t.INTERFACE="INTERFACE",t.UNION="UNION",t.ENUM="ENUM",t.ENUM_VALUE="ENUM_VALUE",t.INPUT_OBJECT="INPUT_OBJECT",t.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION"})(ur||(ur={}));var T;(function(t){t.NAME="Name",t.DOCUMENT="Document",t.OPERATION_DEFINITION="OperationDefinition",t.VARIABLE_DEFINITION="VariableDefinition",t.SELECTION_SET="SelectionSet",t.FIELD="Field",t.ARGUMENT="Argument",t.FRAGMENT_SPREAD="FragmentSpread",t.INLINE_FRAGMENT="InlineFragment",t.FRAGMENT_DEFINITION="FragmentDefinition",t.VARIABLE="Variable",t.INT="IntValue",t.FLOAT="FloatValue",t.STRING="StringValue",t.BOOLEAN="BooleanValue",t.NULL="NullValue",t.ENUM="EnumValue",t.LIST="ListValue",t.OBJECT="ObjectValue",t.OBJECT_FIELD="ObjectField",t.DIRECTIVE="Directive",t.NAMED_TYPE="NamedType",t.LIST_TYPE="ListType",t.NON_NULL_TYPE="NonNullType",t.SCHEMA_DEFINITION="SchemaDefinition",t.OPERATION_TYPE_DEFINITION="OperationTypeDefinition",t.SCALAR_TYPE_DEFINITION="ScalarTypeDefinition",t.OBJECT_TYPE_DEFINITION="ObjectTypeDefinition",t.FIELD_DEFINITION="FieldDefinition",t.INPUT_VALUE_DEFINITION="InputValueDefinition",t.INTERFACE_TYPE_DEFINITION="InterfaceTypeDefinition",t.UNION_TYPE_DEFINITION="UnionTypeDefinition",t.ENUM_TYPE_DEFINITION="EnumTypeDefinition",t.ENUM_VALUE_DEFINITION="EnumValueDefinition",t.INPUT_OBJECT_TYPE_DEFINITION="InputObjectTypeDefinition",t.DIRECTIVE_DEFINITION="DirectiveDefinition",t.SCHEMA_EXTENSION="SchemaExtension",t.SCALAR_TYPE_EXTENSION="ScalarTypeExtension",t.OBJECT_TYPE_EXTENSION="ObjectTypeExtension",t.INTERFACE_TYPE_EXTENSION="InterfaceTypeExtension",t.UNION_TYPE_EXTENSION="UnionTypeExtension",t.ENUM_TYPE_EXTENSION="EnumTypeExtension",t.INPUT_OBJECT_TYPE_EXTENSION="InputObjectTypeExtension"})(T||(T={}));function cr(t){return t===9||t===32}function at(t){return t>=48&&t<=57}function gi(t){return t>=97&&t<=122||t>=65&&t<=90}function bi(t){return gi(t)||t===95}function $a(t){return gi(t)||at(t)||t===95}function Ya(t){var e;let r=Number.MAX_SAFE_INTEGER,n=null,i=-1;for(let o=0;o<t.length;++o){var a;const s=t[o],u=Ha(s);u!==s.length&&(n=(a=n)!==null&&a!==void 0?a:o,i=o,o!==0&&u<r&&(r=u))}return t.map((o,s)=>s===0?o:o.slice(r)).slice((e=n)!==null&&e!==void 0?e:0,i+1)}function Ha(t){let e=0;for(;e<t.length&&cr(t.charCodeAt(e));)++e;return e}function Ja(t,e){const r=t.replace(/"""/g,'\\"""'),n=r.split(/\r\n|[\n\r]/g),i=n.length===1,a=n.length>1&&n.slice(1).every(p=>p.length===0||cr(p.charCodeAt(0))),o=r.endsWith('\\"""'),s=t.endsWith('"')&&!o,u=t.endsWith("\\"),c=s||u,f=!i||t.length>70||c||a||o;let h="";const l=i&&cr(t.charCodeAt(0));return(f&&!l||a)&&(h+=`
`),h+=r,(f||c)&&(h+=`
`),'"""'+h+'"""'}var _;(function(t){t.SOF="<SOF>",t.EOF="<EOF>",t.BANG="!",t.DOLLAR="$",t.AMP="&",t.PAREN_L="(",t.PAREN_R=")",t.SPREAD="...",t.COLON=":",t.EQUALS="=",t.AT="@",t.BRACKET_L="[",t.BRACKET_R="]",t.BRACE_L="{",t.PIPE="|",t.BRACE_R="}",t.NAME="Name",t.INT="Int",t.FLOAT="Float",t.STRING="String",t.BLOCK_STRING="BlockString",t.COMMENT="Comment"})(_||(_={}));class Ka{constructor(e){const r=new yi(_.SOF,0,0,0,0);this.source=e,this.lastToken=r,this.token=r,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){return this.lastToken=this.token,this.token=this.lookahead()}lookahead(){let e=this.token;if(e.kind!==_.EOF)do if(e.next)e=e.next;else{const r=Za(this,e.end);e.next=r,r.prev=e,e=r}while(e.kind===_.COMMENT);return e}}function Xa(t){return t===_.BANG||t===_.DOLLAR||t===_.AMP||t===_.PAREN_L||t===_.PAREN_R||t===_.SPREAD||t===_.COLON||t===_.EQUALS||t===_.AT||t===_.BRACKET_L||t===_.BRACKET_R||t===_.BRACE_L||t===_.PIPE||t===_.BRACE_R}function qe(t){return t>=0&&t<=55295||t>=57344&&t<=1114111}function Ct(t,e){return Ei(t.charCodeAt(e))&&_i(t.charCodeAt(e+1))}function Ei(t){return t>=55296&&t<=56319}function _i(t){return t>=56320&&t<=57343}function _e(t,e){const r=t.source.body.codePointAt(e);if(r===void 0)return _.EOF;if(r>=32&&r<=126){const n=String.fromCodePoint(r);return n==='"'?`'"'`:`"${n}"`}return"U+"+r.toString(16).toUpperCase().padStart(4,"0")}function q(t,e,r,n,i){const a=t.line,o=1+r-t.lineStart;return new yi(e,r,n,a,o,i)}function Za(t,e){const r=t.source.body,n=r.length;let i=e;for(;i<n;){const a=r.charCodeAt(i);switch(a){case 65279:case 9:case 32:case 44:++i;continue;case 10:++i,++t.line,t.lineStart=i;continue;case 13:r.charCodeAt(i+1)===10?i+=2:++i,++t.line,t.lineStart=i;continue;case 35:return eo(t,i);case 33:return q(t,_.BANG,i,i+1);case 36:return q(t,_.DOLLAR,i,i+1);case 38:return q(t,_.AMP,i,i+1);case 40:return q(t,_.PAREN_L,i,i+1);case 41:return q(t,_.PAREN_R,i,i+1);case 46:if(r.charCodeAt(i+1)===46&&r.charCodeAt(i+2)===46)return q(t,_.SPREAD,i,i+3);break;case 58:return q(t,_.COLON,i,i+1);case 61:return q(t,_.EQUALS,i,i+1);case 64:return q(t,_.AT,i,i+1);case 91:return q(t,_.BRACKET_L,i,i+1);case 93:return q(t,_.BRACKET_R,i,i+1);case 123:return q(t,_.BRACE_L,i,i+1);case 124:return q(t,_.PIPE,i,i+1);case 125:return q(t,_.BRACE_R,i,i+1);case 34:return r.charCodeAt(i+1)===34&&r.charCodeAt(i+2)===34?oo(t,i):ro(t,i)}if(at(a)||a===45)return to(t,i,a);if(bi(a))return so(t,i);throw U(t.source,i,a===39?`Unexpected single quote character ('), did you mean to use a double quote (")?`:qe(a)||Ct(r,i)?`Unexpected character: ${_e(t,i)}.`:`Invalid character: ${_e(t,i)}.`)}return q(t,_.EOF,n,n)}function eo(t,e){const r=t.source.body,n=r.length;let i=e+1;for(;i<n;){const a=r.charCodeAt(i);if(a===10||a===13)break;if(qe(a))++i;else if(Ct(r,i))i+=2;else break}return q(t,_.COMMENT,e,i,r.slice(e+1,i))}function to(t,e,r){const n=t.source.body;let i=e,a=r,o=!1;if(a===45&&(a=n.charCodeAt(++i)),a===48){if(a=n.charCodeAt(++i),at(a))throw U(t.source,i,`Invalid number, unexpected digit after 0: ${_e(t,i)}.`)}else i=Ut(t,i,a),a=n.charCodeAt(i);if(a===46&&(o=!0,a=n.charCodeAt(++i),i=Ut(t,i,a),a=n.charCodeAt(i)),(a===69||a===101)&&(o=!0,a=n.charCodeAt(++i),(a===43||a===45)&&(a=n.charCodeAt(++i)),i=Ut(t,i,a),a=n.charCodeAt(i)),a===46||bi(a))throw U(t.source,i,`Invalid number, expected digit but got: ${_e(t,i)}.`);return q(t,o?_.FLOAT:_.INT,e,i,n.slice(e,i))}function Ut(t,e,r){if(!at(r))throw U(t.source,e,`Invalid number, expected digit but got: ${_e(t,e)}.`);const n=t.source.body;let i=e+1;for(;at(n.charCodeAt(i));)++i;return i}function ro(t,e){const r=t.source.body,n=r.length;let i=e+1,a=i,o="";for(;i<n;){const s=r.charCodeAt(i);if(s===34)return o+=r.slice(a,i),q(t,_.STRING,e,i+1,o);if(s===92){o+=r.slice(a,i);const u=r.charCodeAt(i+1)===117?r.charCodeAt(i+2)===123?no(t,i):io(t,i):ao(t,i);o+=u.value,i+=u.size,a=i;continue}if(s===10||s===13)break;if(qe(s))++i;else if(Ct(r,i))i+=2;else throw U(t.source,i,`Invalid character within String: ${_e(t,i)}.`)}throw U(t.source,i,"Unterminated string.")}function no(t,e){const r=t.source.body;let n=0,i=3;for(;i<12;){const a=r.charCodeAt(e+i++);if(a===125){if(i<5||!qe(n))break;return{value:String.fromCodePoint(n),size:i}}if(n=n<<4|Ke(a),n<0)break}throw U(t.source,e,`Invalid Unicode escape sequence: "${r.slice(e,e+i)}".`)}function io(t,e){const r=t.source.body,n=pn(r,e+2);if(qe(n))return{value:String.fromCodePoint(n),size:6};if(Ei(n)&&r.charCodeAt(e+6)===92&&r.charCodeAt(e+7)===117){const i=pn(r,e+8);if(_i(i))return{value:String.fromCodePoint(n,i),size:12}}throw U(t.source,e,`Invalid Unicode escape sequence: "${r.slice(e,e+6)}".`)}function pn(t,e){return Ke(t.charCodeAt(e))<<12|Ke(t.charCodeAt(e+1))<<8|Ke(t.charCodeAt(e+2))<<4|Ke(t.charCodeAt(e+3))}function Ke(t){return t>=48&&t<=57?t-48:t>=65&&t<=70?t-55:t>=97&&t<=102?t-87:-1}function ao(t,e){const r=t.source.body;switch(r.charCodeAt(e+1)){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:`
`,size:2};case 114:return{value:"\r",size:2};case 116:return{value:"	",size:2}}throw U(t.source,e,`Invalid character escape sequence: "${r.slice(e,e+2)}".`)}function oo(t,e){const r=t.source.body,n=r.length;let i=t.lineStart,a=e+3,o=a,s="";const u=[];for(;a<n;){const c=r.charCodeAt(a);if(c===34&&r.charCodeAt(a+1)===34&&r.charCodeAt(a+2)===34){s+=r.slice(o,a),u.push(s);const f=q(t,_.BLOCK_STRING,e,a+3,Ya(u).join(`
`));return t.line+=u.length-1,t.lineStart=i,f}if(c===92&&r.charCodeAt(a+1)===34&&r.charCodeAt(a+2)===34&&r.charCodeAt(a+3)===34){s+=r.slice(o,a),o=a+1,a+=4;continue}if(c===10||c===13){s+=r.slice(o,a),u.push(s),c===13&&r.charCodeAt(a+1)===10?a+=2:++a,s="",o=a,i=a;continue}if(qe(c))++a;else if(Ct(r,a))a+=2;else throw U(t.source,a,`Invalid character within String: ${_e(t,a)}.`)}throw U(t.source,a,"Unterminated string.")}function so(t,e){const r=t.source.body,n=r.length;let i=e+1;for(;i<n;){const a=r.charCodeAt(i);if($a(a))++i;else break}return q(t,_.NAME,e,i,r.slice(e,i))}const uo=10,Oi=2;function Ar(t){return Rt(t,[])}function Rt(t,e){switch(typeof t){case"string":return JSON.stringify(t);case"function":return t.name?`[function ${t.name}]`:"[function]";case"object":return co(t,e);default:return String(t)}}function co(t,e){if(t===null)return"null";if(e.includes(t))return"[Circular]";const r=[...e,t];if(fo(t)){const n=t.toJSON();if(n!==t)return typeof n=="string"?n:Rt(n,r)}else if(Array.isArray(t))return ho(t,r);return lo(t,r)}function fo(t){return typeof t.toJSON=="function"}function lo(t,e){const r=Object.entries(t);return r.length===0?"{}":e.length>Oi?"["+po(t)+"]":"{ "+r.map(([i,a])=>i+": "+Rt(a,e)).join(", ")+" }"}function ho(t,e){if(t.length===0)return"[]";if(e.length>Oi)return"[Array]";const r=Math.min(uo,t.length),n=t.length-r,i=[];for(let a=0;a<r;++a)i.push(Rt(t[a],e));return n===1?i.push("... 1 more item"):n>1&&i.push(`... ${n} more items`),"["+i.join(", ")+"]"}function po(t){const e=Object.prototype.toString.call(t).replace(/^\[object /,"").replace(/]$/,"");if(e==="Object"&&typeof t.constructor=="function"){const r=t.constructor.name;if(typeof r=="string"&&r!=="")return r}return e}const vo=globalThis.process&&!0,yo=vo?function(e,r){return e instanceof r}:function(e,r){if(e instanceof r)return!0;if(typeof e=="object"&&e!==null){var n;const i=r.prototype[Symbol.toStringTag],a=Symbol.toStringTag in e?e[Symbol.toStringTag]:(n=e.constructor)===null||n===void 0?void 0:n.name;if(i===a){const o=Ar(e);throw new Error(`Cannot use ${i} "${o}" from another module or realm.

Ensure that there is only one instance of "graphql" in the node_modules
directory. If different versions of "graphql" are the dependencies of other
relied on modules, use "resolutions" to ensure only one version is installed.

https://yarnpkg.com/en/docs/selective-version-resolutions

Duplicate "graphql" modules cannot be used at the same time since different
versions may have different capabilities and behavior. The data from one
version used in the function from another could produce confusing and
spurious results.`)}}return!1};class Si{constructor(e,r="GraphQL request",n={line:1,column:1}){typeof e=="string"||gt(!1,`Body must be a string. Received: ${Ar(e)}.`),this.body=e,this.name=r,this.locationOffset=n,this.locationOffset.line>0||gt(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||gt(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}}function mo(t){return yo(t,Si)}function go(t,e){const r=new bo(t,e),n=r.parseDocument();return Object.defineProperty(n,"tokenCount",{enumerable:!1,value:r.tokenCount}),n}class bo{constructor(e,r={}){const n=mo(e)?e:new Si(e);this._lexer=new Ka(n),this._options=r,this._tokenCounter=0}get tokenCount(){return this._tokenCounter}parseName(){const e=this.expectToken(_.NAME);return this.node(e,{kind:T.NAME,value:e.value})}parseDocument(){return this.node(this._lexer.token,{kind:T.DOCUMENT,definitions:this.many(_.SOF,this.parseDefinition,_.EOF)})}parseDefinition(){if(this.peek(_.BRACE_L))return this.parseOperationDefinition();const e=this.peekDescription(),r=e?this._lexer.lookahead():this._lexer.token;if(r.kind===_.NAME){switch(r.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(e)throw U(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(r.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(r)}parseOperationDefinition(){const e=this._lexer.token;if(this.peek(_.BRACE_L))return this.node(e,{kind:T.OPERATION_DEFINITION,operation:Ie.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});const r=this.parseOperationType();let n;return this.peek(_.NAME)&&(n=this.parseName()),this.node(e,{kind:T.OPERATION_DEFINITION,operation:r,name:n,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){const e=this.expectToken(_.NAME);switch(e.value){case"query":return Ie.QUERY;case"mutation":return Ie.MUTATION;case"subscription":return Ie.SUBSCRIPTION}throw this.unexpected(e)}parseVariableDefinitions(){return this.optionalMany(_.PAREN_L,this.parseVariableDefinition,_.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:T.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(_.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(_.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){const e=this._lexer.token;return this.expectToken(_.DOLLAR),this.node(e,{kind:T.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:T.SELECTION_SET,selections:this.many(_.BRACE_L,this.parseSelection,_.BRACE_R)})}parseSelection(){return this.peek(_.SPREAD)?this.parseFragment():this.parseField()}parseField(){const e=this._lexer.token,r=this.parseName();let n,i;return this.expectOptionalToken(_.COLON)?(n=r,i=this.parseName()):i=r,this.node(e,{kind:T.FIELD,alias:n,name:i,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(_.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(e){const r=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(_.PAREN_L,r,_.PAREN_R)}parseArgument(e=!1){const r=this._lexer.token,n=this.parseName();return this.expectToken(_.COLON),this.node(r,{kind:T.ARGUMENT,name:n,value:this.parseValueLiteral(e)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){const e=this._lexer.token;this.expectToken(_.SPREAD);const r=this.expectOptionalKeyword("on");return!r&&this.peek(_.NAME)?this.node(e,{kind:T.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(e,{kind:T.INLINE_FRAGMENT,typeCondition:r?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){const e=this._lexer.token;return this.expectKeyword("fragment"),this._options.allowLegacyFragmentVariables===!0?this.node(e,{kind:T.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(e,{kind:T.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if(this._lexer.token.value==="on")throw this.unexpected();return this.parseName()}parseValueLiteral(e){const r=this._lexer.token;switch(r.kind){case _.BRACKET_L:return this.parseList(e);case _.BRACE_L:return this.parseObject(e);case _.INT:return this.advanceLexer(),this.node(r,{kind:T.INT,value:r.value});case _.FLOAT:return this.advanceLexer(),this.node(r,{kind:T.FLOAT,value:r.value});case _.STRING:case _.BLOCK_STRING:return this.parseStringLiteral();case _.NAME:switch(this.advanceLexer(),r.value){case"true":return this.node(r,{kind:T.BOOLEAN,value:!0});case"false":return this.node(r,{kind:T.BOOLEAN,value:!1});case"null":return this.node(r,{kind:T.NULL});default:return this.node(r,{kind:T.ENUM,value:r.value})}case _.DOLLAR:if(e)if(this.expectToken(_.DOLLAR),this._lexer.token.kind===_.NAME){const n=this._lexer.token.value;throw U(this._lexer.source,r.start,`Unexpected variable "$${n}" in constant value.`)}else throw this.unexpected(r);return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){const e=this._lexer.token;return this.advanceLexer(),this.node(e,{kind:T.STRING,value:e.value,block:e.kind===_.BLOCK_STRING})}parseList(e){const r=()=>this.parseValueLiteral(e);return this.node(this._lexer.token,{kind:T.LIST,values:this.any(_.BRACKET_L,r,_.BRACKET_R)})}parseObject(e){const r=()=>this.parseObjectField(e);return this.node(this._lexer.token,{kind:T.OBJECT,fields:this.any(_.BRACE_L,r,_.BRACE_R)})}parseObjectField(e){const r=this._lexer.token,n=this.parseName();return this.expectToken(_.COLON),this.node(r,{kind:T.OBJECT_FIELD,name:n,value:this.parseValueLiteral(e)})}parseDirectives(e){const r=[];for(;this.peek(_.AT);)r.push(this.parseDirective(e));return r}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(e){const r=this._lexer.token;return this.expectToken(_.AT),this.node(r,{kind:T.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e)})}parseTypeReference(){const e=this._lexer.token;let r;if(this.expectOptionalToken(_.BRACKET_L)){const n=this.parseTypeReference();this.expectToken(_.BRACKET_R),r=this.node(e,{kind:T.LIST_TYPE,type:n})}else r=this.parseNamedType();return this.expectOptionalToken(_.BANG)?this.node(e,{kind:T.NON_NULL_TYPE,type:r}):r}parseNamedType(){return this.node(this._lexer.token,{kind:T.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(_.STRING)||this.peek(_.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("schema");const n=this.parseConstDirectives(),i=this.many(_.BRACE_L,this.parseOperationTypeDefinition,_.BRACE_R);return this.node(e,{kind:T.SCHEMA_DEFINITION,description:r,directives:n,operationTypes:i})}parseOperationTypeDefinition(){const e=this._lexer.token,r=this.parseOperationType();this.expectToken(_.COLON);const n=this.parseNamedType();return this.node(e,{kind:T.OPERATION_TYPE_DEFINITION,operation:r,type:n})}parseScalarTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("scalar");const n=this.parseName(),i=this.parseConstDirectives();return this.node(e,{kind:T.SCALAR_TYPE_DEFINITION,description:r,name:n,directives:i})}parseObjectTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("type");const n=this.parseName(),i=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(e,{kind:T.OBJECT_TYPE_DEFINITION,description:r,name:n,interfaces:i,directives:a,fields:o})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(_.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(_.BRACE_L,this.parseFieldDefinition,_.BRACE_R)}parseFieldDefinition(){const e=this._lexer.token,r=this.parseDescription(),n=this.parseName(),i=this.parseArgumentDefs();this.expectToken(_.COLON);const a=this.parseTypeReference(),o=this.parseConstDirectives();return this.node(e,{kind:T.FIELD_DEFINITION,description:r,name:n,arguments:i,type:a,directives:o})}parseArgumentDefs(){return this.optionalMany(_.PAREN_L,this.parseInputValueDef,_.PAREN_R)}parseInputValueDef(){const e=this._lexer.token,r=this.parseDescription(),n=this.parseName();this.expectToken(_.COLON);const i=this.parseTypeReference();let a;this.expectOptionalToken(_.EQUALS)&&(a=this.parseConstValueLiteral());const o=this.parseConstDirectives();return this.node(e,{kind:T.INPUT_VALUE_DEFINITION,description:r,name:n,type:i,defaultValue:a,directives:o})}parseInterfaceTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("interface");const n=this.parseName(),i=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(e,{kind:T.INTERFACE_TYPE_DEFINITION,description:r,name:n,interfaces:i,directives:a,fields:o})}parseUnionTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("union");const n=this.parseName(),i=this.parseConstDirectives(),a=this.parseUnionMemberTypes();return this.node(e,{kind:T.UNION_TYPE_DEFINITION,description:r,name:n,directives:i,types:a})}parseUnionMemberTypes(){return this.expectOptionalToken(_.EQUALS)?this.delimitedMany(_.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("enum");const n=this.parseName(),i=this.parseConstDirectives(),a=this.parseEnumValuesDefinition();return this.node(e,{kind:T.ENUM_TYPE_DEFINITION,description:r,name:n,directives:i,values:a})}parseEnumValuesDefinition(){return this.optionalMany(_.BRACE_L,this.parseEnumValueDefinition,_.BRACE_R)}parseEnumValueDefinition(){const e=this._lexer.token,r=this.parseDescription(),n=this.parseEnumValueName(),i=this.parseConstDirectives();return this.node(e,{kind:T.ENUM_VALUE_DEFINITION,description:r,name:n,directives:i})}parseEnumValueName(){if(this._lexer.token.value==="true"||this._lexer.token.value==="false"||this._lexer.token.value==="null")throw U(this._lexer.source,this._lexer.token.start,`${yt(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("input");const n=this.parseName(),i=this.parseConstDirectives(),a=this.parseInputFieldsDefinition();return this.node(e,{kind:T.INPUT_OBJECT_TYPE_DEFINITION,description:r,name:n,directives:i,fields:a})}parseInputFieldsDefinition(){return this.optionalMany(_.BRACE_L,this.parseInputValueDef,_.BRACE_R)}parseTypeSystemExtension(){const e=this._lexer.lookahead();if(e.kind===_.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)}parseSchemaExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");const r=this.parseConstDirectives(),n=this.optionalMany(_.BRACE_L,this.parseOperationTypeDefinition,_.BRACE_R);if(r.length===0&&n.length===0)throw this.unexpected();return this.node(e,{kind:T.SCHEMA_EXTENSION,directives:r,operationTypes:n})}parseScalarTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");const r=this.parseName(),n=this.parseConstDirectives();if(n.length===0)throw this.unexpected();return this.node(e,{kind:T.SCALAR_TYPE_EXTENSION,name:r,directives:n})}parseObjectTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");const r=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(n.length===0&&i.length===0&&a.length===0)throw this.unexpected();return this.node(e,{kind:T.OBJECT_TYPE_EXTENSION,name:r,interfaces:n,directives:i,fields:a})}parseInterfaceTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");const r=this.parseName(),n=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(n.length===0&&i.length===0&&a.length===0)throw this.unexpected();return this.node(e,{kind:T.INTERFACE_TYPE_EXTENSION,name:r,interfaces:n,directives:i,fields:a})}parseUnionTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");const r=this.parseName(),n=this.parseConstDirectives(),i=this.parseUnionMemberTypes();if(n.length===0&&i.length===0)throw this.unexpected();return this.node(e,{kind:T.UNION_TYPE_EXTENSION,name:r,directives:n,types:i})}parseEnumTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");const r=this.parseName(),n=this.parseConstDirectives(),i=this.parseEnumValuesDefinition();if(n.length===0&&i.length===0)throw this.unexpected();return this.node(e,{kind:T.ENUM_TYPE_EXTENSION,name:r,directives:n,values:i})}parseInputObjectTypeExtension(){const e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");const r=this.parseName(),n=this.parseConstDirectives(),i=this.parseInputFieldsDefinition();if(n.length===0&&i.length===0)throw this.unexpected();return this.node(e,{kind:T.INPUT_OBJECT_TYPE_EXTENSION,name:r,directives:n,fields:i})}parseDirectiveDefinition(){const e=this._lexer.token,r=this.parseDescription();this.expectKeyword("directive"),this.expectToken(_.AT);const n=this.parseName(),i=this.parseArgumentDefs(),a=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");const o=this.parseDirectiveLocations();return this.node(e,{kind:T.DIRECTIVE_DEFINITION,description:r,name:n,arguments:i,repeatable:a,locations:o})}parseDirectiveLocations(){return this.delimitedMany(_.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){const e=this._lexer.token,r=this.parseName();if(Object.prototype.hasOwnProperty.call(ur,r.value))return r;throw this.unexpected(e)}node(e,r){return this._options.noLocation!==!0&&(r.loc=new Wa(e,this._lexer.lastToken,this._lexer.source)),r}peek(e){return this._lexer.token.kind===e}expectToken(e){const r=this._lexer.token;if(r.kind===e)return this.advanceLexer(),r;throw U(this._lexer.source,r.start,`Expected ${Ti(e)}, found ${yt(r)}.`)}expectOptionalToken(e){return this._lexer.token.kind===e?(this.advanceLexer(),!0):!1}expectKeyword(e){const r=this._lexer.token;if(r.kind===_.NAME&&r.value===e)this.advanceLexer();else throw U(this._lexer.source,r.start,`Expected "${e}", found ${yt(r)}.`)}expectOptionalKeyword(e){const r=this._lexer.token;return r.kind===_.NAME&&r.value===e?(this.advanceLexer(),!0):!1}unexpected(e){const r=e??this._lexer.token;return U(this._lexer.source,r.start,`Unexpected ${yt(r)}.`)}any(e,r,n){this.expectToken(e);const i=[];for(;!this.expectOptionalToken(n);)i.push(r.call(this));return i}optionalMany(e,r,n){if(this.expectOptionalToken(e)){const i=[];do i.push(r.call(this));while(!this.expectOptionalToken(n));return i}return[]}many(e,r,n){this.expectToken(e);const i=[];do i.push(r.call(this));while(!this.expectOptionalToken(n));return i}delimitedMany(e,r){this.expectOptionalToken(e);const n=[];do n.push(r.call(this));while(this.expectOptionalToken(e));return n}advanceLexer(){const{maxTokens:e}=this._options,r=this._lexer.advance();if(r.kind!==_.EOF&&(++this._tokenCounter,e!==void 0&&this._tokenCounter>e))throw U(this._lexer.source,r.start,`Document contains more that ${e} tokens. Parsing aborted.`)}}function yt(t){const e=t.value;return Ti(t.kind)+(e!=null?` "${e}"`:"")}function Ti(t){return Xa(t)?`"${t}"`:t}function Eo(t){return`"${t.replace(_o,Oo)}"`}const _o=/[\x00-\x1f\x22\x5c\x7f-\x9f]/g;function Oo(t){return So[t.charCodeAt(0)]}const So=["\\u0000","\\u0001","\\u0002","\\u0003","\\u0004","\\u0005","\\u0006","\\u0007","\\b","\\t","\\n","\\u000B","\\f","\\r","\\u000E","\\u000F","\\u0010","\\u0011","\\u0012","\\u0013","\\u0014","\\u0015","\\u0016","\\u0017","\\u0018","\\u0019","\\u001A","\\u001B","\\u001C","\\u001D","\\u001E","\\u001F","","",'\\"',"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\\\","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\u007F","\\u0080","\\u0081","\\u0082","\\u0083","\\u0084","\\u0085","\\u0086","\\u0087","\\u0088","\\u0089","\\u008A","\\u008B","\\u008C","\\u008D","\\u008E","\\u008F","\\u0090","\\u0091","\\u0092","\\u0093","\\u0094","\\u0095","\\u0096","\\u0097","\\u0098","\\u0099","\\u009A","\\u009B","\\u009C","\\u009D","\\u009E","\\u009F"],Ft=Object.freeze({});function Z(t,e,r=mi){const n=new Map;for(const v of Object.values(T))n.set(v,To(e,v));let i,a=Array.isArray(t),o=[t],s=-1,u=[],c=t,f,h;const l=[],p=[];do{s++;const v=s===o.length,b=v&&u.length!==0;if(v){if(f=p.length===0?void 0:l[l.length-1],c=h,h=p.pop(),b)if(a){c=c.slice();let O=0;for(const[S,k]of u){const x=S-O;k===null?(c.splice(x,1),O++):c[x]=k}}else{c={...c};for(const[O,S]of u)c[O]=S}s=i.index,o=i.keys,u=i.edits,a=i.inArray,i=i.prev}else if(h){if(f=a?s:o[s],c=h[f],c==null)continue;l.push(f)}let E;if(!Array.isArray(c)){var y,m;hn(c)||gt(!1,`Invalid AST Node: ${Ar(c)}.`);const O=v?(y=n.get(c.kind))===null||y===void 0?void 0:y.leave:(m=n.get(c.kind))===null||m===void 0?void 0:m.enter;if(E=O?.call(e,c,f,h,l,p),E===Ft)break;if(E===!1){if(!v){l.pop();continue}}else if(E!==void 0&&(u.push([f,E]),!v))if(hn(E))c=E;else{l.pop();continue}}if(E===void 0&&b&&u.push([f,c]),v)l.pop();else{var g;i={inArray:a,index:s,keys:o,edits:u,prev:i},a=Array.isArray(c),o=a?c:(g=r[c.kind])!==null&&g!==void 0?g:[],s=-1,u=[],h&&p.push(h),h=c}}while(i!==void 0);return u.length!==0?u[u.length-1][1]:t}function To(t,e){const r=t[e];return typeof r=="object"?r:typeof r=="function"?{enter:r,leave:void 0}:{enter:t.enter,leave:t.leave}}function wo(t){return Z(t,Do)}const ko=80,Do={Name:{leave:t=>t.value},Variable:{leave:t=>"$"+t.name},Document:{leave:t=>D(t.definitions,`

`)},OperationDefinition:{leave(t){const e=N("(",D(t.variableDefinitions,", "),")"),r=D([t.operation,D([t.name,e]),D(t.directives," ")]," ");return(r==="query"?"":r+" ")+t.selectionSet}},VariableDefinition:{leave:({variable:t,type:e,defaultValue:r,directives:n})=>t+": "+e+N(" = ",r)+N(" ",D(n," "))},SelectionSet:{leave:({selections:t})=>J(t)},Field:{leave({alias:t,name:e,arguments:r,directives:n,selectionSet:i}){const a=N("",t,": ")+e;let o=a+N("(",D(r,", "),")");return o.length>ko&&(o=a+N(`(
`,bt(D(r,`
`)),`
)`)),D([o,D(n," "),i]," ")}},Argument:{leave:({name:t,value:e})=>t+": "+e},FragmentSpread:{leave:({name:t,directives:e})=>"..."+t+N(" ",D(e," "))},InlineFragment:{leave:({typeCondition:t,directives:e,selectionSet:r})=>D(["...",N("on ",t),D(e," "),r]," ")},FragmentDefinition:{leave:({name:t,typeCondition:e,variableDefinitions:r,directives:n,selectionSet:i})=>`fragment ${t}${N("(",D(r,", "),")")} on ${e} ${N("",D(n," ")," ")}`+i},IntValue:{leave:({value:t})=>t},FloatValue:{leave:({value:t})=>t},StringValue:{leave:({value:t,block:e})=>e?Ja(t):Eo(t)},BooleanValue:{leave:({value:t})=>t?"true":"false"},NullValue:{leave:()=>"null"},EnumValue:{leave:({value:t})=>t},ListValue:{leave:({values:t})=>"["+D(t,", ")+"]"},ObjectValue:{leave:({fields:t})=>"{"+D(t,", ")+"}"},ObjectField:{leave:({name:t,value:e})=>t+": "+e},Directive:{leave:({name:t,arguments:e})=>"@"+t+N("(",D(e,", "),")")},NamedType:{leave:({name:t})=>t},ListType:{leave:({type:t})=>"["+t+"]"},NonNullType:{leave:({type:t})=>t+"!"},SchemaDefinition:{leave:({description:t,directives:e,operationTypes:r})=>N("",t,`
`)+D(["schema",D(e," "),J(r)]," ")},OperationTypeDefinition:{leave:({operation:t,type:e})=>t+": "+e},ScalarTypeDefinition:{leave:({description:t,name:e,directives:r})=>N("",t,`
`)+D(["scalar",e,D(r," ")]," ")},ObjectTypeDefinition:{leave:({description:t,name:e,interfaces:r,directives:n,fields:i})=>N("",t,`
`)+D(["type",e,N("implements ",D(r," & ")),D(n," "),J(i)]," ")},FieldDefinition:{leave:({description:t,name:e,arguments:r,type:n,directives:i})=>N("",t,`
`)+e+(dn(r)?N(`(
`,bt(D(r,`
`)),`
)`):N("(",D(r,", "),")"))+": "+n+N(" ",D(i," "))},InputValueDefinition:{leave:({description:t,name:e,type:r,defaultValue:n,directives:i})=>N("",t,`
`)+D([e+": "+r,N("= ",n),D(i," ")]," ")},InterfaceTypeDefinition:{leave:({description:t,name:e,interfaces:r,directives:n,fields:i})=>N("",t,`
`)+D(["interface",e,N("implements ",D(r," & ")),D(n," "),J(i)]," ")},UnionTypeDefinition:{leave:({description:t,name:e,directives:r,types:n})=>N("",t,`
`)+D(["union",e,D(r," "),N("= ",D(n," | "))]," ")},EnumTypeDefinition:{leave:({description:t,name:e,directives:r,values:n})=>N("",t,`
`)+D(["enum",e,D(r," "),J(n)]," ")},EnumValueDefinition:{leave:({description:t,name:e,directives:r})=>N("",t,`
`)+D([e,D(r," ")]," ")},InputObjectTypeDefinition:{leave:({description:t,name:e,directives:r,fields:n})=>N("",t,`
`)+D(["input",e,D(r," "),J(n)]," ")},DirectiveDefinition:{leave:({description:t,name:e,arguments:r,repeatable:n,locations:i})=>N("",t,`
`)+"directive @"+e+(dn(r)?N(`(
`,bt(D(r,`
`)),`
)`):N("(",D(r,", "),")"))+(n?" repeatable":"")+" on "+D(i," | ")},SchemaExtension:{leave:({directives:t,operationTypes:e})=>D(["extend schema",D(t," "),J(e)]," ")},ScalarTypeExtension:{leave:({name:t,directives:e})=>D(["extend scalar",t,D(e," ")]," ")},ObjectTypeExtension:{leave:({name:t,interfaces:e,directives:r,fields:n})=>D(["extend type",t,N("implements ",D(e," & ")),D(r," "),J(n)]," ")},InterfaceTypeExtension:{leave:({name:t,interfaces:e,directives:r,fields:n})=>D(["extend interface",t,N("implements ",D(e," & ")),D(r," "),J(n)]," ")},UnionTypeExtension:{leave:({name:t,directives:e,types:r})=>D(["extend union",t,D(e," "),N("= ",D(r," | "))]," ")},EnumTypeExtension:{leave:({name:t,directives:e,values:r})=>D(["extend enum",t,D(e," "),J(r)]," ")},InputObjectTypeExtension:{leave:({name:t,directives:e,fields:r})=>D(["extend input",t,D(e," "),J(r)]," ")}};function D(t,e=""){var r;return(r=t?.filter(n=>n).join(e))!==null&&r!==void 0?r:""}function J(t){return N(`{
`,bt(D(t,`
`)),`
}`)}function N(t,e,r=""){return e!=null&&e!==""?t+e+r:""}function bt(t){return N("  ",t.replace(/\n/g,`
  `))}function dn(t){var e;return(e=t?.some(r=>r.includes(`
`)))!==null&&e!==void 0?e:!1}function vn(t){return t.kind===T.FIELD||t.kind===T.FRAGMENT_SPREAD||t.kind===T.INLINE_FRAGMENT}function lt(t,e){var r=t.directives;return!r||!r.length?!0:No(r).every(function(n){var i=n.directive,a=n.ifArgument,o=!1;return a.value.kind==="Variable"?(o=e&&e[a.value.name.value],w(o!==void 0,78,i.name.value)):o=a.value.value,i.name.value==="skip"?!o:o})}function ot(t,e,r){var n=new Set(t),i=n.size;return Z(e,{Directive:function(a){if(n.delete(a.name.value)&&(!r||!n.size))return Ft}}),r?!n.size:n.size<i}function Io(t){return t&&ot(["client","export"],t,!0)}function xo(t){var e=t.name.value;return e==="skip"||e==="include"}function No(t){var e=[];return t&&t.length&&t.forEach(function(r){if(xo(r)){var n=r.arguments,i=r.name.value;w(n&&n.length===1,79,i);var a=n[0];w(a.name&&a.name.value==="if",80,i);var o=a.value;w(o&&(o.kind==="Variable"||o.kind==="BooleanValue"),81,i),e.push({directive:r,ifArgument:a})}}),e}function Co(t){var e,r,n=(e=t.directives)===null||e===void 0?void 0:e.find(function(a){var o=a.name;return o.value==="unmask"});if(!n)return"mask";var i=(r=n.arguments)===null||r===void 0?void 0:r.find(function(a){var o=a.name;return o.value==="mode"});return globalThis.__DEV__!==!1&&i&&(i.value.kind===T.VARIABLE?globalThis.__DEV__!==!1&&w.warn(82):i.value.kind!==T.STRING?globalThis.__DEV__!==!1&&w.warn(83):i.value.value!=="migrate"&&globalThis.__DEV__!==!1&&w.warn(84,i.value.value)),i&&"value"in i.value&&i.value.value==="migrate"?"migrate":"unmask"}const Ro=()=>Object.create(null),{forEach:Fo,slice:yn}=Array.prototype,{hasOwnProperty:Ao}=Object.prototype;class oe{constructor(e=!0,r=Ro){this.weakness=e,this.makeData=r}lookup(){return this.lookupArray(arguments)}lookupArray(e){let r=this;return Fo.call(e,n=>r=r.getChildTrie(n)),Ao.call(r,"data")?r.data:r.data=this.makeData(yn.call(e))}peek(){return this.peekArray(arguments)}peekArray(e){let r=this;for(let n=0,i=e.length;r&&n<i;++n){const a=r.mapFor(e[n],!1);r=a&&a.get(e[n])}return r&&r.data}remove(){return this.removeArray(arguments)}removeArray(e){let r;if(e.length){const n=e[0],i=this.mapFor(n,!1),a=i&&i.get(n);a&&(r=a.removeArray(yn.call(e,1)),!a.data&&!a.weak&&!(a.strong&&a.strong.size)&&i.delete(n))}else r=this.data,delete this.data;return r}getChildTrie(e){const r=this.mapFor(e,!0);let n=r.get(e);return n||r.set(e,n=new oe(this.weakness,this.makeData)),n}mapFor(e,r){return this.weakness&&Po(e)?this.weak||(r?this.weak=new WeakMap:void 0):this.strong||(r?this.strong=new Map:void 0)}}function Po(t){switch(typeof t){case"object":if(t===null)break;case"function":return!0}return!1}var wi=K(function(){return navigator.product})=="ReactNative",Te=typeof WeakMap=="function"&&!(wi&&!global.HermesInternal),Pr=typeof WeakSet=="function",Mr=typeof Symbol=="function"&&typeof Symbol.for=="function",At=Mr&&Symbol.asyncIterator,ki=typeof K(function(){return window.document.createElement})=="function",Mo=K(function(){return navigator.userAgent.indexOf("jsdom")>=0})||!1,Lo=(ki||wi)&&!Mo;function j(t){return t!==null&&typeof t=="object"}function jo(t,e){var r=e,n=[];t.definitions.forEach(function(a){if(a.kind==="OperationDefinition")throw $(85,a.operation,a.name?" named '".concat(a.name.value,"'"):"");a.kind==="FragmentDefinition"&&n.push(a)}),typeof r>"u"&&(w(n.length===1,86,n.length),r=n[0].name.value);var i=d(d({},t),{definitions:W([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:r}}]}}],t.definitions,!0)});return i}function Be(t){t===void 0&&(t=[]);var e={};return t.forEach(function(r){e[r.name.value]=r}),e}function Pt(t,e){switch(t.kind){case"InlineFragment":return t;case"FragmentSpread":{var r=t.name.value;if(typeof e=="function")return e(r);var n=e&&e[r];return w(n,87,r),n||null}default:return null}}function Vo(t){var e=!0;return Z(t,{FragmentSpread:function(r){if(e=!!r.directives&&r.directives.some(function(n){return n.name.value==="unmask"}),!e)return Ft}}),e}function Qo(){}class fr{constructor(e=1/0,r=Qo){this.max=e,this.dispose=r,this.map=new Map,this.newest=null,this.oldest=null}has(e){return this.map.has(e)}get(e){const r=this.getNode(e);return r&&r.value}get size(){return this.map.size}getNode(e){const r=this.map.get(e);if(r&&r!==this.newest){const{older:n,newer:i}=r;i&&(i.older=n),n&&(n.newer=i),r.older=this.newest,r.older.newer=r,r.newer=null,this.newest=r,r===this.oldest&&(this.oldest=i)}return r}set(e,r){let n=this.getNode(e);return n?n.value=r:(n={key:e,value:r,newer:null,older:this.newest},this.newest&&(this.newest.newer=n),this.newest=n,this.oldest=this.oldest||n,this.map.set(e,n),n.value)}clean(){for(;this.oldest&&this.map.size>this.max;)this.delete(this.oldest.key)}delete(e){const r=this.map.get(e);return r?(r===this.newest&&(this.newest=r.older),r===this.oldest&&(this.oldest=r.newer),r.newer&&(r.newer.older=r.older),r.older&&(r.older.newer=r.newer),this.map.delete(e),this.dispose(r.value,e),!0):!1}}function lr(){}const qo=lr,Bo=typeof WeakRef<"u"?WeakRef:function(t){return{deref:()=>t}},Uo=typeof WeakMap<"u"?WeakMap:Map,zo=typeof FinalizationRegistry<"u"?FinalizationRegistry:function(){return{register:lr,unregister:lr}},Wo=10024;class kt{constructor(e=1/0,r=qo){this.max=e,this.dispose=r,this.map=new Uo,this.newest=null,this.oldest=null,this.unfinalizedNodes=new Set,this.finalizationScheduled=!1,this.size=0,this.finalize=()=>{const n=this.unfinalizedNodes.values();for(let i=0;i<Wo;i++){const a=n.next().value;if(!a)break;this.unfinalizedNodes.delete(a);const o=a.key;delete a.key,a.keyRef=new Bo(o),this.registry.register(o,a,a)}this.unfinalizedNodes.size>0?queueMicrotask(this.finalize):this.finalizationScheduled=!1},this.registry=new zo(this.deleteNode.bind(this))}has(e){return this.map.has(e)}get(e){const r=this.getNode(e);return r&&r.value}getNode(e){const r=this.map.get(e);if(r&&r!==this.newest){const{older:n,newer:i}=r;i&&(i.older=n),n&&(n.newer=i),r.older=this.newest,r.older.newer=r,r.newer=null,this.newest=r,r===this.oldest&&(this.oldest=i)}return r}set(e,r){let n=this.getNode(e);return n?n.value=r:(n={key:e,value:r,newer:null,older:this.newest},this.newest&&(this.newest.newer=n),this.newest=n,this.oldest=this.oldest||n,this.scheduleFinalization(n),this.map.set(e,n),this.size++,n.value)}clean(){for(;this.oldest&&this.size>this.max;)this.deleteNode(this.oldest)}deleteNode(e){e===this.newest&&(this.newest=e.older),e===this.oldest&&(this.oldest=e.newer),e.newer&&(e.newer.older=e.older),e.older&&(e.older.newer=e.newer),this.size--;const r=e.key||e.keyRef&&e.keyRef.deref();this.dispose(e.value,r),e.keyRef?this.registry.unregister(e):this.unfinalizedNodes.delete(e),r&&this.map.delete(r)}delete(e){const r=this.map.get(e);return r?(this.deleteNode(r),!0):!1}scheduleFinalization(e){this.unfinalizedNodes.add(e),this.finalizationScheduled||(this.finalizationScheduled=!0,queueMicrotask(this.finalize))}}var zt=new WeakSet;function Di(t){t.size<=(t.max||-1)||zt.has(t)||(zt.add(t),setTimeout(function(){t.clean(),zt.delete(t)},100))}var Lr=function(t,e){var r=new kt(t,e);return r.set=function(n,i){var a=kt.prototype.set.call(this,n,i);return Di(this),a},r},Go=function(t,e){var r=new fr(t,e);return r.set=function(n,i){var a=fr.prototype.set.call(this,n,i);return Di(this),a},r},$o=Symbol.for("apollo.cacheSize"),te=d({},ar[$o]),me={};function jr(t,e){me[t]=e}var Yo=globalThis.__DEV__!==!1?Xo:void 0,Ho=globalThis.__DEV__!==!1?Zo:void 0,Jo=globalThis.__DEV__!==!1?Ii:void 0;function Ko(){var t={parser:1e3,canonicalStringify:1e3,print:2e3,"documentTransform.cache":2e3,"queryManager.getDocumentInfo":2e3,"PersistedQueryLink.persistedQueryHashes":2e3,"fragmentRegistry.transform":2e3,"fragmentRegistry.lookup":1e3,"fragmentRegistry.findFragmentSpreads":4e3,"cache.fragmentQueryDocuments":1e3,"removeTypenameFromVariables.getVariableDefinitions":2e3,"inMemoryCache.maybeBroadcastWatch":5e3,"inMemoryCache.executeSelectionSet":5e4,"inMemoryCache.executeSubSelectedArray":1e4};return Object.fromEntries(Object.entries(t).map(function(e){var r=e[0],n=e[1];return[r,te[r]||n]}))}function Xo(){var t,e,r,n,i;if(globalThis.__DEV__===!1)throw new Error("only supported in development mode");return{limits:Ko(),sizes:d({print:(t=me.print)===null||t===void 0?void 0:t.call(me),parser:(e=me.parser)===null||e===void 0?void 0:e.call(me),canonicalStringify:(r=me.canonicalStringify)===null||r===void 0?void 0:r.call(me),links:pr(this.link),queryManager:{getDocumentInfo:this.queryManager.transformCache.size,documentTransforms:Ni(this.queryManager.documentTransform)}},(i=(n=this.cache).getMemoryInternals)===null||i===void 0?void 0:i.call(n))}}function Ii(){return{cache:{fragmentQueryDocuments:le(this.getFragmentDoc)}}}function Zo(){var t=this.config.fragments;return d(d({},Ii.apply(this)),{addTypenameDocumentTransform:Ni(this.addTypenameTransform),inMemoryCache:{executeSelectionSet:le(this.storeReader.executeSelectionSet),executeSubSelectedArray:le(this.storeReader.executeSubSelectedArray),maybeBroadcastWatch:le(this.maybeBroadcastWatch)},fragmentRegistry:{findFragmentSpreads:le(t?.findFragmentSpreads),lookup:le(t?.lookup),transform:le(t?.transform)}})}function es(t){return!!t&&"dirtyKey"in t}function le(t){return es(t)?t.size:void 0}function xi(t){return t!=null}function Ni(t){return hr(t).map(function(e){return{cache:e}})}function hr(t){return t?W(W([le(t?.performWork)],hr(t?.left),!0),hr(t?.right),!0).filter(xi):[]}function pr(t){var e;return t?W(W([(e=t?.getMemoryInternals)===null||e===void 0?void 0:e.call(t)],pr(t?.left),!0),pr(t?.right),!0).filter(xi):[]}var he=Object.assign(function(e){return JSON.stringify(e,ts)},{reset:function(){xe=new Go(te.canonicalStringify||1e3)}});globalThis.__DEV__!==!1&&jr("canonicalStringify",function(){return xe.size});var xe;he.reset();function ts(t,e){if(e&&typeof e=="object"){var r=Object.getPrototypeOf(e);if(r===Object.prototype||r===null){var n=Object.keys(e);if(n.every(rs))return e;var i=JSON.stringify(n),a=xe.get(i);if(!a){n.sort();var o=JSON.stringify(n);a=xe.get(o)||n,xe.set(i,a),xe.set(o,a)}var s=Object.create(r);return a.forEach(function(u){s[u]=e[u]}),s}}return e}function rs(t,e,r){return e===0||r[e-1]<=t}function Fe(t){return{__ref:String(t)}}function A(t){return!!(t&&typeof t=="object"&&typeof t.__ref=="string")}function ns(t){return j(t)&&t.kind==="Document"&&Array.isArray(t.definitions)}function is(t){return t.kind==="StringValue"}function as(t){return t.kind==="BooleanValue"}function os(t){return t.kind==="IntValue"}function ss(t){return t.kind==="FloatValue"}function us(t){return t.kind==="Variable"}function cs(t){return t.kind==="ObjectValue"}function fs(t){return t.kind==="ListValue"}function ls(t){return t.kind==="EnumValue"}function hs(t){return t.kind==="NullValue"}function Le(t,e,r,n){if(os(r)||ss(r))t[e.value]=Number(r.value);else if(as(r)||is(r))t[e.value]=r.value;else if(cs(r)){var i={};r.fields.map(function(o){return Le(i,o.name,o.value,n)}),t[e.value]=i}else if(us(r)){var a=(n||{})[r.name.value];t[e.value]=a}else if(fs(r))t[e.value]=r.values.map(function(o){var s={};return Le(s,e,o,n),s[e.value]});else if(ls(r))t[e.value]=r.value;else if(hs(r))t[e.value]=null;else throw $(96,e.value,r.kind)}function ps(t,e){var r=null;t.directives&&(r={},t.directives.forEach(function(i){r[i.name.value]={},i.arguments&&i.arguments.forEach(function(a){var o=a.name,s=a.value;return Le(r[i.name.value],o,s,e)})}));var n=null;return t.arguments&&t.arguments.length&&(n={},t.arguments.forEach(function(i){var a=i.name,o=i.value;return Le(n,a,o,e)})),Ci(t.name.value,n,r)}var ds=["connection","include","skip","client","rest","export","nonreactive"],Ge=he,Ci=Object.assign(function(t,e,r){if(e&&r&&r.connection&&r.connection.key)if(r.connection.filter&&r.connection.filter.length>0){var n=r.connection.filter?r.connection.filter:[];n.sort();var i={};return n.forEach(function(s){i[s]=e[s]}),"".concat(r.connection.key,"(").concat(Ge(i),")")}else return r.connection.key;var a=t;if(e){var o=Ge(e);a+="(".concat(o,")")}return r&&Object.keys(r).forEach(function(s){ds.indexOf(s)===-1&&(r[s]&&Object.keys(r[s]).length?a+="@".concat(s,"(").concat(Ge(r[s]),")"):a+="@".concat(s))}),a},{setStringify:function(t){var e=Ge;return Ge=t,e}});function Mt(t,e){if(t.arguments&&t.arguments.length){var r={};return t.arguments.forEach(function(n){var i=n.name,a=n.value;return Le(r,i,a,e)}),r}return null}function ae(t){return t.alias?t.alias.value:t.name.value}function dr(t,e,r){for(var n,i=0,a=e.selections;i<a.length;i++){var o=a[i];if(pe(o)){if(o.name.value==="__typename")return t[ae(o)]}else n?n.push(o):n=[o]}if(typeof t.__typename=="string")return t.__typename;if(n)for(var s=0,u=n;s<u.length;s++){var o=u[s],c=dr(t,Pt(o,r).selectionSet,r);if(typeof c=="string")return c}}function pe(t){return t.kind==="Field"}function vs(t){return t.kind==="InlineFragment"}function Ue(t){w(t&&t.kind==="Document",88);var e=t.definitions.filter(function(r){return r.kind!=="FragmentDefinition"}).map(function(r){if(r.kind!=="OperationDefinition")throw $(89,r.kind);return r});return w(e.length<=1,90,e.length),t}function Oe(t){return Ue(t),t.definitions.filter(function(e){return e.kind==="OperationDefinition"})[0]}function Xe(t){return t.definitions.filter(function(e){return e.kind==="OperationDefinition"&&!!e.name}).map(function(e){return e.name.value})[0]||null}function ze(t){return t.definitions.filter(function(e){return e.kind==="FragmentDefinition"})}function Ri(t){var e=Oe(t);return w(e&&e.operation==="query",91),e}function Fi(t){w(t.kind==="Document",92),w(t.definitions.length<=1,93);var e=t.definitions[0];return w(e.kind==="FragmentDefinition",94),e}function ht(t){Ue(t);for(var e,r=0,n=t.definitions;r<n.length;r++){var i=n[r];if(i.kind==="OperationDefinition"){var a=i.operation;if(a==="query"||a==="mutation"||a==="subscription")return i}i.kind==="FragmentDefinition"&&!e&&(e=i)}if(e)return e;throw $(95)}function Vr(t){var e=Object.create(null),r=t&&t.variableDefinitions;return r&&r.length&&r.forEach(function(n){n.defaultValue&&Le(e,n.variable.name,n.defaultValue)}),e}let z=null;const mn={};let ys=1;const ms=()=>class{constructor(){this.id=["slot",ys++,Date.now(),Math.random().toString(36).slice(2)].join(":")}hasValue(){for(let e=z;e;e=e.parent)if(this.id in e.slots){const r=e.slots[this.id];if(r===mn)break;return e!==z&&(z.slots[this.id]=r),!0}return z&&(z.slots[this.id]=mn),!1}getValue(){if(this.hasValue())return z.slots[this.id]}withValue(e,r,n,i){const a={__proto__:null,[this.id]:e},o=z;z={parent:o,slots:a};try{return r.apply(i,n)}finally{z=o}}static bind(e){const r=z;return function(){const n=z;try{return z=r,e.apply(this,arguments)}finally{z=n}}}static noContext(e,r,n){if(z){const i=z;try{return z=null,e.apply(n,r)}finally{z=i}}else return e.apply(n,r)}};function gn(t){try{return t()}catch{}}const Wt="@wry/context:Slot",gs=gn(()=>globalThis)||gn(()=>global)||Object.create(null),bn=gs,pt=bn[Wt]||Array[Wt]||function(t){try{Object.defineProperty(bn,Wt,{value:t,enumerable:!1,writable:!1,configurable:!0})}finally{return t}}(ms()),{bind:Pc,noContext:Mc}=pt,Lt=new pt,{hasOwnProperty:bs}=Object.prototype,Qr=Array.from||function(t){const e=[];return t.forEach(r=>e.push(r)),e};function qr(t){const{unsubscribe:e}=t;typeof e=="function"&&(t.unsubscribe=void 0,e())}const st=[],Es=100;function je(t,e){if(!t)throw new Error(e||"assertion failure")}function Ai(t,e){const r=t.length;return r>0&&r===e.length&&t[r-1]===e[r-1]}function Pi(t){switch(t.length){case 0:throw new Error("unknown value");case 1:return t[0];case 2:throw t[1]}}function Mi(t){return t.slice(0)}class jt{constructor(e){this.fn=e,this.parents=new Set,this.childValues=new Map,this.dirtyChildren=null,this.dirty=!0,this.recomputing=!1,this.value=[],this.deps=null,++jt.count}peek(){if(this.value.length===1&&!de(this))return En(this),this.value[0]}recompute(e){return je(!this.recomputing,"already recomputing"),En(this),de(this)?_s(this,e):Pi(this.value)}setDirty(){this.dirty||(this.dirty=!0,Li(this),qr(this))}dispose(){this.setDirty(),Bi(this),Br(this,(e,r)=>{e.setDirty(),Ui(e,this)})}forget(){this.dispose()}dependOn(e){e.add(this),this.deps||(this.deps=st.pop()||new Set),this.deps.add(e)}forgetDeps(){this.deps&&(Qr(this.deps).forEach(e=>e.delete(this)),this.deps.clear(),st.push(this.deps),this.deps=null)}}jt.count=0;function En(t){const e=Lt.getValue();if(e)return t.parents.add(e),e.childValues.has(t)||e.childValues.set(t,[]),de(t)?Vi(e,t):Qi(e,t),e}function _s(t,e){return Bi(t),Lt.withValue(t,Os,[t,e]),Ts(t,e)&&Ss(t),Pi(t.value)}function Os(t,e){t.recomputing=!0;const{normalizeResult:r}=t;let n;r&&t.value.length===1&&(n=Mi(t.value)),t.value.length=0;try{if(t.value[0]=t.fn.apply(null,e),r&&n&&!Ai(n,t.value))try{t.value[0]=r(t.value[0],n[0])}catch{}}catch(i){t.value[1]=i}t.recomputing=!1}function de(t){return t.dirty||!!(t.dirtyChildren&&t.dirtyChildren.size)}function Ss(t){t.dirty=!1,!de(t)&&ji(t)}function Li(t){Br(t,Vi)}function ji(t){Br(t,Qi)}function Br(t,e){const r=t.parents.size;if(r){const n=Qr(t.parents);for(let i=0;i<r;++i)e(n[i],t)}}function Vi(t,e){je(t.childValues.has(e)),je(de(e));const r=!de(t);if(!t.dirtyChildren)t.dirtyChildren=st.pop()||new Set;else if(t.dirtyChildren.has(e))return;t.dirtyChildren.add(e),r&&Li(t)}function Qi(t,e){je(t.childValues.has(e)),je(!de(e));const r=t.childValues.get(e);r.length===0?t.childValues.set(e,Mi(e.value)):Ai(r,e.value)||t.setDirty(),qi(t,e),!de(t)&&ji(t)}function qi(t,e){const r=t.dirtyChildren;r&&(r.delete(e),r.size===0&&(st.length<Es&&st.push(r),t.dirtyChildren=null))}function Bi(t){t.childValues.size>0&&t.childValues.forEach((e,r)=>{Ui(t,r)}),t.forgetDeps(),je(t.dirtyChildren===null)}function Ui(t,e){e.parents.delete(t),t.childValues.delete(e),qi(t,e)}function Ts(t,e){if(typeof t.subscribe=="function")try{qr(t),t.unsubscribe=t.subscribe.apply(null,e)}catch{return t.setDirty(),!1}return!0}const ws={setDirty:!0,dispose:!0,forget:!0};function zi(t){const e=new Map;function r(n){const i=Lt.getValue();if(i){let a=e.get(n);a||e.set(n,a=new Set),i.dependOn(a)}}return r.dirty=function(i,a){const o=e.get(i);if(o){const s=a&&bs.call(ws,a)?a:"setDirty";Qr(o).forEach(u=>u[s]()),e.delete(i),qr(o)}},r}let _n;function ks(...t){return(_n||(_n=new oe(typeof WeakMap=="function"))).lookupArray(t)}const Gt=new Set;function ut(t,{max:e=Math.pow(2,16),keyArgs:r,makeCacheKey:n=ks,normalizeResult:i,subscribe:a,cache:o=fr}=Object.create(null)){const s=typeof o=="function"?new o(e,l=>l.dispose()):o,u=function(){const l=n.apply(null,r?r.apply(null,arguments):arguments);if(l===void 0)return t.apply(null,arguments);let p=s.get(l);p||(s.set(l,p=new jt(t)),p.normalizeResult=i,p.subscribe=a,p.forget=()=>s.delete(l));const y=p.recompute(Array.prototype.slice.call(arguments));return s.set(l,p),Gt.add(s),Lt.hasValue()||(Gt.forEach(m=>m.clean()),Gt.clear()),y};Object.defineProperty(u,"size",{get:()=>s.size,configurable:!1,enumerable:!1}),Object.freeze(u.options={max:e,keyArgs:r,makeCacheKey:n,normalizeResult:i,subscribe:a,cache:s});function c(l){const p=l&&s.get(l);p&&p.setDirty()}u.dirtyKey=c,u.dirty=function(){c(n.apply(null,arguments))};function f(l){const p=l&&s.get(l);if(p)return p.peek()}u.peekKey=f,u.peek=function(){return f(n.apply(null,arguments))};function h(l){return l?s.delete(l):!1}return u.forgetKey=h,u.forget=function(){return h(n.apply(null,arguments))},u.makeCacheKey=n,u.getKey=r?function(){return n.apply(null,r.apply(null,arguments))}:n,Object.freeze(u)}function Ds(t){return t}var Wi=function(){function t(e,r){r===void 0&&(r=Object.create(null)),this.resultCache=Pr?new WeakSet:new Set,this.transform=e,r.getCacheKey&&(this.getCacheKey=r.getCacheKey),this.cached=r.cache!==!1,this.resetCache()}return t.prototype.getCacheKey=function(e){return[e]},t.identity=function(){return new t(Ds,{cache:!1})},t.split=function(e,r,n){return n===void 0&&(n=t.identity()),Object.assign(new t(function(i){var a=e(i)?r:n;return a.transformDocument(i)},{cache:!1}),{left:r,right:n})},t.prototype.resetCache=function(){var e=this;if(this.cached){var r=new oe(Te);this.performWork=ut(t.prototype.performWork.bind(this),{makeCacheKey:function(n){var i=e.getCacheKey(n);if(i)return w(Array.isArray(i),77),r.lookupArray(i)},max:te["documentTransform.cache"],cache:kt})}},t.prototype.performWork=function(e){return Ue(e),this.transform(e)},t.prototype.transformDocument=function(e){if(this.resultCache.has(e))return e;var r=this.performWork(e);return this.resultCache.add(r),r},t.prototype.concat=function(e){var r=this;return Object.assign(new t(function(n){return e.transformDocument(r.transformDocument(n))},{cache:!1}),{left:this,right:e})},t}(),et,be=Object.assign(function(t){var e=et.get(t);return e||(e=wo(t),et.set(t,e)),e},{reset:function(){et=new Lr(te.print||2e3)}});be.reset();globalThis.__DEV__!==!1&&jr("print",function(){return et?et.size:0});var V=Array.isArray;function H(t){return Array.isArray(t)&&t.length>0}var On={kind:T.FIELD,name:{kind:T.NAME,value:"__typename"}};function Gi(t,e){return!t||t.selectionSet.selections.every(function(r){return r.kind===T.FRAGMENT_SPREAD&&Gi(e[r.name.value],e)})}function Is(t){return Gi(Oe(t)||Fi(t),Be(ze(t)))?null:t}function xs(t){var e=new Map,r=new Map;return t.forEach(function(n){n&&(n.name?e.set(n.name,n):n.test&&r.set(n.test,n))}),function(n){var i=e.get(n.name.value);return!i&&r.size&&r.forEach(function(a,o){o(n)&&(i=a)}),i}}function Sn(t){var e=new Map;return function(n){n===void 0&&(n=t);var i=e.get(n);return i||e.set(n,i={variables:new Set,fragmentSpreads:new Set}),i}}function $i(t,e){Ue(e);for(var r=Sn(""),n=Sn(""),i=function(v){for(var b=0,E=void 0;b<v.length&&(E=v[b]);++b)if(!V(E)){if(E.kind===T.OPERATION_DEFINITION)return r(E.name&&E.name.value);if(E.kind===T.FRAGMENT_DEFINITION)return n(E.name.value)}return globalThis.__DEV__!==!1&&w.error(97),null},a=0,o=e.definitions.length-1;o>=0;--o)e.definitions[o].kind===T.OPERATION_DEFINITION&&++a;var s=xs(t),u=function(v){return H(v)&&v.map(s).some(function(b){return b&&b.remove})},c=new Map,f=!1,h={enter:function(v){if(u(v.directives))return f=!0,null}},l=Z(e,{Field:h,InlineFragment:h,VariableDefinition:{enter:function(){return!1}},Variable:{enter:function(v,b,E,O,S){var k=i(S);k&&k.variables.add(v.name.value)}},FragmentSpread:{enter:function(v,b,E,O,S){if(u(v.directives))return f=!0,null;var k=i(S);k&&k.fragmentSpreads.add(v.name.value)}},FragmentDefinition:{enter:function(v,b,E,O){c.set(JSON.stringify(O),v)},leave:function(v,b,E,O){var S=c.get(JSON.stringify(O));if(v===S)return v;if(a>0&&v.selectionSet.selections.every(function(k){return k.kind===T.FIELD&&k.name.value==="__typename"}))return n(v.name.value).removed=!0,f=!0,null}},Directive:{leave:function(v){if(s(v))return f=!0,null}}});if(!f)return e;var p=function(v){return v.transitiveVars||(v.transitiveVars=new Set(v.variables),v.removed||v.fragmentSpreads.forEach(function(b){p(n(b)).transitiveVars.forEach(function(E){v.transitiveVars.add(E)})})),v},y=new Set;l.definitions.forEach(function(v){v.kind===T.OPERATION_DEFINITION?p(r(v.name&&v.name.value)).fragmentSpreads.forEach(function(b){y.add(b)}):v.kind===T.FRAGMENT_DEFINITION&&a===0&&!n(v.name.value).removed&&y.add(v.name.value)}),y.forEach(function(v){p(n(v)).fragmentSpreads.forEach(function(b){y.add(b)})});var m=function(v){return!!(!y.has(v)||n(v).removed)},g={enter:function(v){if(m(v.name.value))return null}};return Is(Z(l,{FragmentSpread:g,FragmentDefinition:g,OperationDefinition:{leave:function(v){if(v.variableDefinitions){var b=p(r(v.name&&v.name.value)).transitiveVars;if(b.size<v.variableDefinitions.length)return d(d({},v),{variableDefinitions:v.variableDefinitions.filter(function(E){return b.has(E.variable.name.value)})})}}}}))}var Ur=Object.assign(function(t){return Z(t,{SelectionSet:{enter:function(e,r,n){if(!(n&&n.kind===T.OPERATION_DEFINITION)){var i=e.selections;if(i){var a=i.some(function(s){return pe(s)&&(s.name.value==="__typename"||s.name.value.lastIndexOf("__",0)===0)});if(!a){var o=n;if(!(pe(o)&&o.directives&&o.directives.some(function(s){return s.name.value==="export"})))return d(d({},e),{selections:W(W([],i,!0),[On],!1)})}}}}}})},{added:function(t){return t===On}});function Ns(t){var e=ht(t),r=e.operation;if(r==="query")return t;var n=Z(t,{OperationDefinition:{enter:function(i){return d(d({},i),{operation:"query"})}}});return n}function Yi(t){Ue(t);var e=$i([{test:function(r){return r.name.value==="client"},remove:!0}],t);return e}function Cs(t){return Ue(t),Z(t,{FragmentSpread:function(e){var r;if(!(!((r=e.directives)===null||r===void 0)&&r.some(function(n){return n.name.value==="unmask"})))return d(d({},e),{directives:W(W([],e.directives||[],!0),[{kind:T.DIRECTIVE,name:{kind:T.NAME,value:"nonreactive"}}],!1)})}})}var Rs=Object.prototype.hasOwnProperty;function Tn(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Vt(t)}function Vt(t){var e=t[0]||{},r=t.length;if(r>1)for(var n=new ve,i=1;i<r;++i)e=n.merge(e,t[i]);return e}var Fs=function(t,e,r){return this.merge(t[r],e[r])},ve=function(){function t(e){e===void 0&&(e=Fs),this.reconciler=e,this.isObject=j,this.pastCopies=new Set}return t.prototype.merge=function(e,r){for(var n=this,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];return j(r)&&j(e)?(Object.keys(r).forEach(function(o){if(Rs.call(e,o)){var s=e[o];if(r[o]!==s){var u=n.reconciler.apply(n,W([e,r,o],i,!1));u!==s&&(e=n.shallowCopyForMerge(e),e[o]=u)}}else e=n.shallowCopyForMerge(e),e[o]=r[o]}),e):r},t.prototype.shallowCopyForMerge=function(e){return j(e)&&(this.pastCopies.has(e)||(Array.isArray(e)?e=e.slice(0):e=d({__proto__:Object.getPrototypeOf(e)},e),this.pastCopies.add(e))),e},t}();function As(t,e){var r=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=Ps(t))||e){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ps(t,e){if(t){if(typeof t=="string")return wn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wn(t,e)}}function wn(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function kn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function zr(t,e,r){return e&&kn(t.prototype,e),r&&kn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var Wr=function(){return typeof Symbol=="function"},Gr=function(t){return Wr()&&!!Symbol[t]},$r=function(t){return Gr(t)?Symbol[t]:"@@"+t};Wr()&&!Gr("observable")&&(Symbol.observable=Symbol("observable"));var Ms=$r("iterator"),vr=$r("observable"),Hi=$r("species");function Dt(t,e){var r=t[e];if(r!=null){if(typeof r!="function")throw new TypeError(r+" is not a function");return r}}function $e(t){var e=t.constructor;return e!==void 0&&(e=e[Hi],e===null&&(e=void 0)),e!==void 0?e:L}function Ls(t){return t instanceof L}function Ve(t){Ve.log?Ve.log(t):setTimeout(function(){throw t})}function Et(t){Promise.resolve().then(function(){try{t()}catch(e){Ve(e)}})}function Ji(t){var e=t._cleanup;if(e!==void 0&&(t._cleanup=void 0,!!e))try{if(typeof e=="function")e();else{var r=Dt(e,"unsubscribe");r&&r.call(e)}}catch(n){Ve(n)}}function yr(t){t._observer=void 0,t._queue=void 0,t._state="closed"}function js(t){var e=t._queue;if(e){t._queue=void 0,t._state="ready";for(var r=0;r<e.length&&(Ki(t,e[r].type,e[r].value),t._state!=="closed");++r);}}function Ki(t,e,r){t._state="running";var n=t._observer;try{var i=Dt(n,e);switch(e){case"next":i&&i.call(n,r);break;case"error":if(yr(t),i)i.call(n,r);else throw r;break;case"complete":yr(t),i&&i.call(n);break}}catch(a){Ve(a)}t._state==="closed"?Ji(t):t._state==="running"&&(t._state="ready")}function $t(t,e,r){if(t._state!=="closed"){if(t._state==="buffering"){t._queue.push({type:e,value:r});return}if(t._state!=="ready"){t._state="buffering",t._queue=[{type:e,value:r}],Et(function(){return js(t)});return}Ki(t,e,r)}}var Vs=function(){function t(r,n){this._cleanup=void 0,this._observer=r,this._queue=void 0,this._state="initializing";var i=new Qs(this);try{this._cleanup=n.call(void 0,i)}catch(a){i.error(a)}this._state==="initializing"&&(this._state="ready")}var e=t.prototype;return e.unsubscribe=function(){this._state!=="closed"&&(yr(this),Ji(this))},zr(t,[{key:"closed",get:function(){return this._state==="closed"}}]),t}(),Qs=function(){function t(r){this._subscription=r}var e=t.prototype;return e.next=function(n){$t(this._subscription,"next",n)},e.error=function(n){$t(this._subscription,"error",n)},e.complete=function(){$t(this._subscription,"complete")},zr(t,[{key:"closed",get:function(){return this._subscription._state==="closed"}}]),t}(),L=function(){function t(r){if(!(this instanceof t))throw new TypeError("Observable cannot be called as a function");if(typeof r!="function")throw new TypeError("Observable initializer must be a function");this._subscriber=r}var e=t.prototype;return e.subscribe=function(n){return(typeof n!="object"||n===null)&&(n={next:n,error:arguments[1],complete:arguments[2]}),new Vs(n,this._subscriber)},e.forEach=function(n){var i=this;return new Promise(function(a,o){if(typeof n!="function"){o(new TypeError(n+" is not a function"));return}function s(){u.unsubscribe(),a()}var u=i.subscribe({next:function(c){try{n(c,s)}catch(f){o(f),u.unsubscribe()}},error:o,complete:a})})},e.map=function(n){var i=this;if(typeof n!="function")throw new TypeError(n+" is not a function");var a=$e(this);return new a(function(o){return i.subscribe({next:function(s){try{s=n(s)}catch(u){return o.error(u)}o.next(s)},error:function(s){o.error(s)},complete:function(){o.complete()}})})},e.filter=function(n){var i=this;if(typeof n!="function")throw new TypeError(n+" is not a function");var a=$e(this);return new a(function(o){return i.subscribe({next:function(s){try{if(!n(s))return}catch(u){return o.error(u)}o.next(s)},error:function(s){o.error(s)},complete:function(){o.complete()}})})},e.reduce=function(n){var i=this;if(typeof n!="function")throw new TypeError(n+" is not a function");var a=$e(this),o=arguments.length>1,s=!1,u=arguments[1],c=u;return new a(function(f){return i.subscribe({next:function(h){var l=!s;if(s=!0,!l||o)try{c=n(c,h)}catch(p){return f.error(p)}else c=h},error:function(h){f.error(h)},complete:function(){if(!s&&!o)return f.error(new TypeError("Cannot reduce an empty sequence"));f.next(c),f.complete()}})})},e.concat=function(){for(var n=this,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=$e(this);return new s(function(u){var c,f=0;function h(l){c=l.subscribe({next:function(p){u.next(p)},error:function(p){u.error(p)},complete:function(){f===a.length?(c=void 0,u.complete()):h(s.from(a[f++]))}})}return h(n),function(){c&&(c.unsubscribe(),c=void 0)}})},e.flatMap=function(n){var i=this;if(typeof n!="function")throw new TypeError(n+" is not a function");var a=$e(this);return new a(function(o){var s=[],u=i.subscribe({next:function(f){if(n)try{f=n(f)}catch(l){return o.error(l)}var h=a.from(f).subscribe({next:function(l){o.next(l)},error:function(l){o.error(l)},complete:function(){var l=s.indexOf(h);l>=0&&s.splice(l,1),c()}});s.push(h)},error:function(f){o.error(f)},complete:function(){c()}});function c(){u.closed&&s.length===0&&o.complete()}return function(){s.forEach(function(f){return f.unsubscribe()}),u.unsubscribe()}})},e[vr]=function(){return this},t.from=function(n){var i=typeof this=="function"?this:t;if(n==null)throw new TypeError(n+" is not an object");var a=Dt(n,vr);if(a){var o=a.call(n);if(Object(o)!==o)throw new TypeError(o+" is not an object");return Ls(o)&&o.constructor===i?o:new i(function(s){return o.subscribe(s)})}if(Gr("iterator")&&(a=Dt(n,Ms),a))return new i(function(s){Et(function(){if(!s.closed){for(var u=As(a.call(n)),c;!(c=u()).done;){var f=c.value;if(s.next(f),s.closed)return}s.complete()}})});if(Array.isArray(n))return new i(function(s){Et(function(){if(!s.closed){for(var u=0;u<n.length;++u)if(s.next(n[u]),s.closed)return;s.complete()}})});throw new TypeError(n+" is not observable")},t.of=function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=typeof this=="function"?this:t;return new o(function(s){Et(function(){if(!s.closed){for(var u=0;u<i.length;++u)if(s.next(i[u]),s.closed)return;s.complete()}})})},zr(t,null,[{key:Hi,get:function(){return this}}]),t}();Wr()&&Object.defineProperty(L,Symbol("extensions"),{value:{symbol:vr,hostReportError:Ve},configurable:!0});function qs(t){var e,r=t.Symbol;if(typeof r=="function")if(r.observable)e=r.observable;else{typeof r.for=="function"?e=r.for("https://github.com/benlesh/symbol-observable"):e=r("https://github.com/benlesh/symbol-observable");try{r.observable=e}catch{}}else e="@@observable";return e}var ke;typeof self<"u"?ke=self:typeof window<"u"?ke=window:typeof global<"u"?ke=global:typeof module<"u"?ke=module:ke=Function("return this")();qs(ke);var Dn=L.prototype,In="@@observable";Dn[In]||(Dn[In]=function(){return this});function Bs(t){return t.catch(function(){}),t}var Us=Object.prototype.toString;function Xi(t){return mr(t)}function mr(t,e){switch(Us.call(t)){case"[object Array]":{if(e=e||new Map,e.has(t))return e.get(t);var r=t.slice(0);return e.set(t,r),r.forEach(function(i,a){r[a]=mr(i,e)}),r}case"[object Object]":{if(e=e||new Map,e.has(t))return e.get(t);var n=Object.create(Object.getPrototypeOf(t));return e.set(t,n),Object.keys(t).forEach(function(i){n[i]=mr(t[i],e)}),n}default:return t}}function zs(t){var e=new Set([t]);return e.forEach(function(r){j(r)&&Ws(r)===r&&Object.getOwnPropertyNames(r).forEach(function(n){j(r[n])&&e.add(r[n])})}),t}function Ws(t){if(globalThis.__DEV__!==!1&&!Object.isFrozen(t))try{Object.freeze(t)}catch(e){if(e instanceof TypeError)return null;throw e}return t}function Qe(t){return globalThis.__DEV__!==!1&&zs(t),t}function tt(t,e,r){var n=[];t.forEach(function(i){return i[e]&&n.push(i)}),n.forEach(function(i){return i[e](r)})}function Yt(t,e,r){return new L(function(n){var i={then:function(u){return new Promise(function(c){return c(u())})}};function a(u,c){return function(f){if(u){var h=function(){return n.closed?0:u(f)};i=i.then(h,h).then(function(l){return n.next(l)},function(l){return n.error(l)})}else n[c](f)}}var o={next:a(e,"next"),error:a(r,"error"),complete:function(){i.then(function(){return n.complete()})}},s=t.subscribe(o);return function(){return s.unsubscribe()}})}function Zi(t){function e(r){Object.defineProperty(t,r,{value:L})}return Mr&&Symbol.species&&e(Symbol.species),e("@@species"),t}function xn(t){return t&&typeof t.then=="function"}var De=function(t){re(e,t);function e(r){var n=t.call(this,function(i){return n.addObserver(i),function(){return n.removeObserver(i)}})||this;return n.observers=new Set,n.promise=new Promise(function(i,a){n.resolve=i,n.reject=a}),n.handlers={next:function(i){n.sub!==null&&(n.latest=["next",i],n.notify("next",i),tt(n.observers,"next",i))},error:function(i){var a=n.sub;a!==null&&(a&&setTimeout(function(){return a.unsubscribe()}),n.sub=null,n.latest=["error",i],n.reject(i),n.notify("error",i),tt(n.observers,"error",i))},complete:function(){var i=n,a=i.sub,o=i.sources,s=o===void 0?[]:o;if(a!==null){var u=s.shift();u?xn(u)?u.then(function(c){return n.sub=c.subscribe(n.handlers)},n.handlers.error):n.sub=u.subscribe(n.handlers):(a&&setTimeout(function(){return a.unsubscribe()}),n.sub=null,n.latest&&n.latest[0]==="next"?n.resolve(n.latest[1]):n.resolve(),n.notify("complete"),tt(n.observers,"complete"))}}},n.nextResultListeners=new Set,n.cancel=function(i){n.reject(i),n.sources=[],n.handlers.error(i)},n.promise.catch(function(i){}),typeof r=="function"&&(r=[new L(r)]),xn(r)?r.then(function(i){return n.start(i)},n.handlers.error):n.start(r),n}return e.prototype.start=function(r){this.sub===void 0&&(this.sources=Array.from(r),this.handlers.complete())},e.prototype.deliverLastMessage=function(r){if(this.latest){var n=this.latest[0],i=r[n];i&&i.call(r,this.latest[1]),this.sub===null&&n==="next"&&r.complete&&r.complete()}},e.prototype.addObserver=function(r){this.observers.has(r)||(this.deliverLastMessage(r),this.observers.add(r))},e.prototype.removeObserver=function(r){this.observers.delete(r)&&this.observers.size<1&&this.handlers.complete()},e.prototype.notify=function(r,n){var i=this.nextResultListeners;i.size&&(this.nextResultListeners=new Set,i.forEach(function(a){return a(r,n)}))},e.prototype.beforeNext=function(r){var n=!1;this.nextResultListeners.add(function(i,a){n||(n=!0,r(i,a))})},e}(L);Zi(De);function Ae(t){return"incremental"in t}function Gs(t){return"hasNext"in t&&"data"in t}function $s(t){return Ae(t)||Gs(t)}function Ys(t){return j(t)&&"payload"in t}function ea(t,e){var r=t,n=new ve;return Ae(e)&&H(e.incremental)&&e.incremental.forEach(function(i){for(var a=i.data,o=i.path,s=o.length-1;s>=0;--s){var u=o[s],c=!isNaN(+u),f=c?[]:{};f[u]=a,a=f}r=n.merge(r,a)}),r}function _t(t){var e=gr(t);return H(e)}function gr(t){var e=H(t.errors)?t.errors.slice(0):[];return Ae(t)&&H(t.incremental)&&t.incremental.forEach(function(r){r.errors&&e.push.apply(e,r.errors)}),e}function Se(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=Object.create(null);return t.forEach(function(n){n&&Object.keys(n).forEach(function(i){var a=n[i];a!==void 0&&(r[i]=a)})}),r}function Pe(t,e){return Se(t,e,e.variables&&{variables:Se(d(d({},t&&t.variables),e.variables))})}function Ht(t){return new L(function(e){e.error(t)})}var ta=function(t,e,r){var n=new Error(r);throw n.name="ServerError",n.response=t,n.statusCode=t.status,n.result=e,n};function Hs(t){for(var e=["query","operationName","variables","extensions","context"],r=0,n=Object.keys(t);r<n.length;r++){var i=n[r];if(e.indexOf(i)<0)throw $(46,i)}return t}function Js(t,e){var r=d({},t),n=function(a){typeof a=="function"?r=d(d({},r),a(r)):r=d(d({},r),a)},i=function(){return d({},r)};return Object.defineProperty(e,"setContext",{enumerable:!1,value:n}),Object.defineProperty(e,"getContext",{enumerable:!1,value:i}),e}function Ks(t){var e={variables:t.variables||{},extensions:t.extensions||{},operationName:t.operationName,query:t.query};return e.operationName||(e.operationName=typeof e.query!="string"?Xe(e.query)||void 0:""),e}function Xs(t,e){var r=d({},t),n=new Set(Object.keys(t));return Z(e,{Variable:function(i,a,o){o&&o.kind!=="VariableDefinition"&&n.delete(i.name.value)}}),n.forEach(function(i){delete r[i]}),r}function Nn(t,e){return e?e(t):L.of()}function Ye(t){return typeof t=="function"?new We(t):t}function mt(t){return t.request.length<=1}var We=function(){function t(e){e&&(this.request=e)}return t.empty=function(){return new t(function(){return L.of()})},t.from=function(e){return e.length===0?t.empty():e.map(Ye).reduce(function(r,n){return r.concat(n)})},t.split=function(e,r,n){var i=Ye(r),a=Ye(n||new t(Nn)),o;return mt(i)&&mt(a)?o=new t(function(s){return e(s)?i.request(s)||L.of():a.request(s)||L.of()}):o=new t(function(s,u){return e(s)?i.request(s,u)||L.of():a.request(s,u)||L.of()}),Object.assign(o,{left:i,right:a})},t.execute=function(e,r){return e.request(Js(r.context,Ks(Hs(r))))||L.of()},t.concat=function(e,r){var n=Ye(e);if(mt(n))return globalThis.__DEV__!==!1&&w.warn(38,n),n;var i=Ye(r),a;return mt(i)?a=new t(function(o){return n.request(o,function(s){return i.request(s)||L.of()})||L.of()}):a=new t(function(o,s){return n.request(o,function(u){return i.request(u,s)||L.of()})||L.of()}),Object.assign(a,{left:n,right:i})},t.prototype.split=function(e,r,n){return this.concat(t.split(e,r,n||new t(Nn)))},t.prototype.concat=function(e){return t.concat(this,e)},t.prototype.request=function(e,r){throw $(39)},t.prototype.onError=function(e,r){if(r&&r.error)return r.error(e),!1;throw e},t.prototype.setOnError=function(e){return this.onError=e,this},t}(),Lc=We.from,br=We.execute;function Zs(t){var e,r=t[Symbol.asyncIterator]();return e={next:function(){return r.next()}},e[Symbol.asyncIterator]=function(){return this},e}function eu(t){var e=null,r=null,n=!1,i=[],a=[];function o(h){if(!r){if(a.length){var l=a.shift();if(Array.isArray(l)&&l[0])return l[0]({value:h,done:!1})}i.push(h)}}function s(h){r=h;var l=a.slice();l.forEach(function(p){p[1](h)}),!e||e()}function u(){n=!0;var h=a.slice();h.forEach(function(l){l[0]({value:void 0,done:!0})}),!e||e()}e=function(){e=null,t.removeListener("data",o),t.removeListener("error",s),t.removeListener("end",u),t.removeListener("finish",u),t.removeListener("close",u)},t.on("data",o),t.on("error",s),t.on("end",u),t.on("finish",u),t.on("close",u);function c(){return new Promise(function(h,l){if(r)return l(r);if(i.length)return h({value:i.shift(),done:!1});if(n)return h({value:void 0,done:!0});a.push([h,l])})}var f={next:function(){return c()}};return At&&(f[Symbol.asyncIterator]=function(){return this}),f}function tu(t){var e=!1,r={next:function(){return e?Promise.resolve({value:void 0,done:!0}):(e=!0,new Promise(function(n,i){t.then(function(a){n({value:a,done:!1})}).catch(i)}))}};return At&&(r[Symbol.asyncIterator]=function(){return this}),r}function Cn(t){var e={next:function(){return t.read()}};return At&&(e[Symbol.asyncIterator]=function(){return this}),e}function ru(t){return!!t.body}function nu(t){return!!t.getReader}function iu(t){return!!(At&&t[Symbol.asyncIterator])}function au(t){return!!t.stream}function ou(t){return!!t.arrayBuffer}function su(t){return!!t.pipe}function uu(t){var e=t;if(ru(t)&&(e=t.body),iu(e))return Zs(e);if(nu(e))return Cn(e.getReader());if(au(e))return Cn(e.stream().getReader());if(ou(e))return tu(e.arrayBuffer());if(su(e))return eu(e);throw new Error("Unknown body type for responseIterator. Please pass a streamable response.")}var Yr=Symbol();function cu(t){return t.extensions?Array.isArray(t.extensions[Yr]):!1}function ra(t){return t.hasOwnProperty("graphQLErrors")}var fu=function(t){var e=W(W(W([],t.graphQLErrors,!0),t.clientErrors,!0),t.protocolErrors,!0);return t.networkError&&e.push(t.networkError),e.map(function(r){return j(r)&&r.message||"Error message not found."}).join(`
`)},ne=function(t){re(e,t);function e(r){var n=r.graphQLErrors,i=r.protocolErrors,a=r.clientErrors,o=r.networkError,s=r.errorMessage,u=r.extraInfo,c=t.call(this,s)||this;return c.name="ApolloError",c.graphQLErrors=n||[],c.protocolErrors=i||[],c.clientErrors=a||[],c.networkError=o||null,c.message=s||fu(c),c.extraInfo=u,c.cause=W(W(W([o],n||[],!0),i||[],!0),a||[],!0).find(function(f){return!!f})||null,c.__proto__=e.prototype,c}return e}(Error),Rn=Object.prototype.hasOwnProperty;function lu(t,e){return ce(this,void 0,void 0,function(){var r,n,i,a,o,s,u,c,f,h,l,p,y,m,g,v,b,E,O,S,k,x,I,R;return fe(this,function(M){switch(M.label){case 0:if(TextDecoder===void 0)throw new Error("TextDecoder must be defined in the environment: please import a polyfill.");r=new TextDecoder("utf-8"),n=(R=t.headers)===null||R===void 0?void 0:R.get("content-type"),i="boundary=",a=n?.includes(i)?n?.substring(n?.indexOf(i)+i.length).replace(/['"]/g,"").replace(/\;(.*)/gm,"").trim():"-",o=`\r
--`.concat(a),s="",u=uu(t),c=!0,M.label=1;case 1:return c?[4,u.next()]:[3,3];case 2:for(f=M.sent(),h=f.value,l=f.done,p=typeof h=="string"?h:r.decode(h),y=s.length-o.length+1,c=!l,s+=p,m=s.indexOf(o,y);m>-1;){if(g=void 0,x=[s.slice(0,m),s.slice(m+o.length)],g=x[0],s=x[1],v=g.indexOf(`\r
\r
`),b=hu(g.slice(0,v)),E=b["content-type"],E&&E.toLowerCase().indexOf("application/json")===-1)throw new Error("Unsupported patch content type: application/json is required.");if(O=g.slice(v),O){if(S=na(t,O),Object.keys(S).length>1||"data"in S||"incremental"in S||"errors"in S||"payload"in S)if(Ys(S)){if(k={},"payload"in S){if(Object.keys(S).length===1&&S.payload===null)return[2];k=d({},S.payload)}"errors"in S&&(k=d(d({},k),{extensions:d(d({},"extensions"in k?k.extensions:null),(I={},I[Yr]=S.errors,I))})),e(k)}else e(S);else if(Object.keys(S).length===1&&"hasNext"in S&&!S.hasNext)return[2]}m=s.indexOf(o)}return[3,1];case 3:return[2]}})})}function hu(t){var e={};return t.split(`
`).forEach(function(r){var n=r.indexOf(":");if(n>-1){var i=r.slice(0,n).trim().toLowerCase(),a=r.slice(n+1).trim();e[i]=a}}),e}function na(t,e){if(t.status>=300){var r=function(){try{return JSON.parse(e)}catch{return e}};ta(t,r(),"Response not successful: Received status code ".concat(t.status))}try{return JSON.parse(e)}catch(i){var n=i;throw n.name="ServerParseError",n.response=t,n.statusCode=t.status,n.bodyText=e,n}}function pu(t,e){t.result&&t.result.errors&&t.result.data&&e.next(t.result),e.error(t)}function du(t){return function(e){return e.text().then(function(r){return na(e,r)}).then(function(r){return!Array.isArray(r)&&!Rn.call(r,"data")&&!Rn.call(r,"errors")&&ta(e,r,"Server response was missing for query '".concat(Array.isArray(t)?t.map(function(n){return n.operationName}):t.operationName,"'.")),r})}}var Er=function(t,e){var r;try{r=JSON.stringify(t)}catch(i){var n=$(42,e,i.message);throw n.parseError=i,n}return r},vu={includeQuery:!0,includeExtensions:!1,preserveHeaderCase:!1},yu={accept:"*/*","content-type":"application/json"},mu={method:"POST"},gu={http:vu,headers:yu,options:mu},bu=function(t,e){return e(t)};function Eu(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i={},a={};r.forEach(function(h){i=d(d(d({},i),h.options),{headers:d(d({},i.headers),h.headers)}),h.credentials&&(i.credentials=h.credentials),a=d(d({},a),h.http)}),i.headers&&(i.headers=_u(i.headers,a.preserveHeaderCase));var o=t.operationName,s=t.extensions,u=t.variables,c=t.query,f={operationName:o,variables:u};return a.includeExtensions&&(f.extensions=s),a.includeQuery&&(f.query=e(c,be)),{options:i,body:f}}function _u(t,e){if(!e){var r={};return Object.keys(Object(t)).forEach(function(a){r[a.toLowerCase()]=t[a]}),r}var n={};Object.keys(Object(t)).forEach(function(a){n[a.toLowerCase()]={originalName:a,value:t[a]}});var i={};return Object.keys(n).forEach(function(a){i[n[a].originalName]=n[a].value}),i}var Ou=function(t){if(!t&&typeof fetch>"u")throw $(40)},Su=function(t,e){var r=t.getContext(),n=r.uri;return n||(typeof e=="function"?e(t):e||"/graphql")};function Tu(t,e){var r=[],n=function(h,l){r.push("".concat(h,"=").concat(encodeURIComponent(l)))};if("query"in e&&n("query",e.query),e.operationName&&n("operationName",e.operationName),e.variables){var i=void 0;try{i=Er(e.variables,"Variables map")}catch(h){return{parseError:h}}n("variables",i)}if(e.extensions){var a=void 0;try{a=Er(e.extensions,"Extensions map")}catch(h){return{parseError:h}}n("extensions",a)}var o="",s=t,u=t.indexOf("#");u!==-1&&(o=t.substr(u),s=t.substr(0,u));var c=s.indexOf("?")===-1?"?":"&",f=s+c+r.join("&")+o;return{newURI:f}}var Fn=K(function(){return fetch}),wu=function(t){t===void 0&&(t={});var e=t.uri,r=e===void 0?"/graphql":e,n=t.fetch,i=t.print,a=i===void 0?bu:i,o=t.includeExtensions,s=t.preserveHeaderCase,u=t.useGETForQueries,c=t.includeUnusedVariables,f=c===void 0?!1:c,h=X(t,["uri","fetch","print","includeExtensions","preserveHeaderCase","useGETForQueries","includeUnusedVariables"]);globalThis.__DEV__!==!1&&Ou(n||Fn);var l={http:{includeExtensions:o,preserveHeaderCase:s},options:h.fetchOptions,credentials:h.credentials,headers:h.headers};return new We(function(p){var y=Su(p,r),m=p.getContext(),g={};if(m.clientAwareness){var v=m.clientAwareness,b=v.name,E=v.version;b&&(g["apollographql-client-name"]=b),E&&(g["apollographql-client-version"]=E)}var O=d(d({},g),m.headers),S={http:m.http,options:m.fetchOptions,credentials:m.credentials,headers:O};if(ot(["client"],p.query)){var k=Yi(p.query);if(!k)return Ht(new Error("HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`."));p.query=k}var x=Eu(p,a,gu,l,S),I=x.options,R=x.body;R.variables&&!f&&(R.variables=Xs(R.variables,p.query));var M;!I.signal&&typeof AbortController<"u"&&(M=new AbortController,I.signal=M.signal);var G=function(Y){return Y.kind==="OperationDefinition"&&Y.operation==="mutation"},se=function(Y){return Y.kind==="OperationDefinition"&&Y.operation==="subscription"},Q=se(ht(p.query)),ee=ot(["defer"],p.query);if(u&&!p.query.definitions.some(G)&&(I.method="GET"),ee||Q){I.headers=I.headers||{};var Qt="multipart/mixed;";Q&&ee&&globalThis.__DEV__!==!1&&w.warn(41),Q?Qt+="boundary=graphql;subscriptionSpec=1.0,application/json":ee&&(Qt+="deferSpec=20220824,application/json"),I.headers.accept=Qt}if(I.method==="GET"){var rn=Tu(y,R),Fa=rn.newURI,nn=rn.parseError;if(nn)return Ht(nn);y=Fa}else try{I.body=Er(R,"Payload")}catch(Y){return Ht(Y)}return new L(function(Y){var Aa=n||K(function(){return fetch})||Fn,an=Y.next.bind(Y);return Aa(y,I).then(function(we){var qt;p.setContext({response:we});var on=(qt=we.headers)===null||qt===void 0?void 0:qt.get("content-type");return on!==null&&/^multipart\/mixed/i.test(on)?lu(we,an):du(p)(we).then(an)}).then(function(){M=void 0,Y.complete()}).catch(function(we){M=void 0,pu(we,Y)}),function(){M&&M.abort()}})})},ku=function(t){re(e,t);function e(r){r===void 0&&(r={});var n=t.call(this,wu(r).request)||this;return n.options=r,n}return e}(We);const{toString:An,hasOwnProperty:Du}=Object.prototype,Pn=Function.prototype.toString,_r=new Map;function P(t,e){try{return Or(t,e)}finally{_r.clear()}}function Or(t,e){if(t===e)return!0;const r=An.call(t),n=An.call(e);if(r!==n)return!1;switch(r){case"[object Array]":if(t.length!==e.length)return!1;case"[object Object]":{if(Ln(t,e))return!0;const i=Mn(t),a=Mn(e),o=i.length;if(o!==a.length)return!1;for(let s=0;s<o;++s)if(!Du.call(e,i[s]))return!1;for(let s=0;s<o;++s){const u=i[s];if(!Or(t[u],e[u]))return!1}return!0}case"[object Error]":return t.name===e.name&&t.message===e.message;case"[object Number]":if(t!==t)return e!==e;case"[object Boolean]":case"[object Date]":return+t==+e;case"[object RegExp]":case"[object String]":return t==`${e}`;case"[object Map]":case"[object Set]":{if(t.size!==e.size)return!1;if(Ln(t,e))return!0;const i=t.entries(),a=r==="[object Map]";for(;;){const o=i.next();if(o.done)break;const[s,u]=o.value;if(!e.has(s)||a&&!Or(u,e.get(s)))return!1}return!0}case"[object Uint16Array]":case"[object Uint8Array]":case"[object Uint32Array]":case"[object Int32Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object ArrayBuffer]":t=new Uint8Array(t),e=new Uint8Array(e);case"[object DataView]":{let i=t.byteLength;if(i===e.byteLength)for(;i--&&t[i]===e[i];);return i===-1}case"[object AsyncFunction]":case"[object GeneratorFunction]":case"[object AsyncGeneratorFunction]":case"[object Function]":{const i=Pn.call(t);return i!==Pn.call(e)?!1:!Nu(i,xu)}}return!1}function Mn(t){return Object.keys(t).filter(Iu,t)}function Iu(t){return this[t]!==void 0}const xu="{ [native code] }";function Nu(t,e){const r=t.length-e.length;return r>=0&&t.indexOf(e,r)===r}function Ln(t,e){let r=_r.get(t);if(r){if(r.has(e))return!0}else _r.set(t,r=new Set);return r.add(e),!1}function ia(t,e,r,n){var i=e.data,a=X(e,["data"]),o=r.data,s=X(r,["data"]);return P(a,s)&&Ot(ht(t).selectionSet,i,o,{fragmentMap:Be(ze(t)),variables:n})}function Ot(t,e,r,n){if(e===r)return!0;var i=new Set;return t.selections.every(function(a){if(i.has(a)||(i.add(a),!lt(a,n.variables))||jn(a))return!0;if(pe(a)){var o=ae(a),s=e&&e[o],u=r&&r[o],c=a.selectionSet;if(!c)return P(s,u);var f=Array.isArray(s),h=Array.isArray(u);if(f!==h)return!1;if(f&&h){var l=s.length;if(u.length!==l)return!1;for(var p=0;p<l;++p)if(!Ot(c,s[p],u[p],n))return!1;return!0}return Ot(c,s,u,n)}else{var y=Pt(a,n.fragmentMap);if(y)return jn(y)?!0:Ot(y.selectionSet,e,r,n)}})}function jn(t){return!!t.directives&&t.directives.some(Cu)}function Cu(t){return t.name.value==="nonreactive"}var aa=Te?WeakMap:Map,oa=Pr?WeakSet:Set,Hr=new pt,Vn=!1;function sa(){Vn||(Vn=!0,globalThis.__DEV__!==!1&&w.warn(52))}function ua(t,e,r){return Hr.withValue(!0,function(){var n=Ze(t,e,r,!1);return Object.isFrozen(t)&&Qe(n),n})}function Ru(t,e){if(e.has(t))return e.get(t);var r=Array.isArray(t)?[]:Object.create(null);return e.set(t,r),r}function Ze(t,e,r,n,i){var a,o=r.knownChanged,s=Ru(t,r.mutableTargets);if(Array.isArray(t)){for(var u=0,c=Array.from(t.entries());u<c.length;u++){var f=c[u],h=f[0],l=f[1];if(l===null){s[h]=null;continue}var p=Ze(l,e,r,n,globalThis.__DEV__!==!1?"".concat(i||"","[").concat(h,"]"):void 0);o.has(p)&&o.add(s),s[h]=p}return o.has(s)?s:t}for(var y=0,m=e.selections;y<m.length;y++){var g=m[y],v=void 0;if(n&&o.add(s),g.kind===T.FIELD){var b=ae(g),E=g.selectionSet;if(v=s[b]||t[b],v===void 0)continue;if(E&&v!==null){var p=Ze(t[b],E,r,n,globalThis.__DEV__!==!1?"".concat(i||"",".").concat(b):void 0);o.has(p)&&(v=p)}globalThis.__DEV__===!1&&(s[b]=v),globalThis.__DEV__!==!1&&(n&&b!=="__typename"&&!(!((a=Object.getOwnPropertyDescriptor(s,b))===null||a===void 0)&&a.value)?Object.defineProperty(s,b,Fu(b,v,i||"",r.operationName,r.operationType)):(delete s[b],s[b]=v))}if(g.kind===T.INLINE_FRAGMENT&&(!g.typeCondition||r.cache.fragmentMatches(g,t.__typename))&&(v=Ze(t,g.selectionSet,r,n,i)),g.kind===T.FRAGMENT_SPREAD){var O=g.name.value,S=r.fragmentMap[O]||(r.fragmentMap[O]=r.cache.lookupFragment(O));w(S,47,O);var k=Co(g);k!=="mask"&&(v=Ze(t,S.selectionSet,r,k==="migrate",i))}o.has(v)&&o.add(s)}return"__typename"in t&&!("__typename"in s)&&(s.__typename=t.__typename),Object.keys(s).length!==Object.keys(t).length&&o.add(s),o.has(s)?s:t}function Fu(t,e,r,n,i){var a=function(){return Hr.getValue()||(globalThis.__DEV__!==!1&&w.warn(48,n?"".concat(i," '").concat(n,"'"):"anonymous ".concat(i),"".concat(r,".").concat(t).replace(/^\./,"")),a=function(){return e}),e};return{get:function(){return a()},set:function(o){a=function(){return o}},enumerable:!0,configurable:!0}}function ca(t,e,r,n){if(!r.fragmentMatches)return globalThis.__DEV__!==!1&&sa(),t;var i=e.definitions.filter(function(o){return o.kind===T.FRAGMENT_DEFINITION});typeof n>"u"&&(w(i.length===1,49,i.length),n=i[0].name.value);var a=i.find(function(o){return o.name.value===n});return w(!!a,50,n),t==null||P(t,{})?t:ua(t,a.selectionSet,{operationType:"fragment",operationName:a.name.value,fragmentMap:Be(ze(e)),cache:r,mutableTargets:new aa,knownChanged:new oa})}function Au(t,e,r){var n;if(!r.fragmentMatches)return globalThis.__DEV__!==!1&&sa(),t;var i=Oe(e);return w(i,51),t==null?t:ua(t,i.selectionSet,{operationType:i.operation,operationName:(n=i.name)===null||n===void 0?void 0:n.value,fragmentMap:Be(ze(e)),cache:r,mutableTargets:new aa,knownChanged:new oa})}var fa=function(){function t(){this.assumeImmutableResults=!1,this.getFragmentDoc=ut(jo,{max:te["cache.fragmentQueryDocuments"]||1e3,cache:kt})}return t.prototype.lookupFragment=function(e){return null},t.prototype.batch=function(e){var r=this,n=typeof e.optimistic=="string"?e.optimistic:e.optimistic===!1?null:void 0,i;return this.performTransaction(function(){return i=e.update(r)},n),i},t.prototype.recordOptimisticTransaction=function(e,r){this.performTransaction(e,r)},t.prototype.transformDocument=function(e){return e},t.prototype.transformForLink=function(e){return e},t.prototype.identify=function(e){},t.prototype.gc=function(){return[]},t.prototype.modify=function(e){return!1},t.prototype.readQuery=function(e,r){return r===void 0&&(r=!!e.optimistic),this.read(d(d({},e),{rootId:e.id||"ROOT_QUERY",optimistic:r}))},t.prototype.watchFragment=function(e){var r=this,n=e.fragment,i=e.fragmentName,a=e.from,o=e.optimistic,s=o===void 0?!0:o,u=X(e,["fragment","fragmentName","from","optimistic"]),c=this.getFragmentDoc(n,i),f=typeof a>"u"||typeof a=="string"?a:this.identify(a),h=!!e[Symbol.for("apollo.dataMasking")];if(globalThis.__DEV__!==!1){var l=i||Fi(n).name.value;f||globalThis.__DEV__!==!1&&w.warn(1,l)}var p=d(d({},u),{returnPartialData:!0,id:f,query:c,optimistic:s}),y;return new L(function(m){return r.watch(d(d({},p),{immediate:!0,callback:function(g){var v=h?ca(g.result,n,r,i):g.result;if(!(y&&ia(c,{data:y.result},{data:v},e.variables))){var b={data:v,complete:!!g.complete};g.missing&&(b.missing=Vt(g.missing.map(function(E){return E.missing}))),y=d(d({},g),{result:v}),m.next(b)}}}))})},t.prototype.readFragment=function(e,r){return r===void 0&&(r=!!e.optimistic),this.read(d(d({},e),{query:this.getFragmentDoc(e.fragment,e.fragmentName),rootId:e.id,optimistic:r}))},t.prototype.writeQuery=function(e){var r=e.id,n=e.data,i=X(e,["id","data"]);return this.write(Object.assign(i,{dataId:r||"ROOT_QUERY",result:n}))},t.prototype.writeFragment=function(e){var r=e.id,n=e.data,i=e.fragment,a=e.fragmentName,o=X(e,["id","data","fragment","fragmentName"]);return this.write(Object.assign(o,{query:this.getFragmentDoc(i,a),dataId:r,result:n}))},t.prototype.updateQuery=function(e,r){return this.batch({update:function(n){var i=n.readQuery(e),a=r(i);return a==null?i:(n.writeQuery(d(d({},e),{data:a})),a)}})},t.prototype.updateFragment=function(e,r){return this.batch({update:function(n){var i=n.readFragment(e),a=r(i);return a==null?i:(n.writeFragment(d(d({},e),{data:a})),a)}})},t}();globalThis.__DEV__!==!1&&(fa.prototype.getMemoryInternals=Jo);var la=function(t){re(e,t);function e(r,n,i,a){var o,s=t.call(this,r)||this;if(s.message=r,s.path=n,s.query=i,s.variables=a,Array.isArray(s.path)){s.missing=s.message;for(var u=s.path.length-1;u>=0;--u)s.missing=(o={},o[s.path[u]]=s.missing,o)}else s.missing=s.path;return s.__proto__=e.prototype,s}return e}(Error),B=Object.prototype.hasOwnProperty;function He(t){return t==null}function ha(t,e){var r=t.__typename,n=t.id,i=t._id;if(typeof r=="string"&&(e&&(e.keyObject=He(n)?He(i)?void 0:{_id:i}:{id:n}),He(n)&&!He(i)&&(n=i),!He(n)))return"".concat(r,":").concat(typeof n=="number"||typeof n=="string"?n:JSON.stringify(n))}var pa={dataIdFromObject:ha,addTypename:!0,resultCaching:!0,canonizeResults:!1};function Pu(t){return Se(pa,t)}function da(t){var e=t.canonizeResults;return e===void 0?pa.canonizeResults:e}function Mu(t,e){return A(e)?t.get(e.__ref,"__typename"):e&&e.__typename}var va=/^[_a-z][_0-9a-z]*/i;function ye(t){var e=t.match(va);return e?e[0]:t}function Sr(t,e,r){return j(e)?V(e)?e.every(function(n){return Sr(t,n,r)}):t.selections.every(function(n){if(pe(n)&&lt(n,r)){var i=ae(n);return B.call(e,i)&&(!n.selectionSet||Sr(n.selectionSet,e[i],r))}return!0}):!1}function Ne(t){return j(t)&&!A(t)&&!V(t)}function Lu(){return new ve}function ya(t,e){var r=Be(ze(t));return{fragmentMap:r,lookupFragment:function(n){var i=r[n];return!i&&e&&(i=e.lookup(n)),i||null}}}var St=Object.create(null),Jt=function(){return St},Qn=Object.create(null),ct=function(){function t(e,r){var n=this;this.policies=e,this.group=r,this.data=Object.create(null),this.rootIds=Object.create(null),this.refs=Object.create(null),this.getFieldValue=function(i,a){return Qe(A(i)?n.get(i.__ref,a):i&&i[a])},this.canRead=function(i){return A(i)?n.has(i.__ref):typeof i=="object"},this.toReference=function(i,a){if(typeof i=="string")return Fe(i);if(A(i))return i;var o=n.policies.identify(i)[0];if(o){var s=Fe(o);return a&&n.merge(o,i),s}}}return t.prototype.toObject=function(){return d({},this.data)},t.prototype.has=function(e){return this.lookup(e,!0)!==void 0},t.prototype.get=function(e,r){if(this.group.depend(e,r),B.call(this.data,e)){var n=this.data[e];if(n&&B.call(n,r))return n[r]}if(r==="__typename"&&B.call(this.policies.rootTypenamesById,e))return this.policies.rootTypenamesById[e];if(this instanceof ue)return this.parent.get(e,r)},t.prototype.lookup=function(e,r){if(r&&this.group.depend(e,"__exists"),B.call(this.data,e))return this.data[e];if(this instanceof ue)return this.parent.lookup(e,r);if(this.policies.rootTypenamesById[e])return Object.create(null)},t.prototype.merge=function(e,r){var n=this,i;A(e)&&(e=e.__ref),A(r)&&(r=r.__ref);var a=typeof e=="string"?this.lookup(i=e):e,o=typeof r=="string"?this.lookup(i=r):r;if(o){w(typeof i=="string",2);var s=new ve(Vu).merge(a,o);if(this.data[i]=s,s!==a&&(delete this.refs[i],this.group.caching)){var u=Object.create(null);a||(u.__exists=1),Object.keys(o).forEach(function(c){if(!a||a[c]!==s[c]){u[c]=1;var f=ye(c);f!==c&&!n.policies.hasKeyArgs(s.__typename,f)&&(u[f]=1),s[c]===void 0&&!(n instanceof ue)&&delete s[c]}}),u.__typename&&!(a&&a.__typename)&&this.policies.rootTypenamesById[i]===s.__typename&&delete u.__typename,Object.keys(u).forEach(function(c){return n.group.dirty(i,c)})}}},t.prototype.modify=function(e,r){var n=this,i=this.lookup(e);if(i){var a=Object.create(null),o=!1,s=!0,u={DELETE:St,INVALIDATE:Qn,isReference:A,toReference:this.toReference,canRead:this.canRead,readField:function(c,f){return n.policies.readField(typeof c=="string"?{fieldName:c,from:f||Fe(e)}:c,{store:n})}};if(Object.keys(i).forEach(function(c){var f=ye(c),h=i[c];if(h!==void 0){var l=typeof r=="function"?r:r[c]||r[f];if(l){var p=l===Jt?St:l(Qe(h),d(d({},u),{fieldName:f,storeFieldName:c,storage:n.getStorage(e,c)}));if(p===Qn)n.group.dirty(e,c);else if(p===St&&(p=void 0),p!==h&&(a[c]=p,o=!0,h=p,globalThis.__DEV__!==!1)){var y=function(S){if(n.lookup(S.__ref)===void 0)return globalThis.__DEV__!==!1&&w.warn(3,S),!0};if(A(p))y(p);else if(Array.isArray(p))for(var m=!1,g=void 0,v=0,b=p;v<b.length;v++){var E=b[v];if(A(E)){if(m=!0,y(E))break}else if(typeof E=="object"&&E){var O=n.policies.identify(E)[0];O&&(g=E)}if(m&&g!==void 0){globalThis.__DEV__!==!1&&w.warn(4,g);break}}}}h!==void 0&&(s=!1)}}),o)return this.merge(e,a),s&&(this instanceof ue?this.data[e]=void 0:delete this.data[e],this.group.dirty(e,"__exists")),!0}return!1},t.prototype.delete=function(e,r,n){var i,a=this.lookup(e);if(a){var o=this.getFieldValue(a,"__typename"),s=r&&n?this.policies.getStoreFieldName({typename:o,fieldName:r,args:n}):r;return this.modify(e,s?(i={},i[s]=Jt,i):Jt)}return!1},t.prototype.evict=function(e,r){var n=!1;return e.id&&(B.call(this.data,e.id)&&(n=this.delete(e.id,e.fieldName,e.args)),this instanceof ue&&this!==r&&(n=this.parent.evict(e,r)||n),(e.fieldName||n)&&this.group.dirty(e.id,e.fieldName||"__exists")),n},t.prototype.clear=function(){this.replace(null)},t.prototype.extract=function(){var e=this,r=this.toObject(),n=[];return this.getRootIdSet().forEach(function(i){B.call(e.policies.rootTypenamesById,i)||n.push(i)}),n.length&&(r.__META={extraRootIds:n.sort()}),r},t.prototype.replace=function(e){var r=this;if(Object.keys(this.data).forEach(function(a){e&&B.call(e,a)||r.delete(a)}),e){var n=e.__META,i=X(e,["__META"]);Object.keys(i).forEach(function(a){r.merge(a,i[a])}),n&&n.extraRootIds.forEach(this.retain,this)}},t.prototype.retain=function(e){return this.rootIds[e]=(this.rootIds[e]||0)+1},t.prototype.release=function(e){if(this.rootIds[e]>0){var r=--this.rootIds[e];return r||delete this.rootIds[e],r}return 0},t.prototype.getRootIdSet=function(e){return e===void 0&&(e=new Set),Object.keys(this.rootIds).forEach(e.add,e),this instanceof ue?this.parent.getRootIdSet(e):Object.keys(this.policies.rootTypenamesById).forEach(e.add,e),e},t.prototype.gc=function(){var e=this,r=this.getRootIdSet(),n=this.toObject();r.forEach(function(o){B.call(n,o)&&(Object.keys(e.findChildRefIds(o)).forEach(r.add,r),delete n[o])});var i=Object.keys(n);if(i.length){for(var a=this;a instanceof ue;)a=a.parent;i.forEach(function(o){return a.delete(o)})}return i},t.prototype.findChildRefIds=function(e){if(!B.call(this.refs,e)){var r=this.refs[e]=Object.create(null),n=this.data[e];if(!n)return r;var i=new Set([n]);i.forEach(function(a){A(a)&&(r[a.__ref]=!0),j(a)&&Object.keys(a).forEach(function(o){var s=a[o];j(s)&&i.add(s)})})}return this.refs[e]},t.prototype.makeCacheKey=function(){return this.group.keyMaker.lookupArray(arguments)},t}(),ma=function(){function t(e,r){r===void 0&&(r=null),this.caching=e,this.parent=r,this.d=null,this.resetCaching()}return t.prototype.resetCaching=function(){this.d=this.caching?zi():null,this.keyMaker=new oe(Te)},t.prototype.depend=function(e,r){if(this.d){this.d(Kt(e,r));var n=ye(r);n!==r&&this.d(Kt(e,n)),this.parent&&this.parent.depend(e,r)}},t.prototype.dirty=function(e,r){this.d&&this.d.dirty(Kt(e,r),r==="__exists"?"forget":"setDirty")},t}();function Kt(t,e){return e+"#"+t}function qn(t,e){rt(t)&&t.group.depend(e,"__exists")}(function(t){var e=function(r){re(n,r);function n(i){var a=i.policies,o=i.resultCaching,s=o===void 0?!0:o,u=i.seed,c=r.call(this,a,new ma(s))||this;return c.stump=new ju(c),c.storageTrie=new oe(Te),u&&c.replace(u),c}return n.prototype.addLayer=function(i,a){return this.stump.addLayer(i,a)},n.prototype.removeLayer=function(){return this},n.prototype.getStorage=function(){return this.storageTrie.lookupArray(arguments)},n}(t);t.Root=e})(ct||(ct={}));var ue=function(t){re(e,t);function e(r,n,i,a){var o=t.call(this,n.policies,a)||this;return o.id=r,o.parent=n,o.replay=i,o.group=a,i(o),o}return e.prototype.addLayer=function(r,n){return new e(r,this,n,this.group)},e.prototype.removeLayer=function(r){var n=this,i=this.parent.removeLayer(r);return r===this.id?(this.group.caching&&Object.keys(this.data).forEach(function(a){var o=n.data[a],s=i.lookup(a);s?o?o!==s&&Object.keys(o).forEach(function(u){P(o[u],s[u])||n.group.dirty(a,u)}):(n.group.dirty(a,"__exists"),Object.keys(s).forEach(function(u){n.group.dirty(a,u)})):n.delete(a)}),i):i===this.parent?this:i.addLayer(this.id,this.replay)},e.prototype.toObject=function(){return d(d({},this.parent.toObject()),this.data)},e.prototype.findChildRefIds=function(r){var n=this.parent.findChildRefIds(r);return B.call(this.data,r)?d(d({},n),t.prototype.findChildRefIds.call(this,r)):n},e.prototype.getStorage=function(){for(var r=this.parent;r.parent;)r=r.parent;return r.getStorage.apply(r,arguments)},e}(ct),ju=function(t){re(e,t);function e(r){return t.call(this,"EntityStore.Stump",r,function(){},new ma(r.group.caching,r.group))||this}return e.prototype.removeLayer=function(){return this},e.prototype.merge=function(r,n){return this.parent.merge(r,n)},e}(ue);function Vu(t,e,r){var n=t[r],i=e[r];return P(n,i)?n:i}function rt(t){return!!(t instanceof ct&&t.group.caching)}function Qu(t){return j(t)?V(t)?t.slice(0):d({__proto__:Object.getPrototypeOf(t)},t):t}var Bn=function(){function t(){this.known=new(Pr?WeakSet:Set),this.pool=new oe(Te),this.passes=new WeakMap,this.keysByJSON=new Map,this.empty=this.admit({})}return t.prototype.isKnown=function(e){return j(e)&&this.known.has(e)},t.prototype.pass=function(e){if(j(e)){var r=Qu(e);return this.passes.set(r,e),r}return e},t.prototype.admit=function(e){var r=this;if(j(e)){var n=this.passes.get(e);if(n)return n;var i=Object.getPrototypeOf(e);switch(i){case Array.prototype:{if(this.known.has(e))return e;var a=e.map(this.admit,this),o=this.pool.lookupArray(a);return o.array||(this.known.add(o.array=a),globalThis.__DEV__!==!1&&Object.freeze(a)),o.array}case null:case Object.prototype:{if(this.known.has(e))return e;var s=Object.getPrototypeOf(e),u=[s],c=this.sortedKeys(e);u.push(c.json);var f=u.length;c.sorted.forEach(function(p){u.push(r.admit(e[p]))});var o=this.pool.lookupArray(u);if(!o.object){var h=o.object=Object.create(s);this.known.add(h),c.sorted.forEach(function(p,y){h[p]=u[f+y]}),globalThis.__DEV__!==!1&&Object.freeze(h)}return o.object}}}return e},t.prototype.sortedKeys=function(e){var r=Object.keys(e),n=this.pool.lookupArray(r);if(!n.keys){r.sort();var i=JSON.stringify(r);(n.keys=this.keysByJSON.get(i))||this.keysByJSON.set(i,n.keys={sorted:r,json:i})}return n.keys},t}();function Un(t){return[t.selectionSet,t.objectOrReference,t.context,t.context.canonizeResults]}var qu=function(){function t(e){var r=this;this.knownResults=new(Te?WeakMap:Map),this.config=Se(e,{addTypename:e.addTypename!==!1,canonizeResults:da(e)}),this.canon=e.canon||new Bn,this.executeSelectionSet=ut(function(n){var i,a=n.context.canonizeResults,o=Un(n);o[3]=!a;var s=(i=r.executeSelectionSet).peek.apply(i,o);return s?a?d(d({},s),{result:r.canon.admit(s.result)}):s:(qn(n.context.store,n.enclosingRef.__ref),r.execSelectionSetImpl(n))},{max:this.config.resultCacheMaxSize||te["inMemoryCache.executeSelectionSet"]||5e4,keyArgs:Un,makeCacheKey:function(n,i,a,o){if(rt(a.store))return a.store.makeCacheKey(n,A(i)?i.__ref:i,a.varString,o)}}),this.executeSubSelectedArray=ut(function(n){return qn(n.context.store,n.enclosingRef.__ref),r.execSubSelectedArrayImpl(n)},{max:this.config.resultCacheMaxSize||te["inMemoryCache.executeSubSelectedArray"]||1e4,makeCacheKey:function(n){var i=n.field,a=n.array,o=n.context;if(rt(o.store))return o.store.makeCacheKey(i,a,o.varString)}})}return t.prototype.resetCanon=function(){this.canon=new Bn},t.prototype.diffQueryAgainstStore=function(e){var r=e.store,n=e.query,i=e.rootId,a=i===void 0?"ROOT_QUERY":i,o=e.variables,s=e.returnPartialData,u=s===void 0?!0:s,c=e.canonizeResults,f=c===void 0?this.config.canonizeResults:c,h=this.config.cache.policies;o=d(d({},Vr(Ri(n))),o);var l=Fe(a),p=this.executeSelectionSet({selectionSet:ht(n).selectionSet,objectOrReference:l,enclosingRef:l,context:d({store:r,query:n,policies:h,variables:o,varString:he(o),canonizeResults:f},ya(n,this.config.fragments))}),y;if(p.missing&&(y=[new la(Bu(p.missing),p.missing,n,o)],!u))throw y[0];return{result:p.result,complete:!y,missing:y}},t.prototype.isFresh=function(e,r,n,i){if(rt(i.store)&&this.knownResults.get(e)===n){var a=this.executeSelectionSet.peek(n,r,i,this.canon.isKnown(e));if(a&&e===a.result)return!0}return!1},t.prototype.execSelectionSetImpl=function(e){var r=this,n=e.selectionSet,i=e.objectOrReference,a=e.enclosingRef,o=e.context;if(A(i)&&!o.policies.rootTypenamesById[i.__ref]&&!o.store.has(i.__ref))return{result:this.canon.empty,missing:"Dangling reference to missing ".concat(i.__ref," object")};var s=o.variables,u=o.policies,c=o.store,f=c.getFieldValue(i,"__typename"),h=[],l,p=new ve;this.config.addTypename&&typeof f=="string"&&!u.rootIdsByTypename[f]&&h.push({__typename:f});function y(E,O){var S;return E.missing&&(l=p.merge(l,(S={},S[O]=E.missing,S))),E.result}var m=new Set(n.selections);m.forEach(function(E){var O,S;if(lt(E,s))if(pe(E)){var k=u.readField({fieldName:E.name.value,field:E,variables:o.variables,from:i},o),x=ae(E);k===void 0?Ur.added(E)||(l=p.merge(l,(O={},O[x]="Can't find field '".concat(E.name.value,"' on ").concat(A(i)?i.__ref+" object":"object "+JSON.stringify(i,null,2)),O))):V(k)?k.length>0&&(k=y(r.executeSubSelectedArray({field:E,array:k,enclosingRef:a,context:o}),x)):E.selectionSet?k!=null&&(k=y(r.executeSelectionSet({selectionSet:E.selectionSet,objectOrReference:k,enclosingRef:A(k)?k:a,context:o}),x)):o.canonizeResults&&(k=r.canon.pass(k)),k!==void 0&&h.push((S={},S[x]=k,S))}else{var I=Pt(E,o.lookupFragment);if(!I&&E.kind===T.FRAGMENT_SPREAD)throw $(10,E.name.value);I&&u.fragmentMatches(I,f)&&I.selectionSet.selections.forEach(m.add,m)}});var g=Vt(h),v={result:g,missing:l},b=o.canonizeResults?this.canon.admit(v):Qe(v);return b.result&&this.knownResults.set(b.result,n),b},t.prototype.execSubSelectedArrayImpl=function(e){var r=this,n=e.field,i=e.array,a=e.enclosingRef,o=e.context,s,u=new ve;function c(f,h){var l;return f.missing&&(s=u.merge(s,(l={},l[h]=f.missing,l))),f.result}return n.selectionSet&&(i=i.filter(o.store.canRead)),i=i.map(function(f,h){return f===null?null:V(f)?c(r.executeSubSelectedArray({field:n,array:f,enclosingRef:a,context:o}),h):n.selectionSet?c(r.executeSelectionSet({selectionSet:n.selectionSet,objectOrReference:f,enclosingRef:A(f)?f:a,context:o}),h):(globalThis.__DEV__!==!1&&Uu(o.store,n,f),f)}),{result:o.canonizeResults?this.canon.admit(i):i,missing:s}},t}();function Bu(t){try{JSON.stringify(t,function(e,r){if(typeof r=="string")throw r;return r})}catch(e){return e}}function Uu(t,e,r){if(!e.selectionSet){var n=new Set([r]);n.forEach(function(i){j(i)&&(w(!A(i),11,Mu(t,i),e.name.value),Object.values(i).forEach(n.add,n))})}}var Jr=new pt,zn=new WeakMap;function nt(t){var e=zn.get(t);return e||zn.set(t,e={vars:new Set,dep:zi()}),e}function Wn(t){nt(t).vars.forEach(function(e){return e.forgetCache(t)})}function zu(t){nt(t).vars.forEach(function(e){return e.attachCache(t)})}function Wu(t){var e=new Set,r=new Set,n=function(a){if(arguments.length>0){if(t!==a){t=a,e.forEach(function(u){nt(u).dep.dirty(n),Gu(u)});var o=Array.from(r);r.clear(),o.forEach(function(u){return u(t)})}}else{var s=Jr.getValue();s&&(i(s),nt(s).dep(n))}return t};n.onNextChange=function(a){return r.add(a),function(){r.delete(a)}};var i=n.attachCache=function(a){return e.add(a),nt(a).vars.add(n),n};return n.forgetCache=function(a){return e.delete(a)},n}function Gu(t){t.broadcastWatches&&t.broadcastWatches()}var Gn=Object.create(null);function Kr(t){var e=JSON.stringify(t);return Gn[e]||(Gn[e]=Object.create(null))}function $n(t){var e=Kr(t);return e.keyFieldsFn||(e.keyFieldsFn=function(r,n){var i=function(o,s){return n.readField(s,o)},a=n.keyObject=Xr(t,function(o){var s=Me(n.storeObject,o,i);return s===void 0&&r!==n.storeObject&&B.call(r,o[0])&&(s=Me(r,o,ba)),w(s!==void 0,5,o.join("."),r),s});return"".concat(n.typename,":").concat(JSON.stringify(a))})}function Yn(t){var e=Kr(t);return e.keyArgsFn||(e.keyArgsFn=function(r,n){var i=n.field,a=n.variables,o=n.fieldName,s=Xr(t,function(c){var f=c[0],h=f.charAt(0);if(h==="@"){if(i&&H(i.directives)){var l=f.slice(1),p=i.directives.find(function(v){return v.name.value===l}),y=p&&Mt(p,a);return y&&Me(y,c.slice(1))}return}if(h==="$"){var m=f.slice(1);if(a&&B.call(a,m)){var g=c.slice(0);return g[0]=m,Me(a,g)}return}if(r)return Me(r,c)}),u=JSON.stringify(s);return(r||u!=="{}")&&(o+=":"+u),o})}function Xr(t,e){var r=new ve;return ga(t).reduce(function(n,i){var a,o=e(i);if(o!==void 0){for(var s=i.length-1;s>=0;--s)o=(a={},a[i[s]]=o,a);n=r.merge(n,o)}return n},Object.create(null))}function ga(t){var e=Kr(t);if(!e.paths){var r=e.paths=[],n=[];t.forEach(function(i,a){V(i)?(ga(i).forEach(function(o){return r.push(n.concat(o))}),n.length=0):(n.push(i),V(t[a+1])||(r.push(n.slice(0)),n.length=0))})}return e.paths}function ba(t,e){return t[e]}function Me(t,e,r){return r=r||ba,Ea(e.reduce(function n(i,a){return V(i)?i.map(function(o){return n(o,a)}):i&&r(i,a)},t))}function Ea(t){return j(t)?V(t)?t.map(Ea):Xr(Object.keys(t).sort(),function(e){return Me(t,e)}):t}function Tr(t){return t.args!==void 0?t.args:t.field?Mt(t.field,t.variables):null}var $u=function(){},Hn=function(t,e){return e.fieldName},Jn=function(t,e,r){var n=r.mergeObjects;return n(t,e)},Kn=function(t,e){return e},Yu=function(){function t(e){this.config=e,this.typePolicies=Object.create(null),this.toBeAdded=Object.create(null),this.supertypeMap=new Map,this.fuzzySubtypes=new Map,this.rootIdsByTypename=Object.create(null),this.rootTypenamesById=Object.create(null),this.usingPossibleTypes=!1,this.config=d({dataIdFromObject:ha},e),this.cache=this.config.cache,this.setRootTypename("Query"),this.setRootTypename("Mutation"),this.setRootTypename("Subscription"),e.possibleTypes&&this.addPossibleTypes(e.possibleTypes),e.typePolicies&&this.addTypePolicies(e.typePolicies)}return t.prototype.identify=function(e,r){var n,i=this,a=r&&(r.typename||((n=r.storeObject)===null||n===void 0?void 0:n.__typename))||e.__typename;if(a===this.rootTypenamesById.ROOT_QUERY)return["ROOT_QUERY"];var o=r&&r.storeObject||e,s=d(d({},r),{typename:a,storeObject:o,readField:r&&r.readField||function(){var h=Zr(arguments,o);return i.readField(h,{store:i.cache.data,variables:h.variables})}}),u,c=a&&this.getTypePolicy(a),f=c&&c.keyFn||this.config.dataIdFromObject;return Hr.withValue(!0,function(){for(;f;){var h=f(d(d({},e),o),s);if(V(h))f=$n(h);else{u=h;break}}}),u=u?String(u):void 0,s.keyObject?[u,s.keyObject]:[u]},t.prototype.addTypePolicies=function(e){var r=this;Object.keys(e).forEach(function(n){var i=e[n],a=i.queryType,o=i.mutationType,s=i.subscriptionType,u=X(i,["queryType","mutationType","subscriptionType"]);a&&r.setRootTypename("Query",n),o&&r.setRootTypename("Mutation",n),s&&r.setRootTypename("Subscription",n),B.call(r.toBeAdded,n)?r.toBeAdded[n].push(u):r.toBeAdded[n]=[u]})},t.prototype.updateTypePolicy=function(e,r){var n=this,i=this.getTypePolicy(e),a=r.keyFields,o=r.fields;function s(u,c){u.merge=typeof c=="function"?c:c===!0?Jn:c===!1?Kn:u.merge}s(i,r.merge),i.keyFn=a===!1?$u:V(a)?$n(a):typeof a=="function"?a:i.keyFn,o&&Object.keys(o).forEach(function(u){var c=n.getFieldPolicy(e,u,!0),f=o[u];if(typeof f=="function")c.read=f;else{var h=f.keyArgs,l=f.read,p=f.merge;c.keyFn=h===!1?Hn:V(h)?Yn(h):typeof h=="function"?h:c.keyFn,typeof l=="function"&&(c.read=l),s(c,p)}c.read&&c.merge&&(c.keyFn=c.keyFn||Hn)})},t.prototype.setRootTypename=function(e,r){r===void 0&&(r=e);var n="ROOT_"+e.toUpperCase(),i=this.rootTypenamesById[n];r!==i&&(w(!i||i===e,6,e),i&&delete this.rootIdsByTypename[i],this.rootIdsByTypename[r]=n,this.rootTypenamesById[n]=r)},t.prototype.addPossibleTypes=function(e){var r=this;this.usingPossibleTypes=!0,Object.keys(e).forEach(function(n){r.getSupertypeSet(n,!0),e[n].forEach(function(i){r.getSupertypeSet(i,!0).add(n);var a=i.match(va);(!a||a[0]!==i)&&r.fuzzySubtypes.set(i,new RegExp(i))})})},t.prototype.getTypePolicy=function(e){var r=this;if(!B.call(this.typePolicies,e)){var n=this.typePolicies[e]=Object.create(null);n.fields=Object.create(null);var i=this.supertypeMap.get(e);!i&&this.fuzzySubtypes.size&&(i=this.getSupertypeSet(e,!0),this.fuzzySubtypes.forEach(function(o,s){if(o.test(e)){var u=r.supertypeMap.get(s);u&&u.forEach(function(c){return i.add(c)})}})),i&&i.size&&i.forEach(function(o){var s=r.getTypePolicy(o),u=s.fields,c=X(s,["fields"]);Object.assign(n,c),Object.assign(n.fields,u)})}var a=this.toBeAdded[e];return a&&a.length&&a.splice(0).forEach(function(o){r.updateTypePolicy(e,o)}),this.typePolicies[e]},t.prototype.getFieldPolicy=function(e,r,n){if(e){var i=this.getTypePolicy(e).fields;return i[r]||n&&(i[r]=Object.create(null))}},t.prototype.getSupertypeSet=function(e,r){var n=this.supertypeMap.get(e);return!n&&r&&this.supertypeMap.set(e,n=new Set),n},t.prototype.fragmentMatches=function(e,r,n,i){var a=this;if(!e.typeCondition)return!0;if(!r)return!1;var o=e.typeCondition.name.value;if(r===o)return!0;if(this.usingPossibleTypes&&this.supertypeMap.has(o))for(var s=this.getSupertypeSet(r,!0),u=[s],c=function(y){var m=a.getSupertypeSet(y,!1);m&&m.size&&u.indexOf(m)<0&&u.push(m)},f=!!(n&&this.fuzzySubtypes.size),h=!1,l=0;l<u.length;++l){var p=u[l];if(p.has(o))return s.has(o)||(h&&globalThis.__DEV__!==!1&&w.warn(7,r,o),s.add(o)),!0;p.forEach(c),f&&l===u.length-1&&Sr(e.selectionSet,n,i)&&(f=!1,h=!0,this.fuzzySubtypes.forEach(function(y,m){var g=r.match(y);g&&g[0]===r&&c(m)}))}return!1},t.prototype.hasKeyArgs=function(e,r){var n=this.getFieldPolicy(e,r,!1);return!!(n&&n.keyFn)},t.prototype.getStoreFieldName=function(e){var r=e.typename,n=e.fieldName,i=this.getFieldPolicy(r,n,!1),a,o=i&&i.keyFn;if(o&&r)for(var s={typename:r,fieldName:n,field:e.field||null,variables:e.variables},u=Tr(e);o;){var c=o(u,s);if(V(c))o=Yn(c);else{a=c||n;break}}return a===void 0&&(a=e.field?ps(e.field,e.variables):Ci(n,Tr(e))),a===!1?n:n===ye(a)?a:n+":"+a},t.prototype.readField=function(e,r){var n=e.from;if(n){var i=e.field||e.fieldName;if(i){if(e.typename===void 0){var a=r.store.getFieldValue(n,"__typename");a&&(e.typename=a)}var o=this.getStoreFieldName(e),s=ye(o),u=r.store.getFieldValue(n,o),c=this.getFieldPolicy(e.typename,s,!1),f=c&&c.read;if(f){var h=Xn(this,n,e,r,r.store.getStorage(A(n)?n.__ref:n,o));return Jr.withValue(this.cache,f,[u,h])}return u}}},t.prototype.getReadFunction=function(e,r){var n=this.getFieldPolicy(e,r,!1);return n&&n.read},t.prototype.getMergeFunction=function(e,r,n){var i=this.getFieldPolicy(e,r,!1),a=i&&i.merge;return!a&&n&&(i=this.getTypePolicy(n),a=i&&i.merge),a},t.prototype.runMergeFunction=function(e,r,n,i,a){var o=n.field,s=n.typename,u=n.merge;return u===Jn?_a(i.store)(e,r):u===Kn?r:(i.overwrite&&(e=void 0),u(e,r,Xn(this,void 0,{typename:s,fieldName:o.name.value,field:o,variables:i.variables},i,a||Object.create(null))))},t}();function Xn(t,e,r,n,i){var a=t.getStoreFieldName(r),o=ye(a),s=r.variables||n.variables,u=n.store,c=u.toReference,f=u.canRead;return{args:Tr(r),field:r.field||null,fieldName:o,storeFieldName:a,variables:s,isReference:A,toReference:c,storage:i,cache:t.cache,canRead:f,readField:function(){return t.readField(Zr(arguments,e,s),n)},mergeObjects:_a(n.store)}}function Zr(t,e,r){var n=t[0],i=t[1],a=t.length,o;return typeof n=="string"?o={fieldName:n,from:a>1?i:e}:(o=d({},n),B.call(o,"from")||(o.from=e)),globalThis.__DEV__!==!1&&o.from===void 0&&globalThis.__DEV__!==!1&&w.warn(8,pi(Array.from(t))),o.variables===void 0&&(o.variables=r),o}function _a(t){return function(r,n){if(V(r)||V(n))throw $(9);if(j(r)&&j(n)){var i=t.getFieldValue(r,"__typename"),a=t.getFieldValue(n,"__typename"),o=i&&a&&i!==a;if(o)return n;if(A(r)&&Ne(n))return t.merge(r.__ref,n),r;if(Ne(r)&&A(n))return t.merge(r,n.__ref),n;if(Ne(r)&&Ne(n))return d(d({},r),n)}return n}}function Xt(t,e,r){var n="".concat(e).concat(r),i=t.flavors.get(n);return i||t.flavors.set(n,i=t.clientOnly===e&&t.deferred===r?t:d(d({},t),{clientOnly:e,deferred:r})),i}var Hu=function(){function t(e,r,n){this.cache=e,this.reader=r,this.fragments=n}return t.prototype.writeToStore=function(e,r){var n=this,i=r.query,a=r.result,o=r.dataId,s=r.variables,u=r.overwrite,c=Oe(i),f=Lu();s=d(d({},Vr(c)),s);var h=d(d({store:e,written:Object.create(null),merge:function(p,y){return f.merge(p,y)},variables:s,varString:he(s)},ya(i,this.fragments)),{overwrite:!!u,incomingById:new Map,clientOnly:!1,deferred:!1,flavors:new Map}),l=this.processSelectionSet({result:a||Object.create(null),dataId:o,selectionSet:c.selectionSet,mergeTree:{map:new Map},context:h});if(!A(l))throw $(12,a);return h.incomingById.forEach(function(p,y){var m=p.storeObject,g=p.mergeTree,v=p.fieldNodeSet,b=Fe(y);if(g&&g.map.size){var E=n.applyMerges(g,b,m,h);if(A(E))return;m=E}if(globalThis.__DEV__!==!1&&!h.overwrite){var O=Object.create(null);v.forEach(function(x){x.selectionSet&&(O[x.name.value]=!0)});var S=function(x){return O[ye(x)]===!0},k=function(x){var I=g&&g.map.get(x);return!!(I&&I.info&&I.info.merge)};Object.keys(m).forEach(function(x){S(x)&&!k(x)&&Ju(b,m,x,h.store)})}e.merge(y,m)}),e.retain(l.__ref),l},t.prototype.processSelectionSet=function(e){var r=this,n=e.dataId,i=e.result,a=e.selectionSet,o=e.context,s=e.mergeTree,u=this.cache.policies,c=Object.create(null),f=n&&u.rootTypenamesById[n]||dr(i,a,o.fragmentMap)||n&&o.store.get(n,"__typename");typeof f=="string"&&(c.__typename=f);var h=function(){var E=Zr(arguments,c,o.variables);if(A(E.from)){var O=o.incomingById.get(E.from.__ref);if(O){var S=u.readField(d(d({},E),{from:O.storeObject}),o);if(S!==void 0)return S}}return u.readField(E,o)},l=new Set;this.flattenFields(a,i,o,f).forEach(function(E,O){var S,k=ae(O),x=i[k];if(l.add(O),x!==void 0){var I=u.getStoreFieldName({typename:f,fieldName:O.name.value,field:O,variables:E.variables}),R=Zn(s,I),M=r.processFieldValue(x,O,O.selectionSet?Xt(E,!1,!1):E,R),G=void 0;O.selectionSet&&(A(M)||Ne(M))&&(G=h("__typename",M));var se=u.getMergeFunction(f,O.name.value,G);se?R.info={field:O,typename:f,merge:se}:ei(s,I),c=E.merge(c,(S={},S[I]=M,S))}else globalThis.__DEV__!==!1&&!E.clientOnly&&!E.deferred&&!Ur.added(O)&&!u.getReadFunction(f,O.name.value)&&globalThis.__DEV__!==!1&&w.error(13,ae(O),i)});try{var p=u.identify(i,{typename:f,selectionSet:a,fragmentMap:o.fragmentMap,storeObject:c,readField:h}),y=p[0],m=p[1];n=n||y,m&&(c=o.merge(c,m))}catch(E){if(!n)throw E}if(typeof n=="string"){var g=Fe(n),v=o.written[n]||(o.written[n]=[]);if(v.indexOf(a)>=0||(v.push(a),this.reader&&this.reader.isFresh(i,g,a,o)))return g;var b=o.incomingById.get(n);return b?(b.storeObject=o.merge(b.storeObject,c),b.mergeTree=wr(b.mergeTree,s),l.forEach(function(E){return b.fieldNodeSet.add(E)})):o.incomingById.set(n,{storeObject:c,mergeTree:It(s)?void 0:s,fieldNodeSet:l}),g}return c},t.prototype.processFieldValue=function(e,r,n,i){var a=this;return!r.selectionSet||e===null?globalThis.__DEV__!==!1?Xi(e):e:V(e)?e.map(function(o,s){var u=a.processFieldValue(o,r,n,Zn(i,s));return ei(i,s),u}):this.processSelectionSet({result:e,selectionSet:r.selectionSet,context:n,mergeTree:i})},t.prototype.flattenFields=function(e,r,n,i){i===void 0&&(i=dr(r,e,n.fragmentMap));var a=new Map,o=this.cache.policies,s=new oe(!1);return function u(c,f){var h=s.lookup(c,f.clientOnly,f.deferred);h.visited||(h.visited=!0,c.selections.forEach(function(l){if(lt(l,n.variables)){var p=f.clientOnly,y=f.deferred;if(!(p&&y)&&H(l.directives)&&l.directives.forEach(function(v){var b=v.name.value;if(b==="client"&&(p=!0),b==="defer"){var E=Mt(v,n.variables);(!E||E.if!==!1)&&(y=!0)}}),pe(l)){var m=a.get(l);m&&(p=p&&m.clientOnly,y=y&&m.deferred),a.set(l,Xt(n,p,y))}else{var g=Pt(l,n.lookupFragment);if(!g&&l.kind===T.FRAGMENT_SPREAD)throw $(14,l.name.value);g&&o.fragmentMatches(g,i,r,n.variables)&&u(g.selectionSet,Xt(n,p,y))}}}))}(e,n),a},t.prototype.applyMerges=function(e,r,n,i,a){var o,s=this;if(e.map.size&&!A(n)){var u=!V(n)&&(A(r)||Ne(r))?r:void 0,c=n;u&&!a&&(a=[A(u)?u.__ref:u]);var f,h=function(l,p){return V(l)?typeof p=="number"?l[p]:void 0:i.store.getFieldValue(l,String(p))};e.map.forEach(function(l,p){var y=h(u,p),m=h(c,p);if(m!==void 0){a&&a.push(p);var g=s.applyMerges(l,y,m,i,a);g!==m&&(f=f||new Map,f.set(p,g)),a&&w(a.pop()===p)}}),f&&(n=V(c)?c.slice(0):d({},c),f.forEach(function(l,p){n[p]=l}))}return e.info?this.cache.policies.runMergeFunction(r,n,e.info,i,a&&(o=i.store).getStorage.apply(o,a)):n},t}(),Oa=[];function Zn(t,e){var r=t.map;return r.has(e)||r.set(e,Oa.pop()||{map:new Map}),r.get(e)}function wr(t,e){if(t===e||!e||It(e))return t;if(!t||It(t))return e;var r=t.info&&e.info?d(d({},t.info),e.info):t.info||e.info,n=t.map.size&&e.map.size,i=n?new Map:t.map.size?t.map:e.map,a={info:r,map:i};if(n){var o=new Set(e.map.keys());t.map.forEach(function(s,u){a.map.set(u,wr(s,e.map.get(u))),o.delete(u)}),o.forEach(function(s){a.map.set(s,wr(e.map.get(s),t.map.get(s)))})}return a}function It(t){return!t||!(t.info||t.map.size)}function ei(t,e){var r=t.map,n=r.get(e);n&&It(n)&&(Oa.push(n),r.delete(e))}var ti=new Set;function Ju(t,e,r,n){var i=function(h){var l=n.getFieldValue(h,r);return typeof l=="object"&&l},a=i(t);if(a){var o=i(e);if(o&&!A(a)&&!P(a,o)&&!Object.keys(a).every(function(h){return n.getFieldValue(o,h)!==void 0})){var s=n.getFieldValue(t,"__typename")||n.getFieldValue(e,"__typename"),u=ye(r),c="".concat(s,".").concat(u);if(!ti.has(c)){ti.add(c);var f=[];!V(a)&&!V(o)&&[a,o].forEach(function(h){var l=n.getFieldValue(h,"__typename");typeof l=="string"&&!f.includes(l)&&f.push(l)}),globalThis.__DEV__!==!1&&w.warn(15,u,s,f.length?"either ensure all objects of type "+f.join(" and ")+" have an ID or a custom merge function, or ":"",c,d({},a),d({},o))}}}}var Ku=function(t){re(e,t);function e(r){r===void 0&&(r={});var n=t.call(this)||this;return n.watches=new Set,n.addTypenameTransform=new Wi(Ur),n.assumeImmutableResults=!0,n.makeVar=Wu,n.txCount=0,n.config=Pu(r),n.addTypename=!!n.config.addTypename,n.policies=new Yu({cache:n,dataIdFromObject:n.config.dataIdFromObject,possibleTypes:n.config.possibleTypes,typePolicies:n.config.typePolicies}),n.init(),n}return e.prototype.init=function(){var r=this.data=new ct.Root({policies:this.policies,resultCaching:this.config.resultCaching});this.optimisticData=r.stump,this.resetResultCache()},e.prototype.resetResultCache=function(r){var n=this,i=this.storeReader,a=this.config.fragments;this.storeWriter=new Hu(this,this.storeReader=new qu({cache:this,addTypename:this.addTypename,resultCacheMaxSize:this.config.resultCacheMaxSize,canonizeResults:da(this.config),canon:r?void 0:i&&i.canon,fragments:a}),a),this.maybeBroadcastWatch=ut(function(o,s){return n.broadcastWatch(o,s)},{max:this.config.resultCacheMaxSize||te["inMemoryCache.maybeBroadcastWatch"]||5e3,makeCacheKey:function(o){var s=o.optimistic?n.optimisticData:n.data;if(rt(s)){var u=o.optimistic,c=o.id,f=o.variables;return s.makeCacheKey(o.query,o.callback,he({optimistic:u,id:c,variables:f}))}}}),new Set([this.data.group,this.optimisticData.group]).forEach(function(o){return o.resetCaching()})},e.prototype.restore=function(r){return this.init(),r&&this.data.replace(r),this},e.prototype.extract=function(r){return r===void 0&&(r=!1),(r?this.optimisticData:this.data).extract()},e.prototype.read=function(r){var n=r.returnPartialData,i=n===void 0?!1:n;try{return this.storeReader.diffQueryAgainstStore(d(d({},r),{store:r.optimistic?this.optimisticData:this.data,config:this.config,returnPartialData:i})).result||null}catch(a){if(a instanceof la)return null;throw a}},e.prototype.write=function(r){try{return++this.txCount,this.storeWriter.writeToStore(this.data,r)}finally{!--this.txCount&&r.broadcast!==!1&&this.broadcastWatches()}},e.prototype.modify=function(r){if(B.call(r,"id")&&!r.id)return!1;var n=r.optimistic?this.optimisticData:this.data;try{return++this.txCount,n.modify(r.id||"ROOT_QUERY",r.fields)}finally{!--this.txCount&&r.broadcast!==!1&&this.broadcastWatches()}},e.prototype.diff=function(r){return this.storeReader.diffQueryAgainstStore(d(d({},r),{store:r.optimistic?this.optimisticData:this.data,rootId:r.id||"ROOT_QUERY",config:this.config}))},e.prototype.watch=function(r){var n=this;return this.watches.size||zu(this),this.watches.add(r),r.immediate&&this.maybeBroadcastWatch(r),function(){n.watches.delete(r)&&!n.watches.size&&Wn(n),n.maybeBroadcastWatch.forget(r)}},e.prototype.gc=function(r){var n;he.reset(),be.reset(),this.addTypenameTransform.resetCache(),(n=this.config.fragments)===null||n===void 0||n.resetCaches();var i=this.optimisticData.gc();return r&&!this.txCount&&(r.resetResultCache?this.resetResultCache(r.resetResultIdentities):r.resetResultIdentities&&this.storeReader.resetCanon()),i},e.prototype.retain=function(r,n){return(n?this.optimisticData:this.data).retain(r)},e.prototype.release=function(r,n){return(n?this.optimisticData:this.data).release(r)},e.prototype.identify=function(r){if(A(r))return r.__ref;try{return this.policies.identify(r)[0]}catch(n){globalThis.__DEV__!==!1&&w.warn(n)}},e.prototype.evict=function(r){if(!r.id){if(B.call(r,"id"))return!1;r=d(d({},r),{id:"ROOT_QUERY"})}try{return++this.txCount,this.optimisticData.evict(r,this.data)}finally{!--this.txCount&&r.broadcast!==!1&&this.broadcastWatches()}},e.prototype.reset=function(r){var n=this;return this.init(),he.reset(),r&&r.discardWatches?(this.watches.forEach(function(i){return n.maybeBroadcastWatch.forget(i)}),this.watches.clear(),Wn(this)):this.broadcastWatches(),Promise.resolve()},e.prototype.removeOptimistic=function(r){var n=this.optimisticData.removeLayer(r);n!==this.optimisticData&&(this.optimisticData=n,this.broadcastWatches())},e.prototype.batch=function(r){var n=this,i=r.update,a=r.optimistic,o=a===void 0?!0:a,s=r.removeOptimistic,u=r.onWatchUpdated,c,f=function(l){var p=n,y=p.data,m=p.optimisticData;++n.txCount,l&&(n.data=n.optimisticData=l);try{return c=i(n)}finally{--n.txCount,n.data=y,n.optimisticData=m}},h=new Set;return u&&!this.txCount&&this.broadcastWatches(d(d({},r),{onWatchUpdated:function(l){return h.add(l),!1}})),typeof o=="string"?this.optimisticData=this.optimisticData.addLayer(o,f):o===!1?f(this.data):f(),typeof s=="string"&&(this.optimisticData=this.optimisticData.removeLayer(s)),u&&h.size?(this.broadcastWatches(d(d({},r),{onWatchUpdated:function(l,p){var y=u.call(this,l,p);return y!==!1&&h.delete(l),y}})),h.size&&h.forEach(function(l){return n.maybeBroadcastWatch.dirty(l)})):this.broadcastWatches(r),c},e.prototype.performTransaction=function(r,n){return this.batch({update:r,optimistic:n||n!==null})},e.prototype.transformDocument=function(r){return this.addTypenameToDocument(this.addFragmentsToDocument(r))},e.prototype.fragmentMatches=function(r,n){return this.policies.fragmentMatches(r,n)},e.prototype.lookupFragment=function(r){var n;return((n=this.config.fragments)===null||n===void 0?void 0:n.lookup(r))||null},e.prototype.broadcastWatches=function(r){var n=this;this.txCount||this.watches.forEach(function(i){return n.maybeBroadcastWatch(i,r)})},e.prototype.addFragmentsToDocument=function(r){var n=this.config.fragments;return n?n.transform(r):r},e.prototype.addTypenameToDocument=function(r){return this.addTypename?this.addTypenameTransform.transformDocument(r):r},e.prototype.broadcastWatch=function(r,n){var i=r.lastDiff,a=this.diff(r);n&&(r.optimistic&&typeof n.optimistic=="string"&&(a.fromOptimisticTransaction=!0),n.onWatchUpdated&&n.onWatchUpdated.call(this,r,a,i)===!1)||(!i||!P(i.result,a.result))&&r.callback(r.lastDiff=a,i)},e}(fa);globalThis.__DEV__!==!1&&(Ku.prototype.getMemoryInternals=Ho);var C;(function(t){t[t.loading=1]="loading",t[t.setVariables=2]="setVariables",t[t.fetchMore=3]="fetchMore",t[t.refetch=4]="refetch",t[t.poll=6]="poll",t[t.ready=7]="ready",t[t.error=8]="error"})(C||(C={}));function Ce(t){return t?t<7:!1}var ri=Object.assign,Xu=Object.hasOwnProperty,it=function(t){re(e,t);function e(r){var n=r.queryManager,i=r.queryInfo,a=r.options,o=this,s=e.inactiveOnCreation.getValue();o=t.call(this,function(v){s&&(n.queries.set(o.queryId,i),s=!1);try{var b=v._subscription._observer;b&&!b.error&&(b.error=Zu)}catch{}var E=!o.observers.size;o.observers.add(v);var O=o.last;return O&&O.error?v.error&&v.error(O.error):O&&O.result&&v.next&&v.next(o.maskResult(O.result)),E&&o.reobserve().catch(function(){}),function(){o.observers.delete(v)&&!o.observers.size&&o.tearDownQuery()}})||this,o.observers=new Set,o.subscriptions=new Set,o.dirty=!1,o.queryInfo=i,o.queryManager=n,o.waitForOwnResult=Zt(a.fetchPolicy),o.isTornDown=!1,o.subscribeToMore=o.subscribeToMore.bind(o),o.maskResult=o.maskResult.bind(o);var u=n.defaultOptions.watchQuery,c=u===void 0?{}:u,f=c.fetchPolicy,h=f===void 0?"cache-first":f,l=a.fetchPolicy,p=l===void 0?h:l,y=a.initialFetchPolicy,m=y===void 0?p==="standby"?h:p:y;o.options=d(d({},a),{initialFetchPolicy:m,fetchPolicy:p}),o.queryId=i.queryId||n.generateQueryId();var g=Oe(o.query);return o.queryName=g&&g.name&&g.name.value,o}return Object.defineProperty(e.prototype,"query",{get:function(){return this.lastQuery||this.options.query},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"variables",{get:function(){return this.options.variables},enumerable:!1,configurable:!0}),e.prototype.result=function(){var r=this;return new Promise(function(n,i){var a={next:function(s){n(s),r.observers.delete(a),r.observers.size||r.queryManager.removeQuery(r.queryId),setTimeout(function(){o.unsubscribe()},0)},error:i},o=r.subscribe(a)})},e.prototype.resetDiff=function(){this.queryInfo.resetDiff()},e.prototype.getCurrentFullResult=function(r){r===void 0&&(r=!0);var n=this.getLastResult(!0),i=this.queryInfo.networkStatus||n&&n.networkStatus||C.ready,a=d(d({},n),{loading:Ce(i),networkStatus:i}),o=this.options.fetchPolicy,s=o===void 0?"cache-first":o;if(!(Zt(s)||this.queryManager.getDocumentInfo(this.query).hasForcedResolvers))if(this.waitForOwnResult)this.queryInfo.updateWatch();else{var u=this.queryInfo.getDiff();(u.complete||this.options.returnPartialData)&&(a.data=u.result),P(a.data,{})&&(a.data=void 0),u.complete?(delete a.partial,u.complete&&a.networkStatus===C.loading&&(s==="cache-first"||s==="cache-only")&&(a.networkStatus=C.ready,a.loading=!1)):a.partial=!0,a.networkStatus===C.ready&&(a.error||a.errors)&&(a.networkStatus=C.error),globalThis.__DEV__!==!1&&!u.complete&&!this.options.partialRefetch&&!a.loading&&!a.data&&!a.error&&Sa(u.missing)}return r&&this.updateLastResult(a),a},e.prototype.getCurrentResult=function(r){return r===void 0&&(r=!0),this.maskResult(this.getCurrentFullResult(r))},e.prototype.isDifferentFromLastResult=function(r,n){if(!this.last)return!0;var i=this.queryManager.getDocumentInfo(this.query),a=this.queryManager.dataMasking,o=a?i.nonReactiveQuery:this.query,s=a||i.hasNonreactiveDirective?!ia(o,this.last.result,r,this.variables):!P(this.last.result,r);return s||n&&!P(this.last.variables,n)},e.prototype.getLast=function(r,n){var i=this.last;if(i&&i[r]&&(!n||P(i.variables,this.variables)))return i[r]},e.prototype.getLastResult=function(r){return this.getLast("result",r)},e.prototype.getLastError=function(r){return this.getLast("error",r)},e.prototype.resetLastResults=function(){delete this.last,this.isTornDown=!1},e.prototype.resetQueryStoreErrors=function(){this.queryManager.resetErrors(this.queryId)},e.prototype.refetch=function(r){var n,i={pollInterval:0},a=this.options.fetchPolicy;if(a==="no-cache"?i.fetchPolicy="no-cache":i.fetchPolicy="network-only",globalThis.__DEV__!==!1&&r&&Xu.call(r,"variables")){var o=Ri(this.query),s=o.variableDefinitions;(!s||!s.some(function(u){return u.variable.name.value==="variables"}))&&globalThis.__DEV__!==!1&&w.warn(21,r,((n=o.name)===null||n===void 0?void 0:n.value)||o)}return r&&!P(this.options.variables,r)&&(i.variables=this.options.variables=d(d({},this.options.variables),r)),this.queryInfo.resetLastWrite(),this.reobserve(i,C.refetch)},e.prototype.fetchMore=function(r){var n=this,i=d(d({},r.query?r:d(d(d(d({},this.options),{query:this.options.query}),r),{variables:d(d({},this.options.variables),r.variables)})),{fetchPolicy:"no-cache"});i.query=this.transformDocument(i.query);var a=this.queryManager.generateQueryId();this.lastQuery=r.query?this.transformDocument(this.options.query):i.query;var o=this.queryInfo,s=o.networkStatus;o.networkStatus=C.fetchMore,i.notifyOnNetworkStatusChange&&this.observe();var u=new Set,c=r?.updateQuery,f=this.options.fetchPolicy!=="no-cache";return f||w(c,22),this.queryManager.fetchQuery(a,i,C.fetchMore).then(function(h){if(n.queryManager.removeQuery(a),o.networkStatus===C.fetchMore&&(o.networkStatus=s),f)n.queryManager.cache.batch({update:function(y){var m=r.updateQuery;m?y.updateQuery({query:n.query,variables:n.variables,returnPartialData:!0,optimistic:!1},function(g){return m(g,{fetchMoreResult:h.data,variables:i.variables})}):y.writeQuery({query:i.query,variables:i.variables,data:h.data})},onWatchUpdated:function(y){u.add(y.query)}});else{var l=n.getLast("result"),p=c(l.data,{fetchMoreResult:h.data,variables:i.variables});n.reportResult(d(d({},l),{networkStatus:s,loading:Ce(s),data:p}),n.variables)}return n.maskResult(h)}).finally(function(){f&&!u.has(n.query)&&n.reobserveCacheFirst()})},e.prototype.subscribeToMore=function(r){var n=this,i=this.queryManager.startGraphQLSubscription({query:r.document,variables:r.variables,context:r.context}).subscribe({next:function(a){var o=r.updateQuery;o&&n.updateQuery(function(s,u){return o(s,d({subscriptionData:a},u))})},error:function(a){if(r.onError){r.onError(a);return}globalThis.__DEV__!==!1&&w.error(23,a)}});return this.subscriptions.add(i),function(){n.subscriptions.delete(i)&&i.unsubscribe()}},e.prototype.setOptions=function(r){return this.reobserve(r)},e.prototype.silentSetOptions=function(r){var n=Se(this.options,r||{});ri(this.options,n)},e.prototype.setVariables=function(r){return P(this.variables,r)?this.observers.size?this.result():Promise.resolve():(this.options.variables=r,this.observers.size?this.reobserve({fetchPolicy:this.options.initialFetchPolicy,variables:r},C.setVariables):Promise.resolve())},e.prototype.updateQuery=function(r){var n=this.queryManager,i=n.cache.diff({query:this.options.query,variables:this.variables,returnPartialData:!0,optimistic:!1}),a=i.result,o=i.complete,s=r(a,{variables:this.variables,complete:!!o,previousData:a});s&&(n.cache.writeQuery({query:this.options.query,data:s,variables:this.variables}),n.broadcastQueries())},e.prototype.startPolling=function(r){this.options.pollInterval=r,this.updatePolling()},e.prototype.stopPolling=function(){this.options.pollInterval=0,this.updatePolling()},e.prototype.applyNextFetchPolicy=function(r,n){if(n.nextFetchPolicy){var i=n.fetchPolicy,a=i===void 0?"cache-first":i,o=n.initialFetchPolicy,s=o===void 0?a:o;a==="standby"||(typeof n.nextFetchPolicy=="function"?n.fetchPolicy=n.nextFetchPolicy(a,{reason:r,options:n,observable:this,initialFetchPolicy:s}):r==="variables-changed"?n.fetchPolicy=s:n.fetchPolicy=n.nextFetchPolicy)}return n.fetchPolicy},e.prototype.fetch=function(r,n,i){var a=this.queryManager.getOrCreateQuery(this.queryId);return a.setObservableQuery(this),this.queryManager.fetchConcastWithInfo(a,r,n,i)},e.prototype.updatePolling=function(){var r=this;if(!this.queryManager.ssrMode){var n=this,i=n.pollingInfo,a=n.options.pollInterval;if(!a||!this.hasObservers()){i&&(clearTimeout(i.timeout),delete this.pollingInfo);return}if(!(i&&i.interval===a)){w(a,24);var o=i||(this.pollingInfo={});o.interval=a;var s=function(){var c,f;r.pollingInfo&&(!Ce(r.queryInfo.networkStatus)&&!(!((f=(c=r.options).skipPollAttempt)===null||f===void 0)&&f.call(c))?r.reobserve({fetchPolicy:r.options.initialFetchPolicy==="no-cache"?"no-cache":"network-only"},C.poll).then(u,u):u())},u=function(){var c=r.pollingInfo;c&&(clearTimeout(c.timeout),c.timeout=setTimeout(s,c.interval))};u()}}},e.prototype.updateLastResult=function(r,n){n===void 0&&(n=this.variables);var i=this.getLastError();return i&&this.last&&!P(n,this.last.variables)&&(i=void 0),this.last=d({result:this.queryManager.assumeImmutableResults?r:Xi(r),variables:n},i?{error:i}:null)},e.prototype.reobserveAsConcast=function(r,n){var i=this;this.isTornDown=!1;var a=n===C.refetch||n===C.fetchMore||n===C.poll,o=this.options.variables,s=this.options.fetchPolicy,u=Se(this.options,r||{}),c=a?u:ri(this.options,u),f=this.transformDocument(c.query);this.lastQuery=f,a||(this.updatePolling(),r&&r.variables&&!P(r.variables,o)&&c.fetchPolicy!=="standby"&&(c.fetchPolicy===s||typeof c.nextFetchPolicy=="function")&&(this.applyNextFetchPolicy("variables-changed",c),n===void 0&&(n=C.setVariables))),this.waitForOwnResult&&(this.waitForOwnResult=Zt(c.fetchPolicy));var h=function(){i.concast===y&&(i.waitForOwnResult=!1)},l=c.variables&&d({},c.variables),p=this.fetch(c,n,f),y=p.concast,m=p.fromLink,g={next:function(v){P(i.variables,l)&&(h(),i.reportResult(v,l))},error:function(v){P(i.variables,l)&&(ra(v)||(v=new ne({networkError:v})),h(),i.reportError(v,l))}};return!a&&(m||!this.concast)&&(this.concast&&this.observer&&this.concast.removeObserver(this.observer),this.concast=y,this.observer=g),y.addObserver(g),y},e.prototype.reobserve=function(r,n){return Bs(this.reobserveAsConcast(r,n).promise.then(this.maskResult))},e.prototype.resubscribeAfterError=function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=this.last;this.resetLastResults();var a=this.subscribe.apply(this,r);return this.last=i,a},e.prototype.observe=function(){this.reportResult(this.getCurrentFullResult(!1),this.variables)},e.prototype.reportResult=function(r,n){var i=this.getLastError(),a=this.isDifferentFromLastResult(r,n);(i||!r.partial||this.options.returnPartialData)&&this.updateLastResult(r,n),(i||a)&&tt(this.observers,"next",this.maskResult(r))},e.prototype.reportError=function(r,n){var i=d(d({},this.getLastResult()),{error:r,errors:r.graphQLErrors,networkStatus:C.error,loading:!1});this.updateLastResult(i,n),tt(this.observers,"error",this.last.error=r)},e.prototype.hasObservers=function(){return this.observers.size>0},e.prototype.tearDownQuery=function(){this.isTornDown||(this.concast&&this.observer&&(this.concast.removeObserver(this.observer),delete this.concast,delete this.observer),this.stopPolling(),this.subscriptions.forEach(function(r){return r.unsubscribe()}),this.subscriptions.clear(),this.queryManager.stopQuery(this.queryId),this.observers.clear(),this.isTornDown=!0)},e.prototype.transformDocument=function(r){return this.queryManager.transform(r)},e.prototype.maskResult=function(r){return r&&"data"in r?d(d({},r),{data:this.queryManager.maskOperation({document:this.query,data:r.data,fetchPolicy:this.options.fetchPolicy,id:this.queryId})}):r},e.prototype.resetNotifications=function(){this.cancelNotifyTimeout(),this.dirty=!1},e.prototype.cancelNotifyTimeout=function(){this.notifyTimeout&&(clearTimeout(this.notifyTimeout),this.notifyTimeout=void 0)},e.prototype.scheduleNotify=function(){var r=this;this.dirty||(this.dirty=!0,this.notifyTimeout||(this.notifyTimeout=setTimeout(function(){return r.notify()},0)))},e.prototype.notify=function(){if(this.cancelNotifyTimeout(),this.dirty&&(this.options.fetchPolicy=="cache-only"||this.options.fetchPolicy=="cache-and-network"||!Ce(this.queryInfo.networkStatus))){var r=this.queryInfo.getDiff();r.fromOptimisticTransaction?this.observe():this.reobserveCacheFirst()}this.dirty=!1},e.prototype.reobserveCacheFirst=function(){var r=this.options,n=r.fetchPolicy,i=r.nextFetchPolicy;return n==="cache-and-network"||n==="network-only"?this.reobserve({fetchPolicy:"cache-first",nextFetchPolicy:function(a,o){return this.nextFetchPolicy=i,typeof this.nextFetchPolicy=="function"?this.nextFetchPolicy(a,o):n}}):this.reobserve()},e.inactiveOnCreation=new pt,e}(L);Zi(it);function Zu(t){globalThis.__DEV__!==!1&&w.error(25,t.message,t.stack)}function Sa(t){globalThis.__DEV__!==!1&&t&&globalThis.__DEV__!==!1&&w.debug(26,t)}function Zt(t){return t==="network-only"||t==="no-cache"||t==="standby"}var Re=new(Te?WeakMap:Map);function er(t,e){var r=t[e];typeof r=="function"&&(t[e]=function(){return Re.set(t,(Re.get(t)+1)%1e15),r.apply(this,arguments)})}var tr=function(){function t(e,r){r===void 0&&(r=e.generateQueryId()),this.queryId=r,this.document=null,this.lastRequestId=1,this.stopped=!1,this.observableQuery=null;var n=this.cache=e.cache;Re.has(n)||(Re.set(n,0),er(n,"evict"),er(n,"modify"),er(n,"reset"))}return t.prototype.init=function(e){var r=e.networkStatus||C.loading;return this.variables&&this.networkStatus!==C.loading&&!P(this.variables,e.variables)&&(r=C.setVariables),P(e.variables,this.variables)||(this.lastDiff=void 0,this.cancel()),Object.assign(this,{document:e.document,variables:e.variables,networkError:null,graphQLErrors:this.graphQLErrors||[],networkStatus:r}),e.observableQuery&&this.setObservableQuery(e.observableQuery),e.lastRequestId&&(this.lastRequestId=e.lastRequestId),this},t.prototype.resetDiff=function(){this.lastDiff=void 0},t.prototype.getDiff=function(){var e=this.getDiffOptions();if(this.lastDiff&&P(e,this.lastDiff.options))return this.lastDiff.diff;this.updateWatch(this.variables);var r=this.observableQuery;if(r&&r.options.fetchPolicy==="no-cache")return{complete:!1};var n=this.cache.diff(e);return this.updateLastDiff(n,e),n},t.prototype.updateLastDiff=function(e,r){this.lastDiff=e?{diff:e,options:r||this.getDiffOptions()}:void 0},t.prototype.getDiffOptions=function(e){var r;return e===void 0&&(e=this.variables),{query:this.document,variables:e,returnPartialData:!0,optimistic:!0,canonizeResults:(r=this.observableQuery)===null||r===void 0?void 0:r.options.canonizeResults}},t.prototype.setDiff=function(e){var r,n,i=this.lastDiff&&this.lastDiff.diff;e&&!e.complete&&(!((r=this.observableQuery)===null||r===void 0)&&r.getLastError())||(this.updateLastDiff(e),P(i&&i.result,e&&e.result)||(n=this.observableQuery)===null||n===void 0||n.scheduleNotify())},t.prototype.setObservableQuery=function(e){e!==this.observableQuery&&(this.observableQuery=e,e&&(e.queryInfo=this))},t.prototype.stop=function(){var e;if(!this.stopped){this.stopped=!0,(e=this.observableQuery)===null||e===void 0||e.resetNotifications(),this.cancel();var r=this.observableQuery;r&&r.stopPolling()}},t.prototype.cancel=function(){var e;(e=this.cancelWatch)===null||e===void 0||e.call(this),this.cancelWatch=void 0},t.prototype.updateWatch=function(e){var r=this;e===void 0&&(e=this.variables);var n=this.observableQuery;if(!(n&&n.options.fetchPolicy==="no-cache")){var i=d(d({},this.getDiffOptions(e)),{watcher:this,callback:function(a){return r.setDiff(a)}});(!this.lastWatch||!P(i,this.lastWatch))&&(this.cancel(),this.cancelWatch=this.cache.watch(this.lastWatch=i))}},t.prototype.resetLastWrite=function(){this.lastWrite=void 0},t.prototype.shouldWrite=function(e,r){var n=this.lastWrite;return!(n&&n.dmCount===Re.get(this.cache)&&P(r,n.variables)&&P(e.data,n.result.data))},t.prototype.markResult=function(e,r,n,i){var a=this,o,s=new ve,u=H(e.errors)?e.errors.slice(0):[];if((o=this.observableQuery)===null||o===void 0||o.resetNotifications(),"incremental"in e&&H(e.incremental)){var c=ea(this.getDiff().result,e);e.data=c}else if("hasNext"in e&&e.hasNext){var f=this.getDiff();e.data=s.merge(f.result,e.data)}this.graphQLErrors=u,n.fetchPolicy==="no-cache"?this.updateLastDiff({result:e.data,complete:!0},this.getDiffOptions(n.variables)):i!==0&&(kr(e,n.errorPolicy)?this.cache.performTransaction(function(h){if(a.shouldWrite(e,n.variables))h.writeQuery({query:r,data:e.data,variables:n.variables,overwrite:i===1}),a.lastWrite={result:e,variables:n.variables,dmCount:Re.get(a.cache)};else if(a.lastDiff&&a.lastDiff.diff.complete){e.data=a.lastDiff.diff.result;return}var l=a.getDiffOptions(n.variables),p=h.diff(l);!a.stopped&&P(a.variables,n.variables)&&a.updateWatch(n.variables),a.updateLastDiff(p,l),p.complete&&(e.data=p.result)}):this.lastWrite=void 0)},t.prototype.markReady=function(){return this.networkError=null,this.networkStatus=C.ready},t.prototype.markError=function(e){var r;return this.networkStatus=C.error,this.lastWrite=void 0,(r=this.observableQuery)===null||r===void 0||r.resetNotifications(),e.graphQLErrors&&(this.graphQLErrors=e.graphQLErrors),e.networkError&&(this.networkError=e.networkError),e},t}();function kr(t,e){e===void 0&&(e="none");var r=e==="ignore"||e==="all",n=!_t(t);return!n&&r&&t.data&&(n=!0),n}var ec=Object.prototype.hasOwnProperty,ni=Object.create(null),tc=function(){function t(e){var r=this;this.clientAwareness={},this.queries=new Map,this.fetchCancelFns=new Map,this.transformCache=new Lr(te["queryManager.getDocumentInfo"]||2e3),this.queryIdCounter=1,this.requestIdCounter=1,this.mutationIdCounter=1,this.inFlightLinkObservables=new oe(!1),this.noCacheWarningsByQueryId=new Set;var n=new Wi(function(a){return r.cache.transformDocument(a)},{cache:!1});this.cache=e.cache,this.link=e.link,this.defaultOptions=e.defaultOptions,this.queryDeduplication=e.queryDeduplication,this.clientAwareness=e.clientAwareness,this.localState=e.localState,this.ssrMode=e.ssrMode,this.assumeImmutableResults=e.assumeImmutableResults,this.dataMasking=e.dataMasking;var i=e.documentTransform;this.documentTransform=i?n.concat(i).concat(n):n,this.defaultContext=e.defaultContext||Object.create(null),(this.onBroadcast=e.onBroadcast)&&(this.mutationStore=Object.create(null))}return t.prototype.stop=function(){var e=this;this.queries.forEach(function(r,n){e.stopQueryNoBroadcast(n)}),this.cancelPendingFetches($(27))},t.prototype.cancelPendingFetches=function(e){this.fetchCancelFns.forEach(function(r){return r(e)}),this.fetchCancelFns.clear()},t.prototype.mutate=function(e){return ce(this,arguments,void 0,function(r){var n,i,a,o,s,u,c,f=r.mutation,h=r.variables,l=r.optimisticResponse,p=r.updateQueries,y=r.refetchQueries,m=y===void 0?[]:y,g=r.awaitRefetchQueries,v=g===void 0?!1:g,b=r.update,E=r.onQueryUpdated,O=r.fetchPolicy,S=O===void 0?((u=this.defaultOptions.mutate)===null||u===void 0?void 0:u.fetchPolicy)||"network-only":O,k=r.errorPolicy,x=k===void 0?((c=this.defaultOptions.mutate)===null||c===void 0?void 0:c.errorPolicy)||"none":k,I=r.keepRootFields,R=r.context;return fe(this,function(M){switch(M.label){case 0:return w(f,28),w(S==="network-only"||S==="no-cache",29),n=this.generateMutationId(),f=this.cache.transformForLink(this.transform(f)),i=this.getDocumentInfo(f).hasClientExports,h=this.getVariables(f,h),i?[4,this.localState.addExportedVariables(f,h,R)]:[3,2];case 1:h=M.sent(),M.label=2;case 2:return a=this.mutationStore&&(this.mutationStore[n]={mutation:f,variables:h,loading:!0,error:null}),o=l&&this.markMutationOptimistic(l,{mutationId:n,document:f,variables:h,fetchPolicy:S,errorPolicy:x,context:R,updateQueries:p,update:b,keepRootFields:I}),this.broadcastQueries(),s=this,[2,new Promise(function(G,se){return Yt(s.getObservableFromLink(f,d(d({},R),{optimisticResponse:o?l:void 0}),h,{},!1),function(Q){if(_t(Q)&&x==="none")throw new ne({graphQLErrors:gr(Q)});a&&(a.loading=!1,a.error=null);var ee=d({},Q);return typeof m=="function"&&(m=m(ee)),x==="ignore"&&_t(ee)&&delete ee.errors,s.markMutationResult({mutationId:n,result:ee,document:f,variables:h,fetchPolicy:S,errorPolicy:x,context:R,update:b,updateQueries:p,awaitRefetchQueries:v,refetchQueries:m,removeOptimistic:o?n:void 0,onQueryUpdated:E,keepRootFields:I})}).subscribe({next:function(Q){s.broadcastQueries(),(!("hasNext"in Q)||Q.hasNext===!1)&&G(d(d({},Q),{data:s.maskOperation({document:f,data:Q.data,fetchPolicy:S,id:n})}))},error:function(Q){a&&(a.loading=!1,a.error=Q),o&&s.cache.removeOptimistic(n),s.broadcastQueries(),se(Q instanceof ne?Q:new ne({networkError:Q}))}})})]}})})},t.prototype.markMutationResult=function(e,r){var n=this;r===void 0&&(r=this.cache);var i=e.result,a=[],o=e.fetchPolicy==="no-cache";if(!o&&kr(i,e.errorPolicy)){if(Ae(i)||a.push({result:i.data,dataId:"ROOT_MUTATION",query:e.document,variables:e.variables}),Ae(i)&&H(i.incremental)){var s=r.diff({id:"ROOT_MUTATION",query:this.getDocumentInfo(e.document).asQuery,variables:e.variables,optimistic:!1,returnPartialData:!0}),u=void 0;s.result&&(u=ea(s.result,i)),typeof u<"u"&&(i.data=u,a.push({result:u,dataId:"ROOT_MUTATION",query:e.document,variables:e.variables}))}var c=e.updateQueries;c&&this.queries.forEach(function(h,l){var p=h.observableQuery,y=p&&p.queryName;if(!(!y||!ec.call(c,y))){var m=c[y],g=n.queries.get(l),v=g.document,b=g.variables,E=r.diff({query:v,variables:b,returnPartialData:!0,optimistic:!1}),O=E.result,S=E.complete;if(S&&O){var k=m(O,{mutationResult:i,queryName:v&&Xe(v)||void 0,queryVariables:b});k&&a.push({result:k,dataId:"ROOT_QUERY",query:v,variables:b})}}})}if(a.length>0||(e.refetchQueries||"").length>0||e.update||e.onQueryUpdated||e.removeOptimistic){var f=[];if(this.refetchQueries({updateCache:function(h){o||a.forEach(function(m){return h.write(m)});var l=e.update,p=!$s(i)||Ae(i)&&!i.hasNext;if(l){if(!o){var y=h.diff({id:"ROOT_MUTATION",query:n.getDocumentInfo(e.document).asQuery,variables:e.variables,optimistic:!1,returnPartialData:!0});y.complete&&(i=d(d({},i),{data:y.result}),"incremental"in i&&delete i.incremental,"hasNext"in i&&delete i.hasNext)}p&&l(h,i,{context:e.context,variables:e.variables})}!o&&!e.keepRootFields&&p&&h.modify({id:"ROOT_MUTATION",fields:function(m,g){var v=g.fieldName,b=g.DELETE;return v==="__typename"?m:b}})},include:e.refetchQueries,optimistic:!1,removeOptimistic:e.removeOptimistic,onQueryUpdated:e.onQueryUpdated||null}).forEach(function(h){return f.push(h)}),e.awaitRefetchQueries||e.onQueryUpdated)return Promise.all(f).then(function(){return i})}return Promise.resolve(i)},t.prototype.markMutationOptimistic=function(e,r){var n=this,i=typeof e=="function"?e(r.variables,{IGNORE:ni}):e;return i===ni?!1:(this.cache.recordOptimisticTransaction(function(a){try{n.markMutationResult(d(d({},r),{result:{data:i}}),a)}catch(o){globalThis.__DEV__!==!1&&w.error(o)}},r.mutationId),!0)},t.prototype.fetchQuery=function(e,r,n){return this.fetchConcastWithInfo(this.getOrCreateQuery(e),r,n).concast.promise},t.prototype.getQueryStore=function(){var e=Object.create(null);return this.queries.forEach(function(r,n){e[n]={variables:r.variables,networkStatus:r.networkStatus,networkError:r.networkError,graphQLErrors:r.graphQLErrors}}),e},t.prototype.resetErrors=function(e){var r=this.queries.get(e);r&&(r.networkError=void 0,r.graphQLErrors=[])},t.prototype.transform=function(e){return this.documentTransform.transformDocument(e)},t.prototype.getDocumentInfo=function(e){var r=this.transformCache;if(!r.has(e)){var n={hasClientExports:Io(e),hasForcedResolvers:this.localState.shouldForceResolvers(e),hasNonreactiveDirective:ot(["nonreactive"],e),nonReactiveQuery:Cs(e),clientQuery:this.localState.clientQuery(e),serverQuery:$i([{name:"client",remove:!0},{name:"connection"},{name:"nonreactive"},{name:"unmask"}],e),defaultVars:Vr(Oe(e)),asQuery:d(d({},e),{definitions:e.definitions.map(function(i){return i.kind==="OperationDefinition"&&i.operation!=="query"?d(d({},i),{operation:"query"}):i})})};r.set(e,n)}return r.get(e)},t.prototype.getVariables=function(e,r){return d(d({},this.getDocumentInfo(e).defaultVars),r)},t.prototype.watchQuery=function(e){var r=this.transform(e.query);e=d(d({},e),{variables:this.getVariables(r,e.variables)}),typeof e.notifyOnNetworkStatusChange>"u"&&(e.notifyOnNetworkStatusChange=!1);var n=new tr(this),i=new it({queryManager:this,queryInfo:n,options:e});return i.lastQuery=r,it.inactiveOnCreation.getValue()||this.queries.set(i.queryId,n),n.init({document:r,observableQuery:i,variables:i.variables}),i},t.prototype.query=function(e,r){var n=this;r===void 0&&(r=this.generateQueryId()),w(e.query,30),w(e.query.kind==="Document",31),w(!e.returnPartialData,32),w(!e.pollInterval,33);var i=this.transform(e.query);return this.fetchQuery(r,d(d({},e),{query:i})).then(function(a){return a&&d(d({},a),{data:n.maskOperation({document:i,data:a.data,fetchPolicy:e.fetchPolicy,id:r})})}).finally(function(){return n.stopQuery(r)})},t.prototype.generateQueryId=function(){return String(this.queryIdCounter++)},t.prototype.generateRequestId=function(){return this.requestIdCounter++},t.prototype.generateMutationId=function(){return String(this.mutationIdCounter++)},t.prototype.stopQueryInStore=function(e){this.stopQueryInStoreNoBroadcast(e),this.broadcastQueries()},t.prototype.stopQueryInStoreNoBroadcast=function(e){var r=this.queries.get(e);r&&r.stop()},t.prototype.clearStore=function(e){return e===void 0&&(e={discardWatches:!0}),this.cancelPendingFetches($(34)),this.queries.forEach(function(r){r.observableQuery?r.networkStatus=C.loading:r.stop()}),this.mutationStore&&(this.mutationStore=Object.create(null)),this.cache.reset(e)},t.prototype.getObservableQueries=function(e){var r=this;e===void 0&&(e="active");var n=new Map,i=new Map,a=new Map,o=new Set;return Array.isArray(e)&&e.forEach(function(s){if(typeof s=="string")i.set(s,s),a.set(s,!1);else if(ns(s)){var u=be(r.transform(s));i.set(u,Xe(s)),a.set(u,!1)}else j(s)&&s.query&&o.add(s)}),this.queries.forEach(function(s,u){var c=s.observableQuery,f=s.document;if(c){if(e==="all"){n.set(u,c);return}var h=c.queryName,l=c.options.fetchPolicy;if(l==="standby"||e==="active"&&!c.hasObservers())return;(e==="active"||h&&a.has(h)||f&&a.has(be(f)))&&(n.set(u,c),h&&a.set(h,!0),f&&a.set(be(f),!0))}}),o.size&&o.forEach(function(s){var u=or("legacyOneTimeQuery"),c=r.getOrCreateQuery(u).init({document:s.query,variables:s.variables}),f=new it({queryManager:r,queryInfo:c,options:d(d({},s),{fetchPolicy:"network-only"})});w(f.queryId===u),c.setObservableQuery(f),n.set(u,f)}),globalThis.__DEV__!==!1&&a.size&&a.forEach(function(s,u){if(!s){var c=i.get(u);c?globalThis.__DEV__!==!1&&w.warn(35,c):globalThis.__DEV__!==!1&&w.warn(36)}}),n},t.prototype.reFetchObservableQueries=function(e){var r=this;e===void 0&&(e=!1);var n=[];return this.getObservableQueries(e?"all":"active").forEach(function(i,a){var o=i.options.fetchPolicy;i.resetLastResults(),(e||o!=="standby"&&o!=="cache-only")&&n.push(i.refetch()),(r.queries.get(a)||i.queryInfo).setDiff(null)}),this.broadcastQueries(),Promise.all(n)},t.prototype.startGraphQLSubscription=function(e){var r=this,n=e.query,i=e.variables,a=e.fetchPolicy,o=e.errorPolicy,s=o===void 0?"none":o,u=e.context,c=u===void 0?{}:u,f=e.extensions,h=f===void 0?{}:f;n=this.transform(n),i=this.getVariables(n,i);var l=function(y){return r.getObservableFromLink(n,c,y,h).map(function(m){a!=="no-cache"&&(kr(m,s)&&r.cache.write({query:n,result:m.data,dataId:"ROOT_SUBSCRIPTION",variables:y}),r.broadcastQueries());var g=_t(m),v=cu(m);if(g||v){var b={};if(g&&(b.graphQLErrors=m.errors),v&&(b.protocolErrors=m.extensions[Yr]),s==="none"||v)throw new ne(b)}return s==="ignore"&&delete m.errors,m})};if(this.getDocumentInfo(n).hasClientExports){var p=this.localState.addExportedVariables(n,i,c).then(l);return new L(function(y){var m=null;return p.then(function(g){return m=g.subscribe(y)},y.error),function(){return m&&m.unsubscribe()}})}return l(i)},t.prototype.stopQuery=function(e){this.stopQueryNoBroadcast(e),this.broadcastQueries()},t.prototype.stopQueryNoBroadcast=function(e){this.stopQueryInStoreNoBroadcast(e),this.removeQuery(e)},t.prototype.removeQuery=function(e){var r;this.fetchCancelFns.delete(e),this.queries.has(e)&&((r=this.queries.get(e))===null||r===void 0||r.stop(),this.queries.delete(e))},t.prototype.broadcastQueries=function(){this.onBroadcast&&this.onBroadcast(),this.queries.forEach(function(e){var r;return(r=e.observableQuery)===null||r===void 0?void 0:r.notify()})},t.prototype.getLocalState=function(){return this.localState},t.prototype.getObservableFromLink=function(e,r,n,i,a){var o=this,s;a===void 0&&(a=(s=r?.queryDeduplication)!==null&&s!==void 0?s:this.queryDeduplication);var u,c=this.getDocumentInfo(e),f=c.serverQuery,h=c.clientQuery;if(f){var l=this,p=l.inFlightLinkObservables,y=l.link,m={query:f,variables:n,operationName:Xe(f)||void 0,context:this.prepareContext(d(d({},r),{forceFetch:!a})),extensions:i};if(r=m.context,a){var g=be(f),v=he(n),b=p.lookup(g,v);if(u=b.observable,!u){var E=new De([br(y,m)]);u=b.observable=E,E.beforeNext(function O(S,k){S==="next"&&"hasNext"in k&&k.hasNext?E.beforeNext(O):p.remove(g,v)})}}else u=new De([br(y,m)])}else u=new De([L.of({data:{}})]),r=this.prepareContext(r);return h&&(u=Yt(u,function(O){return o.localState.runResolvers({document:h,remoteResult:O,context:r,variables:n})})),u},t.prototype.getResultsFromLink=function(e,r,n){var i=e.lastRequestId=this.generateRequestId(),a=this.cache.transformForLink(n.query);return Yt(this.getObservableFromLink(a,n.context,n.variables),function(o){var s=gr(o),u=s.length>0,c=n.errorPolicy;if(i>=e.lastRequestId){if(u&&c==="none")throw e.markError(new ne({graphQLErrors:s}));e.markResult(o,a,n,r),e.markReady()}var f={data:o.data,loading:!1,networkStatus:C.ready};return u&&c==="none"&&(f.data=void 0),u&&c!=="ignore"&&(f.errors=s,f.networkStatus=C.error),f},function(o){var s=ra(o)?o:new ne({networkError:o});throw i>=e.lastRequestId&&e.markError(s),s})},t.prototype.fetchConcastWithInfo=function(e,r,n,i){var a=this;n===void 0&&(n=C.loading),i===void 0&&(i=r.query);var o=this.getVariables(i,r.variables),s=this.defaultOptions.watchQuery,u=r.fetchPolicy,c=u===void 0?s&&s.fetchPolicy||"cache-first":u,f=r.errorPolicy,h=f===void 0?s&&s.errorPolicy||"none":f,l=r.returnPartialData,p=l===void 0?!1:l,y=r.notifyOnNetworkStatusChange,m=y===void 0?!1:y,g=r.context,v=g===void 0?{}:g,b=Object.assign({},r,{query:i,variables:o,fetchPolicy:c,errorPolicy:h,returnPartialData:p,notifyOnNetworkStatusChange:m,context:v}),E=function(I){b.variables=I;var R=a.fetchQueryByPolicy(e,b,n);return b.fetchPolicy!=="standby"&&R.sources.length>0&&e.observableQuery&&e.observableQuery.applyNextFetchPolicy("after-fetch",r),R},O=function(){return a.fetchCancelFns.delete(e.queryId)};this.fetchCancelFns.set(e.queryId,function(I){O(),setTimeout(function(){return S.cancel(I)})});var S,k;if(this.getDocumentInfo(b.query).hasClientExports)S=new De(this.localState.addExportedVariables(b.query,b.variables,b.context).then(E).then(function(I){return I.sources})),k=!0;else{var x=E(b.variables);k=x.fromLink,S=new De(x.sources)}return S.promise.then(O,O),{concast:S,fromLink:k}},t.prototype.refetchQueries=function(e){var r=this,n=e.updateCache,i=e.include,a=e.optimistic,o=a===void 0?!1:a,s=e.removeOptimistic,u=s===void 0?o?or("refetchQueries"):void 0:s,c=e.onQueryUpdated,f=new Map;i&&this.getObservableQueries(i).forEach(function(l,p){f.set(p,{oq:l,lastDiff:(r.queries.get(p)||l.queryInfo).getDiff()})});var h=new Map;return n&&this.cache.batch({update:n,optimistic:o&&u||!1,removeOptimistic:u,onWatchUpdated:function(l,p,y){var m=l.watcher instanceof tr&&l.watcher.observableQuery;if(m){if(c){f.delete(m.queryId);var g=c(m,p,y);return g===!0&&(g=m.refetch()),g!==!1&&h.set(m,g),g}c!==null&&f.set(m.queryId,{oq:m,lastDiff:y,diff:p})}}}),f.size&&f.forEach(function(l,p){var y=l.oq,m=l.lastDiff,g=l.diff,v;c&&(g||(g=r.cache.diff(y.queryInfo.getDiffOptions())),v=c(y,g,m)),(!c||v===!0)&&(v=y.refetch()),v!==!1&&h.set(y,v),p.indexOf("legacyOneTimeQuery")>=0&&r.stopQueryNoBroadcast(p)}),u&&this.cache.removeOptimistic(u),h},t.prototype.maskOperation=function(e){var r,n,i,a=e.document,o=e.data;if(globalThis.__DEV__!==!1){var s=e.fetchPolicy,u=e.id,c=(r=Oe(a))===null||r===void 0?void 0:r.operation,f=((n=c?.[0])!==null&&n!==void 0?n:"o")+u;this.dataMasking&&s==="no-cache"&&!Vo(a)&&!this.noCacheWarningsByQueryId.has(f)&&(this.noCacheWarningsByQueryId.add(f),globalThis.__DEV__!==!1&&w.warn(37,(i=Xe(a))!==null&&i!==void 0?i:"Unnamed ".concat(c??"operation")))}return this.dataMasking?Au(o,a,this.cache):o},t.prototype.maskFragment=function(e){var r=e.data,n=e.fragment,i=e.fragmentName;return this.dataMasking?ca(r,n,this.cache,i):r},t.prototype.fetchQueryByPolicy=function(e,r,n){var i=this,a=r.query,o=r.variables,s=r.fetchPolicy,u=r.refetchWritePolicy,c=r.errorPolicy,f=r.returnPartialData,h=r.context,l=r.notifyOnNetworkStatusChange,p=e.networkStatus;e.init({document:a,variables:o,networkStatus:n});var y=function(){return e.getDiff()},m=function(O,S){S===void 0&&(S=e.networkStatus||C.loading);var k=O.result;globalThis.__DEV__!==!1&&!f&&!P(k,{})&&Sa(O.missing);var x=function(I){return L.of(d({data:I,loading:Ce(S),networkStatus:S},O.complete?null:{partial:!0}))};return k&&i.getDocumentInfo(a).hasForcedResolvers?i.localState.runResolvers({document:a,remoteResult:{data:k},context:h,variables:o,onlyRunForcedResolvers:!0}).then(function(I){return x(I.data||void 0)}):c==="none"&&S===C.refetch&&Array.isArray(O.missing)?x(void 0):x(k)},g=s==="no-cache"?0:n===C.refetch&&u!=="merge"?1:2,v=function(){return i.getResultsFromLink(e,g,{query:a,variables:o,context:h,fetchPolicy:s,errorPolicy:c})},b=l&&typeof p=="number"&&p!==n&&Ce(n);switch(s){default:case"cache-first":{var E=y();return E.complete?{fromLink:!1,sources:[m(E,e.markReady())]}:f||b?{fromLink:!0,sources:[m(E),v()]}:{fromLink:!0,sources:[v()]}}case"cache-and-network":{var E=y();return E.complete||f||b?{fromLink:!0,sources:[m(E),v()]}:{fromLink:!0,sources:[v()]}}case"cache-only":return{fromLink:!1,sources:[m(y(),e.markReady())]};case"network-only":return b?{fromLink:!0,sources:[m(y()),v()]}:{fromLink:!0,sources:[v()]};case"no-cache":return b?{fromLink:!0,sources:[m(e.getDiff()),v()]}:{fromLink:!0,sources:[v()]};case"standby":return{fromLink:!1,sources:[]}}},t.prototype.getOrCreateQuery=function(e){return e&&!this.queries.has(e)&&this.queries.set(e,new tr(this,e)),this.queries.get(e)},t.prototype.prepareContext=function(e){e===void 0&&(e={});var r=this.localState.prepareContext(e);return d(d(d({},this.defaultContext),r),{clientAwareness:this.clientAwareness})},t}(),rc=function(){function t(e){var r=e.cache,n=e.client,i=e.resolvers,a=e.fragmentMatcher;this.selectionsToResolveCache=new WeakMap,this.cache=r,n&&(this.client=n),i&&this.addResolvers(i),a&&this.setFragmentMatcher(a)}return t.prototype.addResolvers=function(e){var r=this;this.resolvers=this.resolvers||{},Array.isArray(e)?e.forEach(function(n){r.resolvers=Tn(r.resolvers,n)}):this.resolvers=Tn(this.resolvers,e)},t.prototype.setResolvers=function(e){this.resolvers={},this.addResolvers(e)},t.prototype.getResolvers=function(){return this.resolvers||{}},t.prototype.runResolvers=function(e){return ce(this,arguments,void 0,function(r){var n=r.document,i=r.remoteResult,a=r.context,o=r.variables,s=r.onlyRunForcedResolvers,u=s===void 0?!1:s;return fe(this,function(c){return n?[2,this.resolveDocument(n,i.data,a,o,this.fragmentMatcher,u).then(function(f){return d(d({},i),{data:f.result})})]:[2,i]})})},t.prototype.setFragmentMatcher=function(e){this.fragmentMatcher=e},t.prototype.getFragmentMatcher=function(){return this.fragmentMatcher},t.prototype.clientQuery=function(e){return ot(["client"],e)&&this.resolvers?e:null},t.prototype.serverQuery=function(e){return Yi(e)},t.prototype.prepareContext=function(e){var r=this.cache;return d(d({},e),{cache:r,getCacheKey:function(n){return r.identify(n)}})},t.prototype.addExportedVariables=function(e){return ce(this,arguments,void 0,function(r,n,i){return n===void 0&&(n={}),i===void 0&&(i={}),fe(this,function(a){return r?[2,this.resolveDocument(r,this.buildRootValueFromCache(r,n)||{},this.prepareContext(i),n).then(function(o){return d(d({},n),o.exportedVariables)})]:[2,d({},n)]})})},t.prototype.shouldForceResolvers=function(e){var r=!1;return Z(e,{Directive:{enter:function(n){if(n.name.value==="client"&&n.arguments&&(r=n.arguments.some(function(i){return i.name.value==="always"&&i.value.kind==="BooleanValue"&&i.value.value===!0}),r))return Ft}}}),r},t.prototype.buildRootValueFromCache=function(e,r){return this.cache.diff({query:Ns(e),variables:r,returnPartialData:!0,optimistic:!1}).result},t.prototype.resolveDocument=function(e,r){return ce(this,arguments,void 0,function(n,i,a,o,s,u){var c,f,h,l,p,y,m,g,v,b,E;return a===void 0&&(a={}),o===void 0&&(o={}),s===void 0&&(s=function(){return!0}),u===void 0&&(u=!1),fe(this,function(O){return c=ht(n),f=ze(n),h=Be(f),l=this.collectSelectionsToResolve(c,h),p=c.operation,y=p?p.charAt(0).toUpperCase()+p.slice(1):"Query",m=this,g=m.cache,v=m.client,b={fragmentMap:h,context:d(d({},a),{cache:g,client:v}),variables:o,fragmentMatcher:s,defaultOperationType:y,exportedVariables:{},selectionsToResolve:l,onlyRunForcedResolvers:u},E=!1,[2,this.resolveSelectionSet(c.selectionSet,E,i,b).then(function(S){return{result:S,exportedVariables:b.exportedVariables}})]})})},t.prototype.resolveSelectionSet=function(e,r,n,i){return ce(this,void 0,void 0,function(){var a,o,s,u,c,f=this;return fe(this,function(h){return a=i.fragmentMap,o=i.context,s=i.variables,u=[n],c=function(l){return ce(f,void 0,void 0,function(){var p,y;return fe(this,function(m){return!r&&!i.selectionsToResolve.has(l)?[2]:lt(l,s)?pe(l)?[2,this.resolveField(l,r,n,i).then(function(g){var v;typeof g<"u"&&u.push((v={},v[ae(l)]=g,v))})]:(vs(l)?p=l:(p=a[l.name.value],w(p,19,l.name.value)),p&&p.typeCondition&&(y=p.typeCondition.name.value,i.fragmentMatcher(n,y,o))?[2,this.resolveSelectionSet(p.selectionSet,r,n,i).then(function(g){u.push(g)})]:[2]):[2]})})},[2,Promise.all(e.selections.map(c)).then(function(){return Vt(u)})]})})},t.prototype.resolveField=function(e,r,n,i){return ce(this,void 0,void 0,function(){var a,o,s,u,c,f,h,l,p,y=this;return fe(this,function(m){return n?(a=i.variables,o=e.name.value,s=ae(e),u=o!==s,c=n[s]||n[o],f=Promise.resolve(c),(!i.onlyRunForcedResolvers||this.shouldForceResolvers(e))&&(h=n.__typename||i.defaultOperationType,l=this.resolvers&&this.resolvers[h],l&&(p=l[u?o:s],p&&(f=Promise.resolve(Jr.withValue(this.cache,p,[n,Mt(e,a),i.context,{field:e,fragmentMap:i.fragmentMap}]))))),[2,f.then(function(g){var v,b;if(g===void 0&&(g=c),e.directives&&e.directives.forEach(function(O){O.name.value==="export"&&O.arguments&&O.arguments.forEach(function(S){S.name.value==="as"&&S.value.kind==="StringValue"&&(i.exportedVariables[S.value.value]=g)})}),!e.selectionSet||g==null)return g;var E=(b=(v=e.directives)===null||v===void 0?void 0:v.some(function(O){return O.name.value==="client"}))!==null&&b!==void 0?b:!1;if(Array.isArray(g))return y.resolveSubSelectedArray(e,r||E,g,i);if(e.selectionSet)return y.resolveSelectionSet(e.selectionSet,r||E,g,i)})]):[2,null]})})},t.prototype.resolveSubSelectedArray=function(e,r,n,i){var a=this;return Promise.all(n.map(function(o){if(o===null)return null;if(Array.isArray(o))return a.resolveSubSelectedArray(e,r,o,i);if(e.selectionSet)return a.resolveSelectionSet(e.selectionSet,r,o,i)}))},t.prototype.collectSelectionsToResolve=function(e,r){var n=function(o){return!Array.isArray(o)},i=this.selectionsToResolveCache;function a(o){if(!i.has(o)){var s=new Set;i.set(o,s),Z(o,{Directive:function(u,c,f,h,l){u.name.value==="client"&&l.forEach(function(p){n(p)&&vn(p)&&s.add(p)})},FragmentSpread:function(u,c,f,h,l){var p=r[u.name.value];w(p,20,u.name.value);var y=a(p);y.size>0&&(l.forEach(function(m){n(m)&&vn(m)&&s.add(m)}),s.add(u),y.forEach(function(m){s.add(m)}))}})}return i.get(o)}return a(e)},t}(),ii=!1,nc=function(){function t(e){var r=this,n;if(this.resetStoreCallbacks=[],this.clearStoreCallbacks=[],!e.cache)throw $(16);var i=e.uri,a=e.credentials,o=e.headers,s=e.cache,u=e.documentTransform,c=e.ssrMode,f=c===void 0?!1:c,h=e.ssrForceFetchDelay,l=h===void 0?0:h,p=e.connectToDevTools,y=e.queryDeduplication,m=y===void 0?!0:y,g=e.defaultOptions,v=e.defaultContext,b=e.assumeImmutableResults,E=b===void 0?s.assumeImmutableResults:b,O=e.resolvers,S=e.typeDefs,k=e.fragmentMatcher,x=e.name,I=e.version,R=e.devtools,M=e.dataMasking,G=e.link;G||(G=i?new ku({uri:i,credentials:a,headers:o}):We.empty()),this.link=G,this.cache=s,this.disableNetworkFetches=f||l>0,this.queryDeduplication=m,this.defaultOptions=g||Object.create(null),this.typeDefs=S,this.devtoolsConfig=d(d({},R),{enabled:(n=R?.enabled)!==null&&n!==void 0?n:p}),this.devtoolsConfig.enabled===void 0&&(this.devtoolsConfig.enabled=globalThis.__DEV__!==!1),l&&setTimeout(function(){return r.disableNetworkFetches=!1},l),this.watchQuery=this.watchQuery.bind(this),this.query=this.query.bind(this),this.mutate=this.mutate.bind(this),this.watchFragment=this.watchFragment.bind(this),this.resetStore=this.resetStore.bind(this),this.reFetchObservableQueries=this.reFetchObservableQueries.bind(this),this.version=Nr,this.localState=new rc({cache:s,client:this,resolvers:O,fragmentMatcher:k}),this.queryManager=new tc({cache:this.cache,link:this.link,defaultOptions:this.defaultOptions,defaultContext:v,documentTransform:u,queryDeduplication:m,ssrMode:f,dataMasking:!!M,clientAwareness:{name:x,version:I},localState:this.localState,assumeImmutableResults:E,onBroadcast:this.devtoolsConfig.enabled?function(){r.devToolsHookCb&&r.devToolsHookCb({action:{},state:{queries:r.queryManager.getQueryStore(),mutations:r.queryManager.mutationStore||{}},dataWithOptimisticResults:r.cache.extract(!0)})}:void 0}),this.devtoolsConfig.enabled&&this.connectToDevTools()}return t.prototype.connectToDevTools=function(){if(!(typeof window>"u")){var e=window,r=Symbol.for("apollo.devtools");(e[r]=e[r]||[]).push(this),e.__APOLLO_CLIENT__=this,!ii&&globalThis.__DEV__!==!1&&(ii=!0,window.document&&window.top===window.self&&/^(https?|file):$/.test(window.location.protocol)&&setTimeout(function(){if(!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__){var n=window.navigator,i=n&&n.userAgent,a=void 0;typeof i=="string"&&(i.indexOf("Chrome/")>-1?a="https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm":i.indexOf("Firefox/")>-1&&(a="https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/")),a&&globalThis.__DEV__!==!1&&w.log("Download the Apollo DevTools for a better development experience: %s",a)}},1e4))}},Object.defineProperty(t.prototype,"documentTransform",{get:function(){return this.queryManager.documentTransform},enumerable:!1,configurable:!0}),t.prototype.stop=function(){this.queryManager.stop()},t.prototype.watchQuery=function(e){return this.defaultOptions.watchQuery&&(e=Pe(this.defaultOptions.watchQuery,e)),this.disableNetworkFetches&&(e.fetchPolicy==="network-only"||e.fetchPolicy==="cache-and-network")&&(e=d(d({},e),{fetchPolicy:"cache-first"})),this.queryManager.watchQuery(e)},t.prototype.query=function(e){return this.defaultOptions.query&&(e=Pe(this.defaultOptions.query,e)),w(e.fetchPolicy!=="cache-and-network",17),this.disableNetworkFetches&&e.fetchPolicy==="network-only"&&(e=d(d({},e),{fetchPolicy:"cache-first"})),this.queryManager.query(e)},t.prototype.mutate=function(e){return this.defaultOptions.mutate&&(e=Pe(this.defaultOptions.mutate,e)),this.queryManager.mutate(e)},t.prototype.subscribe=function(e){var r=this,n=this.queryManager.generateQueryId();return this.queryManager.startGraphQLSubscription(e).map(function(i){return d(d({},i),{data:r.queryManager.maskOperation({document:e.query,data:i.data,fetchPolicy:e.fetchPolicy,id:n})})})},t.prototype.readQuery=function(e,r){return r===void 0&&(r=!1),this.cache.readQuery(e,r)},t.prototype.watchFragment=function(e){var r;return this.cache.watchFragment(d(d({},e),(r={},r[Symbol.for("apollo.dataMasking")]=this.queryManager.dataMasking,r)))},t.prototype.readFragment=function(e,r){return r===void 0&&(r=!1),this.cache.readFragment(e,r)},t.prototype.writeQuery=function(e){var r=this.cache.writeQuery(e);return e.broadcast!==!1&&this.queryManager.broadcastQueries(),r},t.prototype.writeFragment=function(e){var r=this.cache.writeFragment(e);return e.broadcast!==!1&&this.queryManager.broadcastQueries(),r},t.prototype.__actionHookForDevTools=function(e){this.devToolsHookCb=e},t.prototype.__requestRaw=function(e){return br(this.link,e)},t.prototype.resetStore=function(){var e=this;return Promise.resolve().then(function(){return e.queryManager.clearStore({discardWatches:!1})}).then(function(){return Promise.all(e.resetStoreCallbacks.map(function(r){return r()}))}).then(function(){return e.reFetchObservableQueries()})},t.prototype.clearStore=function(){var e=this;return Promise.resolve().then(function(){return e.queryManager.clearStore({discardWatches:!0})}).then(function(){return Promise.all(e.clearStoreCallbacks.map(function(r){return r()}))})},t.prototype.onResetStore=function(e){var r=this;return this.resetStoreCallbacks.push(e),function(){r.resetStoreCallbacks=r.resetStoreCallbacks.filter(function(n){return n!==e})}},t.prototype.onClearStore=function(e){var r=this;return this.clearStoreCallbacks.push(e),function(){r.clearStoreCallbacks=r.clearStoreCallbacks.filter(function(n){return n!==e})}},t.prototype.reFetchObservableQueries=function(e){return this.queryManager.reFetchObservableQueries(e)},t.prototype.refetchQueries=function(e){var r=this.queryManager.refetchQueries(e),n=[],i=[];r.forEach(function(o,s){n.push(s),i.push(o)});var a=Promise.all(i);return a.queries=n,a.results=i,a.catch(function(o){globalThis.__DEV__!==!1&&w.debug(18,o)}),a},t.prototype.getObservableQueries=function(e){return e===void 0&&(e="active"),this.queryManager.getObservableQueries(e)},t.prototype.extract=function(e){return this.cache.extract(e)},t.prototype.restore=function(e){return this.cache.restore(e)},t.prototype.addResolvers=function(e){this.localState.addResolvers(e)},t.prototype.setResolvers=function(e){this.localState.setResolvers(e)},t.prototype.getResolvers=function(){return this.localState.getResolvers()},t.prototype.setLocalStateFragmentMatcher=function(e){this.localState.setFragmentMatcher(e)},t.prototype.setLink=function(e){this.link=this.queryManager.link=e},Object.defineProperty(t.prototype,"defaultContext",{get:function(){return this.queryManager.defaultContext},enumerable:!1,configurable:!0}),t}();globalThis.__DEV__!==!1&&(nc.prototype.getMemoryInternals=Yo);var Tt=new Map,Dr=new Map,Ta=!0,xt=!1;function wa(t){return t.replace(/[\s,]+/g," ").trim()}function ic(t){return wa(t.source.body.substring(t.start,t.end))}function ac(t){var e=new Set,r=[];return t.definitions.forEach(function(n){if(n.kind==="FragmentDefinition"){var i=n.name.value,a=ic(n.loc),o=Dr.get(i);o&&!o.has(a)?Ta&&console.warn("Warning: fragment with name "+i+` already exists.
graphql-tag enforces all fragment names across your application to be unique; read more about
this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names`):o||Dr.set(i,o=new Set),o.add(a),e.has(a)||(e.add(a),r.push(n))}else r.push(n)}),d(d({},t),{definitions:r})}function oc(t){var e=new Set(t.definitions);e.forEach(function(n){n.loc&&delete n.loc,Object.keys(n).forEach(function(i){var a=n[i];a&&typeof a=="object"&&e.add(a)})});var r=t.loc;return r&&(delete r.startToken,delete r.endToken),t}function sc(t){var e=wa(t);if(!Tt.has(e)){var r=go(t,{experimentalFragmentVariables:xt,allowLegacyFragmentVariables:xt});if(!r||r.kind!=="Document")throw new Error("Not a valid GraphQL document.");Tt.set(e,oc(ac(r)))}return Tt.get(e)}function ft(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];typeof t=="string"&&(t=[t]);var n=t[0];return e.forEach(function(i,a){i&&i.kind==="Document"?n+=i.loc.source.body:n+=i,n+=t[a+1]}),sc(n)}function uc(){Tt.clear(),Dr.clear()}function cc(){Ta=!1}function fc(){xt=!0}function lc(){xt=!1}var Je={gql:ft,resetCaches:uc,disableFragmentWarnings:cc,enableExperimentalFragmentVariables:fc,disableExperimentalFragmentVariables:lc};(function(t){t.gql=Je.gql,t.resetCaches=Je.resetCaches,t.disableFragmentWarnings=Je.disableFragmentWarnings,t.enableExperimentalFragmentVariables=Je.enableExperimentalFragmentVariables,t.disableExperimentalFragmentVariables=Je.disableExperimentalFragmentVariables})(ft||(ft={}));ft.default=ft;var rr={exports:{}},ai;function hc(){return ai||(ai=1,function(t){t.exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=void 0,t.exports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=void 0,t.exports.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=void 0,Object.assign(t.exports,Pa())}(rr)),rr.exports}var F=hc();const pc=Ma(F),ka=La({__proto__:null,default:pc},[F]);var oi=Mr?Symbol.for("__APOLLO_CONTEXT__"):"__APOLLO_CONTEXT__";function en(){w("createContext"in ka,54);var t=F.createContext[oi];return t||(Object.defineProperty(F.createContext,oi,{value:t=F.createContext({}),enumerable:!1,writable:!1,configurable:!0}),t.displayName="ApolloContext"),t}var jc=function(t){var e=t.client,r=t.children,n=en(),i=F.useContext(n),a=F.useMemo(function(){return d(d({},i),{client:e||i.client})},[i,e]);return w(a.client,55),F.createElement(n.Provider,{value:a},r)};function Da(t){var e=F.useContext(en()),r=t||e.client;return w(!!r,58),r}var si=!1,dc="useSyncExternalStore",vc=ka[dc],yc=vc||function(t,e,r){var n=e();globalThis.__DEV__!==!1&&!si&&n!==e()&&(si=!0,globalThis.__DEV__!==!1&&w.error(68));var i=F.useState({inst:{value:n,getSnapshot:e}}),a=i[0].inst,o=i[1];return Lo?F.useLayoutEffect(function(){Object.assign(a,{value:n,getSnapshot:e}),nr(a)&&o({inst:a})},[t,n,e]):Object.assign(a,{value:n,getSnapshot:e}),F.useEffect(function(){return nr(a)&&o({inst:a}),t(function(){nr(a)&&o({inst:a})})},[t]),n};function nr(t){var e=t.value,r=t.getSnapshot;try{return e!==r()}catch{return!0}}var ie;(function(t){t[t.Query=0]="Query",t[t.Mutation=1]="Mutation",t[t.Subscription=2]="Subscription"})(ie||(ie={}));var Ee;function ui(t){var e;switch(t){case ie.Query:e="Query";break;case ie.Mutation:e="Mutation";break;case ie.Subscription:e="Subscription";break}return e}function Ia(t){Ee||(Ee=new Lr(te.parser||1e3));var e=Ee.get(t);if(e)return e;var r,n,i;w(!!t&&!!t.kind,70,t);for(var a=[],o=[],s=[],u=[],c=0,f=t.definitions;c<f.length;c++){var h=f[c];if(h.kind==="FragmentDefinition"){a.push(h);continue}if(h.kind==="OperationDefinition")switch(h.operation){case"query":o.push(h);break;case"mutation":s.push(h);break;case"subscription":u.push(h);break}}w(!a.length||o.length||s.length||u.length,71),w(o.length+s.length+u.length<=1,72,t,o.length,u.length,s.length),n=o.length?ie.Query:ie.Mutation,!o.length&&!s.length&&(n=ie.Subscription);var l=o.length?o:s.length?s:u;w(l.length===1,73,t,l.length);var p=l[0];r=p.variableDefinitions||[],p.name&&p.name.kind==="Name"?i=p.name.value:i="data";var y={name:i,type:n,variables:r};return Ee.set(t,y),y}Ia.resetCache=function(){Ee=void 0};globalThis.__DEV__!==!1&&jr("parser",function(){return Ee?Ee.size:0});function mc(t,e){var r=Ia(t),n=ui(e),i=ui(r.type);w(r.type===e,74,n,n,i)}var gc=ki?F.useLayoutEffect:F.useEffect,bc=Symbol.for("apollo.hook.wrappers");function Ec(t,e,r){var n=r.queryManager,i=n&&n[bc],a=i&&i[t];return a?a(e):e}var _c=Object.prototype.hasOwnProperty;function ci(){}var wt=Symbol();function Vc(t,e){return e===void 0&&(e=Object.create(null)),Ec("useQuery",Oc,Da(e&&e.client))(t,e)}function Oc(t,e){var r=xa(t,e),n=r.result,i=r.obsQueryFields;return F.useMemo(function(){return d(d({},n),i)},[n,i])}function Sc(t,e,r,n,i){function a(h){var l;mc(e,ie.Query);var p={client:t,query:e,observable:n&&n.getSSRObservable(i())||it.inactiveOnCreation.withValue(!n,function(){return t.watchQuery(tn(void 0,t,r,i()))}),resultData:{previousData:(l=h?.resultData.current)===null||l===void 0?void 0:l.data}};return p}var o=F.useState(a),s=o[0],u=o[1];function c(h){var l,p;Object.assign(s.observable,(l={},l[wt]=h,l));var y=s.resultData;u(d(d({},s),{query:h.query,resultData:Object.assign(y,{previousData:((p=y.current)===null||p===void 0?void 0:p.data)||y.previousData,current:void 0})}))}if(t!==s.client||e!==s.query){var f=a(s);return u(f),[f,c]}return[s,c]}function xa(t,e){var r=Da(e.client),n=F.useContext(en()).renderPromises,i=!!n,a=r.disableNetworkFetches,o=e.ssr!==!1&&!e.skip,s=e.partialRefetch,u=Na(r,t,e,i),c=Sc(r,t,e,n,u),f=c[0],h=f.observable,l=f.resultData,p=c[1],y=u(h);kc(l,h,r,e,y);var m=F.useMemo(function(){return Nc(h)},[h]);wc(h,n,o);var g=Tc(l,h,r,e,y,a,s,i,{onCompleted:e.onCompleted||ci,onError:e.onError||ci});return{result:g,obsQueryFields:m,observable:h,resultData:l,client:r,onQueryExecuted:p}}function Tc(t,e,r,n,i,a,o,s,u){var c=F.useRef(u);F.useEffect(function(){c.current=u});var f=(s||a)&&n.ssr===!1&&!n.skip?Ca:n.skip||i.fetchPolicy==="standby"?Ra:void 0,h=t.previousData,l=F.useMemo(function(){return f&&Nt(f,h,e,r)},[r,e,f,h]);return yc(F.useCallback(function(p){if(s)return function(){};var y=function(){var v=t.current,b=e.getCurrentResult();v&&v.loading===b.loading&&v.networkStatus===b.networkStatus&&P(v.data,b.data)||Ir(b,t,e,r,o,p,c.current)},m=function(v){if(g.current.unsubscribe(),g.current=e.resubscribeAfterError(y,m),!_c.call(v,"graphQLErrors"))throw v;var b=t.current;(!b||b&&b.loading||!P(v,b.error))&&Ir({data:b&&b.data,error:v,loading:!1,networkStatus:C.error},t,e,r,o,p,c.current)},g={current:e.subscribe(y,m)};return function(){setTimeout(function(){return g.current.unsubscribe()})}},[a,s,e,t,o,r]),function(){return l||fi(t,e,c.current,o,r)},function(){return l||fi(t,e,c.current,o,r)})}function wc(t,e,r){e&&r&&(e.registerSSRObservable(t),t.getCurrentResult().loading&&e.addObservableQueryPromise(t))}function kc(t,e,r,n,i){var a;e[wt]&&!P(e[wt],i)&&(e.reobserve(tn(e,r,n,i)),t.previousData=((a=t.current)===null||a===void 0?void 0:a.data)||t.previousData,t.current=void 0),e[wt]=i}function Na(t,e,r,n){r===void 0&&(r={});var i=r.skip;r.ssr,r.onCompleted,r.onError;var a=r.defaultOptions,o=X(r,["skip","ssr","onCompleted","onError","defaultOptions"]);return function(s){var u=Object.assign(o,{query:e});return n&&(u.fetchPolicy==="network-only"||u.fetchPolicy==="cache-and-network")&&(u.fetchPolicy="cache-first"),u.variables||(u.variables={}),i?(u.initialFetchPolicy=u.initialFetchPolicy||u.fetchPolicy||xr(a,t.defaultOptions),u.fetchPolicy="standby"):u.fetchPolicy||(u.fetchPolicy=s?.options.initialFetchPolicy||xr(a,t.defaultOptions)),u}}function tn(t,e,r,n){var i=[],a=e.defaultOptions.watchQuery;return a&&i.push(a),r.defaultOptions&&i.push(r.defaultOptions),i.push(Se(t&&t.options,n)),i.reduce(Pe)}function Ir(t,e,r,n,i,a,o){var s=e.current;s&&s.data&&(e.previousData=s.data),!t.error&&H(t.errors)&&(t.error=new ne({graphQLErrors:t.errors})),e.current=Nt(xc(t,r,i),e.previousData,r,n),a(),Dc(t,s?.networkStatus,o)}function Dc(t,e,r){if(!t.loading){var n=Ic(t);Promise.resolve().then(function(){n?r.onError(n):t.data&&e!==t.networkStatus&&t.networkStatus===C.ready&&r.onCompleted(t.data)}).catch(function(i){globalThis.__DEV__!==!1&&w.warn(i)})}}function fi(t,e,r,n,i){return t.current||Ir(e.getCurrentResult(),t,e,i,n,function(){},r),t.current}function xr(t,e){var r;return t?.fetchPolicy||((r=e?.watchQuery)===null||r===void 0?void 0:r.fetchPolicy)||"cache-first"}function Ic(t){return H(t.errors)?new ne({graphQLErrors:t.errors}):t.error}function Nt(t,e,r,n){var i=t.data;t.partial;var a=X(t,["data","partial"]),o=d(d({data:i},a),{client:n,observable:r,variables:r.variables,called:t!==Ca&&t!==Ra,previousData:e});return o}function xc(t,e,r){return t.partial&&r&&!t.loading&&(!t.data||Object.keys(t.data).length===0)&&e.options.fetchPolicy!=="cache-only"?(e.refetch(),d(d({},t),{loading:!0,networkStatus:C.refetch})):t}var Ca=Qe({loading:!0,data:void 0,error:void 0,networkStatus:C.loading}),Ra=Qe({loading:!1,data:void 0,error:void 0,networkStatus:C.ready});function Nc(t){return{refetch:t.refetch.bind(t),reobserve:t.reobserve.bind(t),fetchMore:t.fetchMore.bind(t),updateQuery:t.updateQuery.bind(t),startPolling:t.startPolling.bind(t),stopPolling:t.stopPolling.bind(t),subscribeToMore:t.subscribeToMore.bind(t)}}var Cc=["refetch","reobserve","fetchMore","updateQuery","startPolling","stopPolling","subscribeToMore"];function Qc(t,e){var r,n=F.useRef(void 0),i=F.useRef(void 0),a=F.useRef(void 0),o=Pe(e,n.current||{}),s=(r=o?.query)!==null&&r!==void 0?r:t;i.current=e,a.current=s;var u=d(d({},o),{skip:!n.current}),c=xa(s,u),f=c.obsQueryFields,h=c.result,l=c.client,p=c.resultData,y=c.observable,m=c.onQueryExecuted,g=y.options.initialFetchPolicy||xr(u.defaultOptions,l.defaultOptions),v=F.useReducer(function(I){return I+1},0)[1],b=F.useMemo(function(){for(var I={},R=function(Q){var ee=f[Q];I[Q]=function(){return n.current||(n.current=Object.create(null),v()),ee.apply(this,arguments)}},M=0,G=Cc;M<G.length;M++){var se=G[M];R(se)}return I},[v,f]),E=!!n.current,O=F.useMemo(function(){return d(d(d({},h),b),{called:E})},[h,b,E]),S=F.useCallback(function(I){n.current=I?d(d({},I),{fetchPolicy:I.fetchPolicy||g}):{fetchPolicy:g};var R=Pe(i.current,d({query:a.current},n.current)),M=Rc(p,y,l,s,d(d({},R),{skip:!1}),m).then(function(G){return Object.assign(G,b)});return M.catch(function(){}),M},[l,s,b,g,y,p,m]),k=F.useRef(S);gc(function(){k.current=S});var x=F.useCallback(function(){for(var I=[],R=0;R<arguments.length;R++)I[R]=arguments[R];return k.current.apply(k,I)},[]);return[x,O]}function Rc(t,e,r,n,i,a){var o=i.query||n,s=Na(r,o,i,!1)(e),u=e.reobserveAsConcast(tn(e,r,i,s));return a(s),new Promise(function(c){var f;u.subscribe({next:function(h){f=h},error:function(){c(Nt(e.getCurrentResult(),t.previousData,e,r))},complete:function(){c(Nt(e.maskResult(f),t.previousData,e,r))}})})}export{We as A,Ku as I,L as O,Yr as P,X as _,re as a,nc as b,wu as c,ft as d,jc as e,Lc as f,cu as g,Qc as h,Vc as u};
