const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MainDashboard-DY2OdiLg.js","assets/animation-vendor-CTGg7XC5.js","assets/react-vendor-Csw2ODfV.js","assets/ui-vendor-DuyO0ClB.js","assets/apollo-vendor-CuB5uN0C.js","assets/LeaderboardSection-p9OipZjv.js","assets/SearchSection-C3K7A1kI.js","assets/ExportSection-rRXF8O1w.js","assets/PiscineSection-2jx3wjQL.js","assets/NotificationSystem-BGD0Bgny.js","assets/UserPreferences-eeJ58MU7.js"])))=>i.map(i=>d[i]);
import{r as w,R as mu,j as s,m as $}from"./animation-vendor-CTGg7XC5.js";import{r as Sg,a as Eg,g as _g}from"./react-vendor-Csw2ODfV.js";import{A as Fc,_ as Ng,O as Nh,g as Tg,P as Ag,a as jg,c as wg,I as Dg,b as Rg,f as Mg,u as _l,d as ae,e as Th}from"./apollo-vendor-CuB5uN0C.js";import{L as uh,C as Og,M as $c,a as Ug,E as zg,b as Cg,B as Lg,c as Ic,T as Hg,S as Ah,D as qg,U as xu,d as Bg,e as Gg,f as sh,g as Vg,P as Yg,Z as Xg,h as Zg,i as jh,A as gu,j as Gn,k as Qg,l as Kg,m as ch,n as kc,H as wh,o as $g,R as Ig}from"./ui-vendor-DuyO0ClB.js";(function(){const f=document.createElement("link").relList;if(f&&f.supports&&f.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const m of d)if(m.type==="childList")for(const y of m.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&c(y)}).observe(document,{childList:!0,subtree:!0});function r(d){const m={};return d.integrity&&(m.integrity=d.integrity),d.referrerPolicy&&(m.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?m.credentials="include":d.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function c(d){if(d.ep)return;d.ep=!0;const m=r(d);fetch(d.href,m)}})();var Gc={exports:{}},Hn={},Vc={exports:{}},Yc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rh;function kg(){return rh||(rh=1,function(u){function f(C,G){var J=C.length;C.push(G);e:for(;0<J;){var ve=J-1>>>1,be=C[ve];if(0<d(be,G))C[ve]=G,C[J]=be,J=ve;else break e}}function r(C){return C.length===0?null:C[0]}function c(C){if(C.length===0)return null;var G=C[0],J=C.pop();if(J!==G){C[0]=J;e:for(var ve=0,be=C.length,Ie=be>>>1;ve<Ie;){var Ne=2*(ve+1)-1,fe=C[Ne],qe=Ne+1,At=C[qe];if(0>d(fe,J))qe<be&&0>d(At,fe)?(C[ve]=At,C[qe]=J,ve=qe):(C[ve]=fe,C[Ne]=J,ve=Ne);else if(qe<be&&0>d(At,J))C[ve]=At,C[qe]=J,ve=qe;else break e}}return G}function d(C,G){var J=C.sortIndex-G.sortIndex;return J!==0?J:C.id-G.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;u.unstable_now=function(){return m.now()}}else{var y=Date,v=y.now();u.unstable_now=function(){return y.now()-v}}var p=[],x=[],E=1,M=null,T=3,q=!1,R=!1,D=!1,L=!1,U=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;function X(C){for(var G=r(x);G!==null;){if(G.callback===null)c(x);else if(G.startTime<=C)c(x),G.sortIndex=G.expirationTime,f(p,G);else break;G=r(x)}}function W(C){if(D=!1,X(C),!R)if(r(p)!==null)R=!0,xe||(xe=!0,Ae());else{var G=r(x);G!==null&&Ut(W,G.startTime-C)}}var xe=!1,ue=-1,_e=5,De=-1;function ne(){return L?!0:!(u.unstable_now()-De<_e)}function Ce(){if(L=!1,xe){var C=u.unstable_now();De=C;var G=!0;try{e:{R=!1,D&&(D=!1,V(ue),ue=-1),q=!0;var J=T;try{t:{for(X(C),M=r(p);M!==null&&!(M.expirationTime>C&&ne());){var ve=M.callback;if(typeof ve=="function"){M.callback=null,T=M.priorityLevel;var be=ve(M.expirationTime<=C);if(C=u.unstable_now(),typeof be=="function"){M.callback=be,X(C),G=!0;break t}M===r(p)&&c(p),X(C)}else c(p);M=r(p)}if(M!==null)G=!0;else{var Ie=r(x);Ie!==null&&Ut(W,Ie.startTime-C),G=!1}}break e}finally{M=null,T=J,q=!1}G=void 0}}finally{G?Ae():xe=!1}}}var Ae;if(typeof B=="function")Ae=function(){B(Ce)};else if(typeof MessageChannel<"u"){var Tl=new MessageChannel,qa=Tl.port2;Tl.port1.onmessage=Ce,Ae=function(){qa.postMessage(null)}}else Ae=function(){U(Ce,0)};function Ut(C,G){ue=U(function(){C(u.unstable_now())},G)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(C){C.callback=null},u.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_e=0<C?Math.floor(1e3/C):5},u.unstable_getCurrentPriorityLevel=function(){return T},u.unstable_next=function(C){switch(T){case 1:case 2:case 3:var G=3;break;default:G=T}var J=T;T=G;try{return C()}finally{T=J}},u.unstable_requestPaint=function(){L=!0},u.unstable_runWithPriority=function(C,G){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var J=T;T=C;try{return G()}finally{T=J}},u.unstable_scheduleCallback=function(C,G,J){var ve=u.unstable_now();switch(typeof J=="object"&&J!==null?(J=J.delay,J=typeof J=="number"&&0<J?ve+J:ve):J=ve,C){case 1:var be=-1;break;case 2:be=250;break;case 5:be=1073741823;break;case 4:be=1e4;break;default:be=5e3}return be=J+be,C={id:E++,callback:G,priorityLevel:C,startTime:J,expirationTime:be,sortIndex:-1},J>ve?(C.sortIndex=J,f(x,C),r(p)===null&&C===r(x)&&(D?(V(ue),ue=-1):D=!0,Ut(W,J-ve))):(C.sortIndex=be,f(p,C),R||q||(R=!0,xe||(xe=!0,Ae()))),C},u.unstable_shouldYield=ne,u.unstable_wrapCallback=function(C){var G=T;return function(){var J=T;T=G;try{return C.apply(this,arguments)}finally{T=J}}}}(Yc)),Yc}var oh;function Jg(){return oh||(oh=1,Vc.exports=kg()),Vc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function Wg(){if(fh)return Hn;fh=1;var u=Jg(),f=Sg(),r=Eg();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function y(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(m(e)!==e)throw Error(c(188))}function p(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(c(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return v(n),e;if(i===a)return v(n),t;i=i.sibling}throw Error(c(188))}if(l.return!==a.return)l=n,a=i;else{for(var o=!1,h=n.child;h;){if(h===l){o=!0,l=n,a=i;break}if(h===a){o=!0,a=n,l=i;break}h=h.sibling}if(!o){for(h=i.child;h;){if(h===l){o=!0,l=i,a=n;break}if(h===a){o=!0,a=i,l=n;break}h=h.sibling}if(!o)throw Error(c(189))}}if(l.alternate!==a)throw Error(c(190))}if(l.tag!==3)throw Error(c(188));return l.stateNode.current===l?e:t}function x(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=x(e),t!==null)return t;e=e.sibling}return null}var E=Object.assign,M=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),q=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),U=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),B=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),W=Symbol.for("react.suspense"),xe=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),_e=Symbol.for("react.lazy"),De=Symbol.for("react.activity"),ne=Symbol.for("react.memo_cache_sentinel"),Ce=Symbol.iterator;function Ae(e){return e===null||typeof e!="object"?null:(e=Ce&&e[Ce]||e["@@iterator"],typeof e=="function"?e:null)}var Tl=Symbol.for("react.client.reference");function qa(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Tl?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case L:return"Profiler";case D:return"StrictMode";case W:return"Suspense";case xe:return"SuspenseList";case De:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case q:return"Portal";case B:return(e.displayName||"Context")+".Provider";case V:return(e._context.displayName||"Context")+".Consumer";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ue:return t=e.displayName||null,t!==null?t:qa(e.type)||"Memo";case _e:t=e._payload,e=e._init;try{return qa(e(t))}catch{}}return null}var Ut=Array.isArray,C=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J={pending:!1,data:null,method:null,action:null},ve=[],be=-1;function Ie(e){return{current:e}}function Ne(e){0>be||(e.current=ve[be],ve[be]=null,be--)}function fe(e,t){be++,ve[be]=e.current,e.current=t}var qe=Ie(null),At=Ie(null),Pt=Ie(null),Kn=Ie(null);function $n(e,t){switch(fe(Pt,t),fe(At,e),fe(qe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?zd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=zd(t),e=Cd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Ne(qe),fe(qe,e)}function Ql(){Ne(qe),Ne(At),Ne(Pt)}function Tu(e){e.memoizedState!==null&&fe(Kn,e);var t=qe.current,l=Cd(t,e.type);t!==l&&(fe(At,e),fe(qe,l))}function In(e){At.current===e&&(Ne(qe),Ne(At)),Kn.current===e&&(Ne(Kn),On._currentValue=J)}var Au=Object.prototype.hasOwnProperty,ju=u.unstable_scheduleCallback,wu=u.unstable_cancelCallback,Fh=u.unstable_shouldYield,em=u.unstable_requestPaint,jt=u.unstable_now,tm=u.unstable_getCurrentPriorityLevel,dr=u.unstable_ImmediatePriority,hr=u.unstable_UserBlockingPriority,kn=u.unstable_NormalPriority,lm=u.unstable_LowPriority,mr=u.unstable_IdlePriority,am=u.log,nm=u.unstable_setDisableYieldValue,Ba=null,tt=null;function Ft(e){if(typeof am=="function"&&nm(e),tt&&typeof tt.setStrictMode=="function")try{tt.setStrictMode(Ba,e)}catch{}}var lt=Math.clz32?Math.clz32:sm,im=Math.log,um=Math.LN2;function sm(e){return e>>>=0,e===0?32:31-(im(e)/um|0)|0}var Jn=256,Wn=4194304;function Al(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Pn(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,i=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var h=a&134217727;return h!==0?(a=h&~i,a!==0?n=Al(a):(o&=h,o!==0?n=Al(o):l||(l=h&~e,l!==0&&(n=Al(l))))):(h=a&~i,h!==0?n=Al(h):o!==0?n=Al(o):l||(l=a&~e,l!==0&&(n=Al(l)))),n===0?0:t!==0&&t!==n&&(t&i)===0&&(i=n&-n,l=t&-t,i>=l||i===32&&(l&4194048)!==0)?t:n}function Ga(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function cm(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function gr(){var e=Jn;return Jn<<=1,(Jn&4194048)===0&&(Jn=256),e}function pr(){var e=Wn;return Wn<<=1,(Wn&62914560)===0&&(Wn=4194304),e}function Du(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Va(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function rm(e,t,l,a,n,i){var o=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var h=e.entanglements,g=e.expirationTimes,N=e.hiddenUpdates;for(l=o&~l;0<l;){var O=31-lt(l),H=1<<O;h[O]=0,g[O]=-1;var A=N[O];if(A!==null)for(N[O]=null,O=0;O<A.length;O++){var j=A[O];j!==null&&(j.lane&=-536870913)}l&=~H}a!==0&&yr(e,a,0),i!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=i&~(o&~t))}function yr(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-lt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function xr(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-lt(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function Ru(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Mu(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function vr(){var e=G.p;return e!==0?e:(e=window.event,e===void 0?32:eh(e.type))}function om(e,t){var l=G.p;try{return G.p=e,t()}finally{G.p=l}}var el=Math.random().toString(36).slice(2),Ke="__reactFiber$"+el,Je="__reactProps$"+el,Kl="__reactContainer$"+el,Ou="__reactEvents$"+el,fm="__reactListeners$"+el,dm="__reactHandles$"+el,br="__reactResources$"+el,Ya="__reactMarker$"+el;function Uu(e){delete e[Ke],delete e[Je],delete e[Ou],delete e[fm],delete e[dm]}function $l(e){var t=e[Ke];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Kl]||l[Ke]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Bd(e);e!==null;){if(l=e[Ke])return l;e=Bd(e)}return t}e=l,l=e.parentNode}return null}function Il(e){if(e=e[Ke]||e[Kl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Xa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function kl(e){var t=e[br];return t||(t=e[br]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Be(e){e[Ya]=!0}var Sr=new Set,Er={};function jl(e,t){Jl(e,t),Jl(e+"Capture",t)}function Jl(e,t){for(Er[e]=t,e=0;e<t.length;e++)Sr.add(t[e])}var hm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_r={},Nr={};function mm(e){return Au.call(Nr,e)?!0:Au.call(_r,e)?!1:hm.test(e)?Nr[e]=!0:(_r[e]=!0,!1)}function Fn(e,t,l){if(mm(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function ei(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function zt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var zu,Tr;function Wl(e){if(zu===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);zu=t&&t[1]||"",Tr=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+zu+e+Tr}var Cu=!1;function Lu(e,t){if(!e||Cu)return"";Cu=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(j){var A=j}Reflect.construct(e,[],H)}else{try{H.call()}catch(j){A=j}e.call(H.prototype)}}else{try{throw Error()}catch(j){A=j}(H=e())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(j){if(j&&A&&typeof j.stack=="string")return[j.stack,A.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),o=i[0],h=i[1];if(o&&h){var g=o.split(`
`),N=h.split(`
`);for(n=a=0;a<g.length&&!g[a].includes("DetermineComponentFrameRoot");)a++;for(;n<N.length&&!N[n].includes("DetermineComponentFrameRoot");)n++;if(a===g.length||n===N.length)for(a=g.length-1,n=N.length-1;1<=a&&0<=n&&g[a]!==N[n];)n--;for(;1<=a&&0<=n;a--,n--)if(g[a]!==N[n]){if(a!==1||n!==1)do if(a--,n--,0>n||g[a]!==N[n]){var O=`
`+g[a].replace(" at new "," at ");return e.displayName&&O.includes("<anonymous>")&&(O=O.replace("<anonymous>",e.displayName)),O}while(1<=a&&0<=n);break}}}finally{Cu=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?Wl(l):""}function gm(e){switch(e.tag){case 26:case 27:case 5:return Wl(e.type);case 16:return Wl("Lazy");case 13:return Wl("Suspense");case 19:return Wl("SuspenseList");case 0:case 15:return Lu(e.type,!1);case 11:return Lu(e.type.render,!1);case 1:return Lu(e.type,!0);case 31:return Wl("Activity");default:return""}}function Ar(e){try{var t="";do t+=gm(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ot(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jr(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function pm(e){var t=jr(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(o){a=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(o){a=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ti(e){e._valueTracker||(e._valueTracker=pm(e))}function wr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=jr(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function li(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var ym=/[\n"\\]/g;function ft(e){return e.replace(ym,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Hu(e,t,l,a,n,i,o,h){e.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.type=o:e.removeAttribute("type"),t!=null?o==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+ot(t)):e.value!==""+ot(t)&&(e.value=""+ot(t)):o!=="submit"&&o!=="reset"||e.removeAttribute("value"),t!=null?qu(e,o,ot(t)):l!=null?qu(e,o,ot(l)):a!=null&&e.removeAttribute("value"),n==null&&i!=null&&(e.defaultChecked=!!i),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+ot(h):e.removeAttribute("name")}function Dr(e,t,l,a,n,i,o,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;l=l!=null?""+ot(l):"",t=t!=null?""+ot(t):l,h||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=h?e.checked:!!a,e.defaultChecked=!!a,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.name=o)}function qu(e,t,l){t==="number"&&li(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Pl(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+ot(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Rr(e,t,l){if(t!=null&&(t=""+ot(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+ot(l):""}function Mr(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(c(92));if(Ut(a)){if(1<a.length)throw Error(c(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=ot(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Fl(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var xm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Or(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||xm.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Ur(e,t,l){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&Or(e,n,a)}else for(var i in t)t.hasOwnProperty(i)&&Or(e,i,t[i])}function Bu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var vm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),bm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ai(e){return bm.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Gu=null;function Vu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ea=null,ta=null;function zr(e){var t=Il(e);if(t&&(e=t.stateNode)){var l=e[Je]||null;e:switch(e=t.stateNode,t.type){case"input":if(Hu(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+ft(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[Je]||null;if(!n)throw Error(c(90));Hu(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&wr(a)}break e;case"textarea":Rr(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Pl(e,!!l.multiple,t,!1)}}}var Yu=!1;function Cr(e,t,l){if(Yu)return e(t,l);Yu=!0;try{var a=e(t);return a}finally{if(Yu=!1,(ea!==null||ta!==null)&&(Yi(),ea&&(t=ea,e=ta,ta=ea=null,zr(t),e)))for(t=0;t<e.length;t++)zr(e[t])}}function Za(e,t){var l=e.stateNode;if(l===null)return null;var a=l[Je]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(c(231,t,typeof l));return l}var Ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Xu=!1;if(Ct)try{var Qa={};Object.defineProperty(Qa,"passive",{get:function(){Xu=!0}}),window.addEventListener("test",Qa,Qa),window.removeEventListener("test",Qa,Qa)}catch{Xu=!1}var tl=null,Zu=null,ni=null;function Lr(){if(ni)return ni;var e,t=Zu,l=t.length,a,n="value"in tl?tl.value:tl.textContent,i=n.length;for(e=0;e<l&&t[e]===n[e];e++);var o=l-e;for(a=1;a<=o&&t[l-a]===n[i-a];a++);return ni=n.slice(e,1<a?1-a:void 0)}function ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ui(){return!0}function Hr(){return!1}function We(e){function t(l,a,n,i,o){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(l=e[h],this[h]=l?l(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ui:Hr,this.isPropagationStopped=Hr,this}return E(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=ui)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=ui)},persist:function(){},isPersistent:ui}),t}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},si=We(wl),Ka=E({},wl,{view:0,detail:0}),Sm=We(Ka),Qu,Ku,$a,ci=E({},Ka,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Iu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$a&&($a&&e.type==="mousemove"?(Qu=e.screenX-$a.screenX,Ku=e.screenY-$a.screenY):Ku=Qu=0,$a=e),Qu)},movementY:function(e){return"movementY"in e?e.movementY:Ku}}),qr=We(ci),Em=E({},ci,{dataTransfer:0}),_m=We(Em),Nm=E({},Ka,{relatedTarget:0}),$u=We(Nm),Tm=E({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),Am=We(Tm),jm=E({},wl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wm=We(jm),Dm=E({},wl,{data:0}),Br=We(Dm),Rm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Mm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Om={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Um(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Om[e])?!!t[e]:!1}function Iu(){return Um}var zm=E({},Ka,{key:function(e){if(e.key){var t=Rm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Mm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Iu,charCode:function(e){return e.type==="keypress"?ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cm=We(zm),Lm=E({},ci,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gr=We(Lm),Hm=E({},Ka,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Iu}),qm=We(Hm),Bm=E({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Gm=We(Bm),Vm=E({},ci,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ym=We(Vm),Xm=E({},wl,{newState:0,oldState:0}),Zm=We(Xm),Qm=[9,13,27,32],ku=Ct&&"CompositionEvent"in window,Ia=null;Ct&&"documentMode"in document&&(Ia=document.documentMode);var Km=Ct&&"TextEvent"in window&&!Ia,Vr=Ct&&(!ku||Ia&&8<Ia&&11>=Ia),Yr=" ",Xr=!1;function Zr(e,t){switch(e){case"keyup":return Qm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var la=!1;function $m(e,t){switch(e){case"compositionend":return Qr(t);case"keypress":return t.which!==32?null:(Xr=!0,Yr);case"textInput":return e=t.data,e===Yr&&Xr?null:e;default:return null}}function Im(e,t){if(la)return e==="compositionend"||!ku&&Zr(e,t)?(e=Lr(),ni=Zu=tl=null,la=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Vr&&t.locale!=="ko"?null:t.data;default:return null}}var km={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!km[e.type]:t==="textarea"}function $r(e,t,l,a){ea?ta?ta.push(a):ta=[a]:ea=a,t=Ii(t,"onChange"),0<t.length&&(l=new si("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var ka=null,Ja=null;function Jm(e){Dd(e,0)}function ri(e){var t=Xa(e);if(wr(t))return e}function Ir(e,t){if(e==="change")return t}var kr=!1;if(Ct){var Ju;if(Ct){var Wu="oninput"in document;if(!Wu){var Jr=document.createElement("div");Jr.setAttribute("oninput","return;"),Wu=typeof Jr.oninput=="function"}Ju=Wu}else Ju=!1;kr=Ju&&(!document.documentMode||9<document.documentMode)}function Wr(){ka&&(ka.detachEvent("onpropertychange",Pr),Ja=ka=null)}function Pr(e){if(e.propertyName==="value"&&ri(Ja)){var t=[];$r(t,Ja,e,Vu(e)),Cr(Jm,t)}}function Wm(e,t,l){e==="focusin"?(Wr(),ka=t,Ja=l,ka.attachEvent("onpropertychange",Pr)):e==="focusout"&&Wr()}function Pm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ri(Ja)}function Fm(e,t){if(e==="click")return ri(t)}function e0(e,t){if(e==="input"||e==="change")return ri(t)}function t0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:t0;function Wa(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!Au.call(t,n)||!at(e[n],t[n]))return!1}return!0}function Fr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function eo(e,t){var l=Fr(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Fr(l)}}function to(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?to(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function lo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=li(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=li(e.document)}return t}function Pu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var l0=Ct&&"documentMode"in document&&11>=document.documentMode,aa=null,Fu=null,Pa=null,es=!1;function ao(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;es||aa==null||aa!==li(a)||(a=aa,"selectionStart"in a&&Pu(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Pa&&Wa(Pa,a)||(Pa=a,a=Ii(Fu,"onSelect"),0<a.length&&(t=new si("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=aa)))}function Dl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var na={animationend:Dl("Animation","AnimationEnd"),animationiteration:Dl("Animation","AnimationIteration"),animationstart:Dl("Animation","AnimationStart"),transitionrun:Dl("Transition","TransitionRun"),transitionstart:Dl("Transition","TransitionStart"),transitioncancel:Dl("Transition","TransitionCancel"),transitionend:Dl("Transition","TransitionEnd")},ts={},no={};Ct&&(no=document.createElement("div").style,"AnimationEvent"in window||(delete na.animationend.animation,delete na.animationiteration.animation,delete na.animationstart.animation),"TransitionEvent"in window||delete na.transitionend.transition);function Rl(e){if(ts[e])return ts[e];if(!na[e])return e;var t=na[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in no)return ts[e]=t[l];return e}var io=Rl("animationend"),uo=Rl("animationiteration"),so=Rl("animationstart"),a0=Rl("transitionrun"),n0=Rl("transitionstart"),i0=Rl("transitioncancel"),co=Rl("transitionend"),ro=new Map,ls="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ls.push("scrollEnd");function bt(e,t){ro.set(e,t),jl(t,[e])}var oo=new WeakMap;function dt(e,t){if(typeof e=="object"&&e!==null){var l=oo.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Ar(t)},oo.set(e,t),t)}return{value:e,source:t,stack:Ar(t)}}var ht=[],ia=0,as=0;function oi(){for(var e=ia,t=as=ia=0;t<e;){var l=ht[t];ht[t++]=null;var a=ht[t];ht[t++]=null;var n=ht[t];ht[t++]=null;var i=ht[t];if(ht[t++]=null,a!==null&&n!==null){var o=a.pending;o===null?n.next=n:(n.next=o.next,o.next=n),a.pending=n}i!==0&&fo(l,n,i)}}function fi(e,t,l,a){ht[ia++]=e,ht[ia++]=t,ht[ia++]=l,ht[ia++]=a,as|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function ns(e,t,l,a){return fi(e,t,l,a),di(e)}function ua(e,t){return fi(e,null,null,t),di(e)}function fo(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=e.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(n=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,n&&t!==null&&(n=31-lt(l),e=i.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),i):null}function di(e){if(50<Nn)throw Nn=0,oc=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var sa={};function u0(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,l,a){return new u0(e,t,l,a)}function is(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lt(e,t){var l=e.alternate;return l===null?(l=nt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function ho(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function hi(e,t,l,a,n,i){var o=0;if(a=e,typeof e=="function")is(e)&&(o=1);else if(typeof e=="string")o=cg(e,l,qe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case De:return e=nt(31,l,t,n),e.elementType=De,e.lanes=i,e;case R:return Ml(l.children,n,i,t);case D:o=8,n|=24;break;case L:return e=nt(12,l,t,n|2),e.elementType=L,e.lanes=i,e;case W:return e=nt(13,l,t,n),e.elementType=W,e.lanes=i,e;case xe:return e=nt(19,l,t,n),e.elementType=xe,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case U:case B:o=10;break e;case V:o=9;break e;case X:o=11;break e;case ue:o=14;break e;case _e:o=16,a=null;break e}o=29,l=Error(c(130,e===null?"null":typeof e,"")),a=null}return t=nt(o,l,t,n),t.elementType=e,t.type=a,t.lanes=i,t}function Ml(e,t,l,a){return e=nt(7,e,a,t),e.lanes=l,e}function us(e,t,l){return e=nt(6,e,null,t),e.lanes=l,e}function ss(e,t,l){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ca=[],ra=0,mi=null,gi=0,mt=[],gt=0,Ol=null,Ht=1,qt="";function Ul(e,t){ca[ra++]=gi,ca[ra++]=mi,mi=e,gi=t}function mo(e,t,l){mt[gt++]=Ht,mt[gt++]=qt,mt[gt++]=Ol,Ol=e;var a=Ht;e=qt;var n=32-lt(a)-1;a&=~(1<<n),l+=1;var i=32-lt(t)+n;if(30<i){var o=n-n%5;i=(a&(1<<o)-1).toString(32),a>>=o,n-=o,Ht=1<<32-lt(t)+n|l<<n|a,qt=i+e}else Ht=1<<i|l<<n|a,qt=e}function cs(e){e.return!==null&&(Ul(e,1),mo(e,1,0))}function rs(e){for(;e===mi;)mi=ca[--ra],ca[ra]=null,gi=ca[--ra],ca[ra]=null;for(;e===Ol;)Ol=mt[--gt],mt[gt]=null,qt=mt[--gt],mt[gt]=null,Ht=mt[--gt],mt[gt]=null}var ke=null,je=null,se=!1,zl=null,wt=!1,os=Error(c(519));function Cl(e){var t=Error(c(418,""));throw tn(dt(t,e)),os}function go(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Ke]=e,t[Je]=a,l){case"dialog":te("cancel",t),te("close",t);break;case"iframe":case"object":case"embed":te("load",t);break;case"video":case"audio":for(l=0;l<An.length;l++)te(An[l],t);break;case"source":te("error",t);break;case"img":case"image":case"link":te("error",t),te("load",t);break;case"details":te("toggle",t);break;case"input":te("invalid",t),Dr(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),ti(t);break;case"select":te("invalid",t);break;case"textarea":te("invalid",t),Mr(t,a.value,a.defaultValue,a.children),ti(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Ud(t.textContent,l)?(a.popover!=null&&(te("beforetoggle",t),te("toggle",t)),a.onScroll!=null&&te("scroll",t),a.onScrollEnd!=null&&te("scrollend",t),a.onClick!=null&&(t.onclick=ki),t=!0):t=!1,t||Cl(e)}function po(e){for(ke=e.return;ke;)switch(ke.tag){case 5:case 13:wt=!1;return;case 27:case 3:wt=!0;return;default:ke=ke.return}}function Fa(e){if(e!==ke)return!1;if(!se)return po(e),se=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||Ac(e.type,e.memoizedProps)),l=!l),l&&je&&Cl(e),po(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){je=Et(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}je=null}}else t===27?(t=je,yl(e.type)?(e=Rc,Rc=null,je=e):je=t):je=ke?Et(e.stateNode.nextSibling):null;return!0}function en(){je=ke=null,se=!1}function yo(){var e=zl;return e!==null&&(et===null?et=e:et.push.apply(et,e),zl=null),e}function tn(e){zl===null?zl=[e]:zl.push(e)}var fs=Ie(null),Ll=null,Bt=null;function ll(e,t,l){fe(fs,t._currentValue),t._currentValue=l}function Gt(e){e._currentValue=fs.current,Ne(fs)}function ds(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function hs(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var i=n.dependencies;if(i!==null){var o=n.child;i=i.firstContext;e:for(;i!==null;){var h=i;i=n;for(var g=0;g<t.length;g++)if(h.context===t[g]){i.lanes|=l,h=i.alternate,h!==null&&(h.lanes|=l),ds(i.return,l,e),a||(o=null);break e}i=h.next}}else if(n.tag===18){if(o=n.return,o===null)throw Error(c(341));o.lanes|=l,i=o.alternate,i!==null&&(i.lanes|=l),ds(o,l,e),o=null}else o=n.child;if(o!==null)o.return=n;else for(o=n;o!==null;){if(o===e){o=null;break}if(n=o.sibling,n!==null){n.return=o.return,o=n;break}o=o.return}n=o}}function ln(e,t,l,a){e=null;for(var n=t,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var o=n.alternate;if(o===null)throw Error(c(387));if(o=o.memoizedProps,o!==null){var h=n.type;at(n.pendingProps.value,o.value)||(e!==null?e.push(h):e=[h])}}else if(n===Kn.current){if(o=n.alternate,o===null)throw Error(c(387));o.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(On):e=[On])}n=n.return}e!==null&&hs(t,e,l,a),t.flags|=262144}function pi(e){for(e=e.firstContext;e!==null;){if(!at(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Hl(e){Ll=e,Bt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function $e(e){return xo(Ll,e)}function yi(e,t){return Ll===null&&Hl(e),xo(e,t)}function xo(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Bt===null){if(e===null)throw Error(c(308));Bt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Bt=Bt.next=t;return l}var s0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},c0=u.unstable_scheduleCallback,r0=u.unstable_NormalPriority,Le={$$typeof:B,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ms(){return{controller:new s0,data:new Map,refCount:0}}function an(e){e.refCount--,e.refCount===0&&c0(r0,function(){e.controller.abort()})}var nn=null,gs=0,oa=0,fa=null;function o0(e,t){if(nn===null){var l=nn=[];gs=0,oa=yc(),fa={status:"pending",value:void 0,then:function(a){l.push(a)}}}return gs++,t.then(vo,vo),t}function vo(){if(--gs===0&&nn!==null){fa!==null&&(fa.status="fulfilled");var e=nn;nn=null,oa=0,fa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function f0(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var bo=C.S;C.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&o0(e,t),bo!==null&&bo(e,t)};var ql=Ie(null);function ps(){var e=ql.current;return e!==null?e:ye.pooledCache}function xi(e,t){t===null?fe(ql,ql.current):fe(ql,t.pool)}function So(){var e=ps();return e===null?null:{parent:Le._currentValue,pool:e}}var un=Error(c(460)),Eo=Error(c(474)),vi=Error(c(542)),ys={then:function(){}};function _o(e){return e=e.status,e==="fulfilled"||e==="rejected"}function bi(){}function No(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(bi,bi),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ao(e),e;default:if(typeof t.status=="string")t.then(bi,bi);else{if(e=ye,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Ao(e),e}throw sn=t,un}}var sn=null;function To(){if(sn===null)throw Error(c(459));var e=sn;return sn=null,e}function Ao(e){if(e===un||e===vi)throw Error(c(483))}var al=!1;function xs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function vs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function nl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function il(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(re&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=di(e),fo(e,null,l),t}return fi(e,a,t,l),di(e)}function cn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,xr(e,l)}}function bs(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var o={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=o:i=i.next=o,l=l.next}while(l!==null);i===null?n=i=t:i=i.next=t}else n=i=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Ss=!1;function rn(){if(Ss){var e=fa;if(e!==null)throw e}}function on(e,t,l,a){Ss=!1;var n=e.updateQueue;al=!1;var i=n.firstBaseUpdate,o=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var g=h,N=g.next;g.next=null,o===null?i=N:o.next=N,o=g;var O=e.alternate;O!==null&&(O=O.updateQueue,h=O.lastBaseUpdate,h!==o&&(h===null?O.firstBaseUpdate=N:h.next=N,O.lastBaseUpdate=g))}if(i!==null){var H=n.baseState;o=0,O=N=g=null,h=i;do{var A=h.lane&-536870913,j=A!==h.lane;if(j?(le&A)===A:(a&A)===A){A!==0&&A===oa&&(Ss=!0),O!==null&&(O=O.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var k=e,K=h;A=t;var me=l;switch(K.tag){case 1:if(k=K.payload,typeof k=="function"){H=k.call(me,H,A);break e}H=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=K.payload,A=typeof k=="function"?k.call(me,H,A):k,A==null)break e;H=E({},H,A);break e;case 2:al=!0}}A=h.callback,A!==null&&(e.flags|=64,j&&(e.flags|=8192),j=n.callbacks,j===null?n.callbacks=[A]:j.push(A))}else j={lane:A,tag:h.tag,payload:h.payload,callback:h.callback,next:null},O===null?(N=O=j,g=H):O=O.next=j,o|=A;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;j=h,h=j.next,j.next=null,n.lastBaseUpdate=j,n.shared.pending=null}}while(!0);O===null&&(g=H),n.baseState=g,n.firstBaseUpdate=N,n.lastBaseUpdate=O,i===null&&(n.shared.lanes=0),hl|=o,e.lanes=o,e.memoizedState=H}}function jo(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function wo(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)jo(l[e],t)}var da=Ie(null),Si=Ie(0);function Do(e,t){e=$t,fe(Si,e),fe(da,t),$t=e|t.baseLanes}function Es(){fe(Si,$t),fe(da,da.current)}function _s(){$t=Si.current,Ne(da),Ne(Si)}var ul=0,P=null,de=null,Ue=null,Ei=!1,ha=!1,Bl=!1,_i=0,fn=0,ma=null,d0=0;function Re(){throw Error(c(321))}function Ns(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!at(e[l],t[l]))return!1;return!0}function Ts(e,t,l,a,n,i){return ul=i,P=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,C.H=e===null||e.memoizedState===null?hf:mf,Bl=!1,i=l(a,n),Bl=!1,ha&&(i=Mo(t,l,a,n)),Ro(e),i}function Ro(e){C.H=Di;var t=de!==null&&de.next!==null;if(ul=0,Ue=de=P=null,Ei=!1,fn=0,ma=null,t)throw Error(c(300));e===null||Ge||(e=e.dependencies,e!==null&&pi(e)&&(Ge=!0))}function Mo(e,t,l,a){P=e;var n=0;do{if(ha&&(ma=null),fn=0,ha=!1,25<=n)throw Error(c(301));if(n+=1,Ue=de=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}C.H=v0,i=t(l,a)}while(ha);return i}function h0(){var e=C.H,t=e.useState()[0];return t=typeof t.then=="function"?dn(t):t,e=e.useState()[0],(de!==null?de.memoizedState:null)!==e&&(P.flags|=1024),t}function As(){var e=_i!==0;return _i=0,e}function js(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function ws(e){if(Ei){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ei=!1}ul=0,Ue=de=P=null,ha=!1,fn=_i=0,ma=null}function Pe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ue===null?P.memoizedState=Ue=e:Ue=Ue.next=e,Ue}function ze(){if(de===null){var e=P.alternate;e=e!==null?e.memoizedState:null}else e=de.next;var t=Ue===null?P.memoizedState:Ue.next;if(t!==null)Ue=t,de=e;else{if(e===null)throw P.alternate===null?Error(c(467)):Error(c(310));de=e,e={memoizedState:de.memoizedState,baseState:de.baseState,baseQueue:de.baseQueue,queue:de.queue,next:null},Ue===null?P.memoizedState=Ue=e:Ue=Ue.next=e}return Ue}function Ds(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function dn(e){var t=fn;return fn+=1,ma===null&&(ma=[]),e=No(ma,e,t),t=P,(Ue===null?t.memoizedState:Ue.next)===null&&(t=t.alternate,C.H=t===null||t.memoizedState===null?hf:mf),e}function Ni(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return dn(e);if(e.$$typeof===B)return $e(e)}throw Error(c(438,String(e)))}function Rs(e){var t=null,l=P.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=P.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=Ds(),P.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=ne;return t.index++,l}function Vt(e,t){return typeof t=="function"?t(e):t}function Ti(e){var t=ze();return Ms(t,de,e)}function Ms(e,t,l){var a=e.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=l;var n=e.baseQueue,i=a.pending;if(i!==null){if(n!==null){var o=n.next;n.next=i.next,i.next=o}t.baseQueue=n=i,a.pending=null}if(i=e.baseState,n===null)e.memoizedState=i;else{t=n.next;var h=o=null,g=null,N=t,O=!1;do{var H=N.lane&-536870913;if(H!==N.lane?(le&H)===H:(ul&H)===H){var A=N.revertLane;if(A===0)g!==null&&(g=g.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),H===oa&&(O=!0);else if((ul&A)===A){N=N.next,A===oa&&(O=!0);continue}else H={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},g===null?(h=g=H,o=i):g=g.next=H,P.lanes|=A,hl|=A;H=N.action,Bl&&l(i,H),i=N.hasEagerState?N.eagerState:l(i,H)}else A={lane:H,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},g===null?(h=g=A,o=i):g=g.next=A,P.lanes|=H,hl|=H;N=N.next}while(N!==null&&N!==t);if(g===null?o=i:g.next=h,!at(i,e.memoizedState)&&(Ge=!0,O&&(l=fa,l!==null)))throw l;e.memoizedState=i,e.baseState=o,e.baseQueue=g,a.lastRenderedState=i}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Os(e){var t=ze(),l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,i=t.memoizedState;if(n!==null){l.pending=null;var o=n=n.next;do i=e(i,o.action),o=o.next;while(o!==n);at(i,t.memoizedState)||(Ge=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),l.lastRenderedState=i}return[i,a]}function Oo(e,t,l){var a=P,n=ze(),i=se;if(i){if(l===void 0)throw Error(c(407));l=l()}else l=t();var o=!at((de||n).memoizedState,l);o&&(n.memoizedState=l,Ge=!0),n=n.queue;var h=Co.bind(null,a,n,e);if(hn(2048,8,h,[e]),n.getSnapshot!==t||o||Ue!==null&&Ue.memoizedState.tag&1){if(a.flags|=2048,ga(9,Ai(),zo.bind(null,a,n,l,t),null),ye===null)throw Error(c(349));i||(ul&124)!==0||Uo(a,t,l)}return l}function Uo(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=P.updateQueue,t===null?(t=Ds(),P.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function zo(e,t,l,a){t.value=l,t.getSnapshot=a,Lo(t)&&Ho(e)}function Co(e,t,l){return l(function(){Lo(t)&&Ho(e)})}function Lo(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!at(e,l)}catch{return!0}}function Ho(e){var t=ua(e,2);t!==null&&rt(t,e,2)}function Us(e){var t=Pe();if(typeof e=="function"){var l=e;if(e=l(),Bl){Ft(!0);try{l()}finally{Ft(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:e},t}function qo(e,t,l,a){return e.baseState=l,Ms(e,de,typeof a=="function"?a:Vt)}function m0(e,t,l,a,n){if(wi(e))throw Error(c(485));if(e=t.action,e!==null){var i={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){i.listeners.push(o)}};C.T!==null?l(!0):i.isTransition=!1,a(i),l=t.pending,l===null?(i.next=t.pending=i,Bo(t,i)):(i.next=l.next,t.pending=l.next=i)}}function Bo(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var i=C.T,o={};C.T=o;try{var h=l(n,a),g=C.S;g!==null&&g(o,h),Go(e,t,h)}catch(N){zs(e,t,N)}finally{C.T=i}}else try{i=l(n,a),Go(e,t,i)}catch(N){zs(e,t,N)}}function Go(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Vo(e,t,a)},function(a){return zs(e,t,a)}):Vo(e,t,l)}function Vo(e,t,l){t.status="fulfilled",t.value=l,Yo(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Bo(e,l)))}function zs(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,Yo(t),t=t.next;while(t!==a)}e.action=null}function Yo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Xo(e,t){return t}function Zo(e,t){if(se){var l=ye.formState;if(l!==null){e:{var a=P;if(se){if(je){t:{for(var n=je,i=wt;n.nodeType!==8;){if(!i){n=null;break t}if(n=Et(n.nextSibling),n===null){n=null;break t}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){je=Et(n.nextSibling),a=n.data==="F!";break e}}Cl(a)}a=!1}a&&(t=l[0])}}return l=Pe(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xo,lastRenderedState:t},l.queue=a,l=of.bind(null,P,a),a.dispatch=l,a=Us(!1),i=Bs.bind(null,P,!1,a.queue),a=Pe(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=m0.bind(null,P,n,i,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function Qo(e){var t=ze();return Ko(t,de,e)}function Ko(e,t,l){if(t=Ms(e,t,Xo)[0],e=Ti(Vt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=dn(t)}catch(o){throw o===un?vi:o}else a=t;t=ze();var n=t.queue,i=n.dispatch;return l!==t.memoizedState&&(P.flags|=2048,ga(9,Ai(),g0.bind(null,n,l),null)),[a,i,e]}function g0(e,t){e.action=t}function $o(e){var t=ze(),l=de;if(l!==null)return Ko(t,l,e);ze(),t=t.memoizedState,l=ze();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function ga(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=P.updateQueue,t===null&&(t=Ds(),P.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Ai(){return{destroy:void 0,resource:void 0}}function Io(){return ze().memoizedState}function ji(e,t,l,a){var n=Pe();a=a===void 0?null:a,P.flags|=e,n.memoizedState=ga(1|t,Ai(),l,a)}function hn(e,t,l,a){var n=ze();a=a===void 0?null:a;var i=n.memoizedState.inst;de!==null&&a!==null&&Ns(a,de.memoizedState.deps)?n.memoizedState=ga(t,i,l,a):(P.flags|=e,n.memoizedState=ga(1|t,i,l,a))}function ko(e,t){ji(8390656,8,e,t)}function Jo(e,t){hn(2048,8,e,t)}function Wo(e,t){return hn(4,2,e,t)}function Po(e,t){return hn(4,4,e,t)}function Fo(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ef(e,t,l){l=l!=null?l.concat([e]):null,hn(4,4,Fo.bind(null,t,e),l)}function Cs(){}function tf(e,t){var l=ze();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Ns(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function lf(e,t){var l=ze();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Ns(t,a[1]))return a[0];if(a=e(),Bl){Ft(!0);try{e()}finally{Ft(!1)}}return l.memoizedState=[a,t],a}function Ls(e,t,l){return l===void 0||(ul&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=ud(),P.lanes|=e,hl|=e,l)}function af(e,t,l,a){return at(l,t)?l:da.current!==null?(e=Ls(e,l,a),at(e,t)||(Ge=!0),e):(ul&42)===0?(Ge=!0,e.memoizedState=l):(e=ud(),P.lanes|=e,hl|=e,t)}function nf(e,t,l,a,n){var i=G.p;G.p=i!==0&&8>i?i:8;var o=C.T,h={};C.T=h,Bs(e,!1,t,l);try{var g=n(),N=C.S;if(N!==null&&N(h,g),g!==null&&typeof g=="object"&&typeof g.then=="function"){var O=f0(g,a);mn(e,t,O,ct(e))}else mn(e,t,a,ct(e))}catch(H){mn(e,t,{then:function(){},status:"rejected",reason:H},ct())}finally{G.p=i,C.T=o}}function p0(){}function Hs(e,t,l,a){if(e.tag!==5)throw Error(c(476));var n=uf(e).queue;nf(e,n,t,J,l===null?p0:function(){return sf(e),l(a)})}function uf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:J,baseState:J,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:J},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function sf(e){var t=uf(e).next.queue;mn(e,t,{},ct())}function qs(){return $e(On)}function cf(){return ze().memoizedState}function rf(){return ze().memoizedState}function y0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=ct();e=nl(l);var a=il(t,e,l);a!==null&&(rt(a,t,l),cn(a,t,l)),t={cache:ms()},e.payload=t;return}t=t.return}}function x0(e,t,l){var a=ct();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},wi(e)?ff(t,l):(l=ns(e,t,l,a),l!==null&&(rt(l,e,a),df(l,t,a)))}function of(e,t,l){var a=ct();mn(e,t,l,a)}function mn(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(wi(e))ff(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,h=i(o,l);if(n.hasEagerState=!0,n.eagerState=h,at(h,o))return fi(e,t,n,0),ye===null&&oi(),!1}catch{}finally{}if(l=ns(e,t,n,a),l!==null)return rt(l,e,a),df(l,t,a),!0}return!1}function Bs(e,t,l,a){if(a={lane:2,revertLane:yc(),action:a,hasEagerState:!1,eagerState:null,next:null},wi(e)){if(t)throw Error(c(479))}else t=ns(e,l,a,2),t!==null&&rt(t,e,2)}function wi(e){var t=e.alternate;return e===P||t!==null&&t===P}function ff(e,t){ha=Ei=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function df(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,xr(e,l)}}var Di={readContext:$e,use:Ni,useCallback:Re,useContext:Re,useEffect:Re,useImperativeHandle:Re,useLayoutEffect:Re,useInsertionEffect:Re,useMemo:Re,useReducer:Re,useRef:Re,useState:Re,useDebugValue:Re,useDeferredValue:Re,useTransition:Re,useSyncExternalStore:Re,useId:Re,useHostTransitionStatus:Re,useFormState:Re,useActionState:Re,useOptimistic:Re,useMemoCache:Re,useCacheRefresh:Re},hf={readContext:$e,use:Ni,useCallback:function(e,t){return Pe().memoizedState=[e,t===void 0?null:t],e},useContext:$e,useEffect:ko,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,ji(4194308,4,Fo.bind(null,t,e),l)},useLayoutEffect:function(e,t){return ji(4194308,4,e,t)},useInsertionEffect:function(e,t){ji(4,2,e,t)},useMemo:function(e,t){var l=Pe();t=t===void 0?null:t;var a=e();if(Bl){Ft(!0);try{e()}finally{Ft(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=Pe();if(l!==void 0){var n=l(t);if(Bl){Ft(!0);try{l(t)}finally{Ft(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=x0.bind(null,P,e),[a.memoizedState,e]},useRef:function(e){var t=Pe();return e={current:e},t.memoizedState=e},useState:function(e){e=Us(e);var t=e.queue,l=of.bind(null,P,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:Cs,useDeferredValue:function(e,t){var l=Pe();return Ls(l,e,t)},useTransition:function(){var e=Us(!1);return e=nf.bind(null,P,e.queue,!0,!1),Pe().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=P,n=Pe();if(se){if(l===void 0)throw Error(c(407));l=l()}else{if(l=t(),ye===null)throw Error(c(349));(le&124)!==0||Uo(a,t,l)}n.memoizedState=l;var i={value:l,getSnapshot:t};return n.queue=i,ko(Co.bind(null,a,i,e),[e]),a.flags|=2048,ga(9,Ai(),zo.bind(null,a,i,l,t),null),l},useId:function(){var e=Pe(),t=ye.identifierPrefix;if(se){var l=qt,a=Ht;l=(a&~(1<<32-lt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=_i++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=d0++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:qs,useFormState:Zo,useActionState:Zo,useOptimistic:function(e){var t=Pe();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=Bs.bind(null,P,!0,l),l.dispatch=t,[e,t]},useMemoCache:Rs,useCacheRefresh:function(){return Pe().memoizedState=y0.bind(null,P)}},mf={readContext:$e,use:Ni,useCallback:tf,useContext:$e,useEffect:Jo,useImperativeHandle:ef,useInsertionEffect:Wo,useLayoutEffect:Po,useMemo:lf,useReducer:Ti,useRef:Io,useState:function(){return Ti(Vt)},useDebugValue:Cs,useDeferredValue:function(e,t){var l=ze();return af(l,de.memoizedState,e,t)},useTransition:function(){var e=Ti(Vt)[0],t=ze().memoizedState;return[typeof e=="boolean"?e:dn(e),t]},useSyncExternalStore:Oo,useId:cf,useHostTransitionStatus:qs,useFormState:Qo,useActionState:Qo,useOptimistic:function(e,t){var l=ze();return qo(l,de,e,t)},useMemoCache:Rs,useCacheRefresh:rf},v0={readContext:$e,use:Ni,useCallback:tf,useContext:$e,useEffect:Jo,useImperativeHandle:ef,useInsertionEffect:Wo,useLayoutEffect:Po,useMemo:lf,useReducer:Os,useRef:Io,useState:function(){return Os(Vt)},useDebugValue:Cs,useDeferredValue:function(e,t){var l=ze();return de===null?Ls(l,e,t):af(l,de.memoizedState,e,t)},useTransition:function(){var e=Os(Vt)[0],t=ze().memoizedState;return[typeof e=="boolean"?e:dn(e),t]},useSyncExternalStore:Oo,useId:cf,useHostTransitionStatus:qs,useFormState:$o,useActionState:$o,useOptimistic:function(e,t){var l=ze();return de!==null?qo(l,de,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:Rs,useCacheRefresh:rf},pa=null,gn=0;function Ri(e){var t=gn;return gn+=1,pa===null&&(pa=[]),No(pa,e,t)}function pn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Mi(e,t){throw t.$$typeof===M?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function gf(e){var t=e._init;return t(e._payload)}function pf(e){function t(S,b){if(e){var _=S.deletions;_===null?(S.deletions=[b],S.flags|=16):_.push(b)}}function l(S,b){if(!e)return null;for(;b!==null;)t(S,b),b=b.sibling;return null}function a(S){for(var b=new Map;S!==null;)S.key!==null?b.set(S.key,S):b.set(S.index,S),S=S.sibling;return b}function n(S,b){return S=Lt(S,b),S.index=0,S.sibling=null,S}function i(S,b,_){return S.index=_,e?(_=S.alternate,_!==null?(_=_.index,_<b?(S.flags|=67108866,b):_):(S.flags|=67108866,b)):(S.flags|=1048576,b)}function o(S){return e&&S.alternate===null&&(S.flags|=67108866),S}function h(S,b,_,z){return b===null||b.tag!==6?(b=us(_,S.mode,z),b.return=S,b):(b=n(b,_),b.return=S,b)}function g(S,b,_,z){var Y=_.type;return Y===R?O(S,b,_.props.children,z,_.key):b!==null&&(b.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===_e&&gf(Y)===b.type)?(b=n(b,_.props),pn(b,_),b.return=S,b):(b=hi(_.type,_.key,_.props,null,S.mode,z),pn(b,_),b.return=S,b)}function N(S,b,_,z){return b===null||b.tag!==4||b.stateNode.containerInfo!==_.containerInfo||b.stateNode.implementation!==_.implementation?(b=ss(_,S.mode,z),b.return=S,b):(b=n(b,_.children||[]),b.return=S,b)}function O(S,b,_,z,Y){return b===null||b.tag!==7?(b=Ml(_,S.mode,z,Y),b.return=S,b):(b=n(b,_),b.return=S,b)}function H(S,b,_){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=us(""+b,S.mode,_),b.return=S,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case T:return _=hi(b.type,b.key,b.props,null,S.mode,_),pn(_,b),_.return=S,_;case q:return b=ss(b,S.mode,_),b.return=S,b;case _e:var z=b._init;return b=z(b._payload),H(S,b,_)}if(Ut(b)||Ae(b))return b=Ml(b,S.mode,_,null),b.return=S,b;if(typeof b.then=="function")return H(S,Ri(b),_);if(b.$$typeof===B)return H(S,yi(S,b),_);Mi(S,b)}return null}function A(S,b,_,z){var Y=b!==null?b.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return Y!==null?null:h(S,b,""+_,z);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case T:return _.key===Y?g(S,b,_,z):null;case q:return _.key===Y?N(S,b,_,z):null;case _e:return Y=_._init,_=Y(_._payload),A(S,b,_,z)}if(Ut(_)||Ae(_))return Y!==null?null:O(S,b,_,z,null);if(typeof _.then=="function")return A(S,b,Ri(_),z);if(_.$$typeof===B)return A(S,b,yi(S,_),z);Mi(S,_)}return null}function j(S,b,_,z,Y){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return S=S.get(_)||null,h(b,S,""+z,Y);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case T:return S=S.get(z.key===null?_:z.key)||null,g(b,S,z,Y);case q:return S=S.get(z.key===null?_:z.key)||null,N(b,S,z,Y);case _e:var F=z._init;return z=F(z._payload),j(S,b,_,z,Y)}if(Ut(z)||Ae(z))return S=S.get(_)||null,O(b,S,z,Y,null);if(typeof z.then=="function")return j(S,b,_,Ri(z),Y);if(z.$$typeof===B)return j(S,b,_,yi(b,z),Y);Mi(b,z)}return null}function k(S,b,_,z){for(var Y=null,F=null,Z=b,I=b=0,Ye=null;Z!==null&&I<_.length;I++){Z.index>I?(Ye=Z,Z=null):Ye=Z.sibling;var ie=A(S,Z,_[I],z);if(ie===null){Z===null&&(Z=Ye);break}e&&Z&&ie.alternate===null&&t(S,Z),b=i(ie,b,I),F===null?Y=ie:F.sibling=ie,F=ie,Z=Ye}if(I===_.length)return l(S,Z),se&&Ul(S,I),Y;if(Z===null){for(;I<_.length;I++)Z=H(S,_[I],z),Z!==null&&(b=i(Z,b,I),F===null?Y=Z:F.sibling=Z,F=Z);return se&&Ul(S,I),Y}for(Z=a(Z);I<_.length;I++)Ye=j(Z,S,I,_[I],z),Ye!==null&&(e&&Ye.alternate!==null&&Z.delete(Ye.key===null?I:Ye.key),b=i(Ye,b,I),F===null?Y=Ye:F.sibling=Ye,F=Ye);return e&&Z.forEach(function(El){return t(S,El)}),se&&Ul(S,I),Y}function K(S,b,_,z){if(_==null)throw Error(c(151));for(var Y=null,F=null,Z=b,I=b=0,Ye=null,ie=_.next();Z!==null&&!ie.done;I++,ie=_.next()){Z.index>I?(Ye=Z,Z=null):Ye=Z.sibling;var El=A(S,Z,ie.value,z);if(El===null){Z===null&&(Z=Ye);break}e&&Z&&El.alternate===null&&t(S,Z),b=i(El,b,I),F===null?Y=El:F.sibling=El,F=El,Z=Ye}if(ie.done)return l(S,Z),se&&Ul(S,I),Y;if(Z===null){for(;!ie.done;I++,ie=_.next())ie=H(S,ie.value,z),ie!==null&&(b=i(ie,b,I),F===null?Y=ie:F.sibling=ie,F=ie);return se&&Ul(S,I),Y}for(Z=a(Z);!ie.done;I++,ie=_.next())ie=j(Z,S,I,ie.value,z),ie!==null&&(e&&ie.alternate!==null&&Z.delete(ie.key===null?I:ie.key),b=i(ie,b,I),F===null?Y=ie:F.sibling=ie,F=ie);return e&&Z.forEach(function(bg){return t(S,bg)}),se&&Ul(S,I),Y}function me(S,b,_,z){if(typeof _=="object"&&_!==null&&_.type===R&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case T:e:{for(var Y=_.key;b!==null;){if(b.key===Y){if(Y=_.type,Y===R){if(b.tag===7){l(S,b.sibling),z=n(b,_.props.children),z.return=S,S=z;break e}}else if(b.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===_e&&gf(Y)===b.type){l(S,b.sibling),z=n(b,_.props),pn(z,_),z.return=S,S=z;break e}l(S,b);break}else t(S,b);b=b.sibling}_.type===R?(z=Ml(_.props.children,S.mode,z,_.key),z.return=S,S=z):(z=hi(_.type,_.key,_.props,null,S.mode,z),pn(z,_),z.return=S,S=z)}return o(S);case q:e:{for(Y=_.key;b!==null;){if(b.key===Y)if(b.tag===4&&b.stateNode.containerInfo===_.containerInfo&&b.stateNode.implementation===_.implementation){l(S,b.sibling),z=n(b,_.children||[]),z.return=S,S=z;break e}else{l(S,b);break}else t(S,b);b=b.sibling}z=ss(_,S.mode,z),z.return=S,S=z}return o(S);case _e:return Y=_._init,_=Y(_._payload),me(S,b,_,z)}if(Ut(_))return k(S,b,_,z);if(Ae(_)){if(Y=Ae(_),typeof Y!="function")throw Error(c(150));return _=Y.call(_),K(S,b,_,z)}if(typeof _.then=="function")return me(S,b,Ri(_),z);if(_.$$typeof===B)return me(S,b,yi(S,_),z);Mi(S,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,b!==null&&b.tag===6?(l(S,b.sibling),z=n(b,_),z.return=S,S=z):(l(S,b),z=us(_,S.mode,z),z.return=S,S=z),o(S)):l(S,b)}return function(S,b,_,z){try{gn=0;var Y=me(S,b,_,z);return pa=null,Y}catch(Z){if(Z===un||Z===vi)throw Z;var F=nt(29,Z,null,S.mode);return F.lanes=z,F.return=S,F}finally{}}}var ya=pf(!0),yf=pf(!1),pt=Ie(null),Dt=null;function sl(e){var t=e.alternate;fe(He,He.current&1),fe(pt,e),Dt===null&&(t===null||da.current!==null||t.memoizedState!==null)&&(Dt=e)}function xf(e){if(e.tag===22){if(fe(He,He.current),fe(pt,e),Dt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Dt=e)}}else cl()}function cl(){fe(He,He.current),fe(pt,pt.current)}function Yt(e){Ne(pt),Dt===e&&(Dt=null),Ne(He)}var He=Ie(0);function Oi(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Dc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Gs(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:E({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var Vs={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=ct(),n=nl(a);n.payload=t,l!=null&&(n.callback=l),t=il(e,n,a),t!==null&&(rt(t,e,a),cn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=ct(),n=nl(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=il(e,n,a),t!==null&&(rt(t,e,a),cn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=ct(),a=nl(l);a.tag=2,t!=null&&(a.callback=t),t=il(e,a,l),t!==null&&(rt(t,e,l),cn(t,e,l))}};function vf(e,t,l,a,n,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,i,o):t.prototype&&t.prototype.isPureReactComponent?!Wa(l,a)||!Wa(n,i):!0}function bf(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&Vs.enqueueReplaceState(t,t.state,null)}function Gl(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=E({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var Ui=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Sf(e){Ui(e)}function Ef(e){console.error(e)}function _f(e){Ui(e)}function zi(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Nf(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Ys(e,t,l){return l=nl(l),l.tag=3,l.payload={element:null},l.callback=function(){zi(e,t)},l}function Tf(e){return e=nl(e),e.tag=3,e}function Af(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;e.payload=function(){return n(i)},e.callback=function(){Nf(t,l,a)}}var o=l.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(e.callback=function(){Nf(t,l,a),typeof n!="function"&&(ml===null?ml=new Set([this]):ml.add(this));var h=a.stack;this.componentDidCatch(a.value,{componentStack:h!==null?h:""})})}function b0(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&ln(t,l,n,!0),l=pt.current,l!==null){switch(l.tag){case 13:return Dt===null?dc():l.alternate===null&&we===0&&(we=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===ys?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),mc(e,a,n)),!1;case 22:return l.flags|=65536,a===ys?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),mc(e,a,n)),!1}throw Error(c(435,l.tag))}return mc(e,a,n),dc(),!1}if(se)return t=pt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==os&&(e=Error(c(422),{cause:a}),tn(dt(e,l)))):(a!==os&&(t=Error(c(423),{cause:a}),tn(dt(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=dt(a,l),n=Ys(e.stateNode,a,n),bs(e,n),we!==4&&(we=2)),!1;var i=Error(c(520),{cause:a});if(i=dt(i,l),_n===null?_n=[i]:_n.push(i),we!==4&&(we=2),t===null)return!0;a=dt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=Ys(l.stateNode,a,e),bs(l,e),!1;case 1:if(t=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(ml===null||!ml.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Tf(n),Af(n,e,l,a),bs(l,n),!1}l=l.return}while(l!==null);return!1}var jf=Error(c(461)),Ge=!1;function Xe(e,t,l,a){t.child=e===null?yf(t,null,l,a):ya(t,e.child,l,a)}function wf(e,t,l,a,n){l=l.render;var i=t.ref;if("ref"in a){var o={};for(var h in a)h!=="ref"&&(o[h]=a[h])}else o=a;return Hl(t),a=Ts(e,t,l,o,i,n),h=As(),e!==null&&!Ge?(js(e,t,n),Xt(e,t,n)):(se&&h&&cs(t),t.flags|=1,Xe(e,t,a,n),t.child)}function Df(e,t,l,a,n){if(e===null){var i=l.type;return typeof i=="function"&&!is(i)&&i.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=i,Rf(e,t,i,a,n)):(e=hi(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!Js(e,n)){var o=i.memoizedProps;if(l=l.compare,l=l!==null?l:Wa,l(o,a)&&e.ref===t.ref)return Xt(e,t,n)}return t.flags|=1,e=Lt(i,a),e.ref=t.ref,e.return=t,t.child=e}function Rf(e,t,l,a,n){if(e!==null){var i=e.memoizedProps;if(Wa(i,a)&&e.ref===t.ref)if(Ge=!1,t.pendingProps=a=i,Js(e,n))(e.flags&131072)!==0&&(Ge=!0);else return t.lanes=e.lanes,Xt(e,t,n)}return Xs(e,t,l,a,n)}function Mf(e,t,l){var a=t.pendingProps,n=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,e!==null){for(n=t.child=e.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;t.childLanes=i&~a}else t.childLanes=0,t.child=null;return Of(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&xi(t,i!==null?i.cachePool:null),i!==null?Do(t,i):Es(),xf(t);else return t.lanes=t.childLanes=536870912,Of(e,t,i!==null?i.baseLanes|l:l,l)}else i!==null?(xi(t,i.cachePool),Do(t,i),cl(),t.memoizedState=null):(e!==null&&xi(t,null),Es(),cl());return Xe(e,t,n,l),t.child}function Of(e,t,l,a){var n=ps();return n=n===null?null:{parent:Le._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&xi(t,null),Es(),xf(t),e!==null&&ln(e,t,a,!0),null}function Ci(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(c(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function Xs(e,t,l,a,n){return Hl(t),l=Ts(e,t,l,a,void 0,n),a=As(),e!==null&&!Ge?(js(e,t,n),Xt(e,t,n)):(se&&a&&cs(t),t.flags|=1,Xe(e,t,l,n),t.child)}function Uf(e,t,l,a,n,i){return Hl(t),t.updateQueue=null,l=Mo(t,a,l,n),Ro(e),a=As(),e!==null&&!Ge?(js(e,t,i),Xt(e,t,i)):(se&&a&&cs(t),t.flags|=1,Xe(e,t,l,i),t.child)}function zf(e,t,l,a,n){if(Hl(t),t.stateNode===null){var i=sa,o=l.contextType;typeof o=="object"&&o!==null&&(i=$e(o)),i=new l(a,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Vs,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=a,i.state=t.memoizedState,i.refs={},xs(t),o=l.contextType,i.context=typeof o=="object"&&o!==null?$e(o):sa,i.state=t.memoizedState,o=l.getDerivedStateFromProps,typeof o=="function"&&(Gs(t,l,o,a),i.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(o=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),o!==i.state&&Vs.enqueueReplaceState(i,i.state,null),on(t,a,i,n),rn(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){i=t.stateNode;var h=t.memoizedProps,g=Gl(l,h);i.props=g;var N=i.context,O=l.contextType;o=sa,typeof O=="object"&&O!==null&&(o=$e(O));var H=l.getDerivedStateFromProps;O=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,O||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||N!==o)&&bf(t,i,a,o),al=!1;var A=t.memoizedState;i.state=A,on(t,a,i,n),rn(),N=t.memoizedState,h||A!==N||al?(typeof H=="function"&&(Gs(t,l,H,a),N=t.memoizedState),(g=al||vf(t,l,g,a,A,N,o))?(O||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=N),i.props=a,i.state=N,i.context=o,a=g):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{i=t.stateNode,vs(e,t),o=t.memoizedProps,O=Gl(l,o),i.props=O,H=t.pendingProps,A=i.context,N=l.contextType,g=sa,typeof N=="object"&&N!==null&&(g=$e(N)),h=l.getDerivedStateFromProps,(N=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==H||A!==g)&&bf(t,i,a,g),al=!1,A=t.memoizedState,i.state=A,on(t,a,i,n),rn();var j=t.memoizedState;o!==H||A!==j||al||e!==null&&e.dependencies!==null&&pi(e.dependencies)?(typeof h=="function"&&(Gs(t,l,h,a),j=t.memoizedState),(O=al||vf(t,l,O,a,A,j,g)||e!==null&&e.dependencies!==null&&pi(e.dependencies))?(N||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,j,g),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,j,g)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=j),i.props=a,i.state=j,i.context=g,a=O):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),a=!1)}return i=a,Ci(e,t),a=(t.flags&128)!==0,i||a?(i=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&a?(t.child=ya(t,e.child,null,n),t.child=ya(t,null,l,n)):Xe(e,t,l,n),t.memoizedState=i.state,e=t.child):e=Xt(e,t,n),e}function Cf(e,t,l,a){return en(),t.flags|=256,Xe(e,t,l,a),t.child}var Zs={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Qs(e){return{baseLanes:e,cachePool:So()}}function Ks(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=yt),e}function Lf(e,t,l){var a=t.pendingProps,n=!1,i=(t.flags&128)!==0,o;if((o=i)||(o=e!==null&&e.memoizedState===null?!1:(He.current&2)!==0),o&&(n=!0,t.flags&=-129),o=(t.flags&32)!==0,t.flags&=-33,e===null){if(se){if(n?sl(t):cl(),se){var h=je,g;if(g=h){e:{for(g=h,h=wt;g.nodeType!==8;){if(!h){h=null;break e}if(g=Et(g.nextSibling),g===null){h=null;break e}}h=g}h!==null?(t.memoizedState={dehydrated:h,treeContext:Ol!==null?{id:Ht,overflow:qt}:null,retryLane:536870912,hydrationErrors:null},g=nt(18,null,null,0),g.stateNode=h,g.return=t,t.child=g,ke=t,je=null,g=!0):g=!1}g||Cl(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Dc(h)?t.lanes=32:t.lanes=536870912,null;Yt(t)}return h=a.children,a=a.fallback,n?(cl(),n=t.mode,h=Li({mode:"hidden",children:h},n),a=Ml(a,n,l,null),h.return=t,a.return=t,h.sibling=a,t.child=h,n=t.child,n.memoizedState=Qs(l),n.childLanes=Ks(e,o,l),t.memoizedState=Zs,a):(sl(t),$s(t,h))}if(g=e.memoizedState,g!==null&&(h=g.dehydrated,h!==null)){if(i)t.flags&256?(sl(t),t.flags&=-257,t=Is(e,t,l)):t.memoizedState!==null?(cl(),t.child=e.child,t.flags|=128,t=null):(cl(),n=a.fallback,h=t.mode,a=Li({mode:"visible",children:a.children},h),n=Ml(n,h,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,ya(t,e.child,null,l),a=t.child,a.memoizedState=Qs(l),a.childLanes=Ks(e,o,l),t.memoizedState=Zs,t=n);else if(sl(t),Dc(h)){if(o=h.nextSibling&&h.nextSibling.dataset,o)var N=o.dgst;o=N,a=Error(c(419)),a.stack="",a.digest=o,tn({value:a,source:null,stack:null}),t=Is(e,t,l)}else if(Ge||ln(e,t,l,!1),o=(l&e.childLanes)!==0,Ge||o){if(o=ye,o!==null&&(a=l&-l,a=(a&42)!==0?1:Ru(a),a=(a&(o.suspendedLanes|l))!==0?0:a,a!==0&&a!==g.retryLane))throw g.retryLane=a,ua(e,a),rt(o,e,a),jf;h.data==="$?"||dc(),t=Is(e,t,l)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=g.treeContext,je=Et(h.nextSibling),ke=t,se=!0,zl=null,wt=!1,e!==null&&(mt[gt++]=Ht,mt[gt++]=qt,mt[gt++]=Ol,Ht=e.id,qt=e.overflow,Ol=t),t=$s(t,a.children),t.flags|=4096);return t}return n?(cl(),n=a.fallback,h=t.mode,g=e.child,N=g.sibling,a=Lt(g,{mode:"hidden",children:a.children}),a.subtreeFlags=g.subtreeFlags&65011712,N!==null?n=Lt(N,n):(n=Ml(n,h,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,h=e.child.memoizedState,h===null?h=Qs(l):(g=h.cachePool,g!==null?(N=Le._currentValue,g=g.parent!==N?{parent:N,pool:N}:g):g=So(),h={baseLanes:h.baseLanes|l,cachePool:g}),n.memoizedState=h,n.childLanes=Ks(e,o,l),t.memoizedState=Zs,a):(sl(t),l=e.child,e=l.sibling,l=Lt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=l,t.memoizedState=null,l)}function $s(e,t){return t=Li({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Li(e,t){return e=nt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Is(e,t,l){return ya(t,e.child,null,l),e=$s(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hf(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),ds(e.return,t,l)}function ks(e,t,l,a,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function qf(e,t,l){var a=t.pendingProps,n=a.revealOrder,i=a.tail;if(Xe(e,t,a.children,l),a=He.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Hf(e,l,t);else if(e.tag===19)Hf(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(fe(He,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&Oi(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),ks(t,!1,n,l,i);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Oi(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}ks(t,!0,l,null,i);break;case"together":ks(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xt(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),hl|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(ln(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,l=Lt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Lt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Js(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&pi(e)))}function S0(e,t,l){switch(t.tag){case 3:$n(t,t.stateNode.containerInfo),ll(t,Le,e.memoizedState.cache),en();break;case 27:case 5:Tu(t);break;case 4:$n(t,t.stateNode.containerInfo);break;case 10:ll(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(sl(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Lf(e,t,l):(sl(t),e=Xt(e,t,l),e!==null?e.sibling:null);sl(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(ln(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return qf(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),fe(He,He.current),a)break;return null;case 22:case 23:return t.lanes=0,Mf(e,t,l);case 24:ll(t,Le,e.memoizedState.cache)}return Xt(e,t,l)}function Bf(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ge=!0;else{if(!Js(e,l)&&(t.flags&128)===0)return Ge=!1,S0(e,t,l);Ge=(e.flags&131072)!==0}else Ge=!1,se&&(t.flags&1048576)!==0&&mo(t,gi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")is(a)?(e=Gl(a,e),t.tag=1,t=zf(null,t,a,e,l)):(t.tag=0,t=Xs(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===X){t.tag=11,t=wf(null,t,a,e,l);break e}else if(n===ue){t.tag=14,t=Df(null,t,a,e,l);break e}}throw t=qa(a)||a,Error(c(306,t,""))}}return t;case 0:return Xs(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Gl(a,t.pendingProps),zf(e,t,a,n,l);case 3:e:{if($n(t,t.stateNode.containerInfo),e===null)throw Error(c(387));a=t.pendingProps;var i=t.memoizedState;n=i.element,vs(e,t),on(t,a,null,l);var o=t.memoizedState;if(a=o.cache,ll(t,Le,a),a!==i.cache&&hs(t,[Le],l,!0),rn(),a=o.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=Cf(e,t,a,l);break e}else if(a!==n){n=dt(Error(c(424)),t),tn(n),t=Cf(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(je=Et(e.firstChild),ke=t,se=!0,zl=null,wt=!0,l=yf(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(en(),a===n){t=Xt(e,t,l);break e}Xe(e,t,a,l)}t=t.child}return t;case 26:return Ci(e,t),e===null?(l=Xd(t.type,null,t.pendingProps,null))?t.memoizedState=l:se||(l=t.type,e=t.pendingProps,a=Ji(Pt.current).createElement(l),a[Ke]=t,a[Je]=e,Qe(a,l,e),Be(a),t.stateNode=a):t.memoizedState=Xd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Tu(t),e===null&&se&&(a=t.stateNode=Gd(t.type,t.pendingProps,Pt.current),ke=t,wt=!0,n=je,yl(t.type)?(Rc=n,je=Et(a.firstChild)):je=n),Xe(e,t,t.pendingProps.children,l),Ci(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&se&&((n=a=je)&&(a=k0(a,t.type,t.pendingProps,wt),a!==null?(t.stateNode=a,ke=t,je=Et(a.firstChild),wt=!1,n=!0):n=!1),n||Cl(t)),Tu(t),n=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,a=i.children,Ac(n,i)?a=null:o!==null&&Ac(n,o)&&(t.flags|=32),t.memoizedState!==null&&(n=Ts(e,t,h0,null,null,l),On._currentValue=n),Ci(e,t),Xe(e,t,a,l),t.child;case 6:return e===null&&se&&((e=l=je)&&(l=J0(l,t.pendingProps,wt),l!==null?(t.stateNode=l,ke=t,je=null,e=!0):e=!1),e||Cl(t)),null;case 13:return Lf(e,t,l);case 4:return $n(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=ya(t,null,a,l):Xe(e,t,a,l),t.child;case 11:return wf(e,t,t.type,t.pendingProps,l);case 7:return Xe(e,t,t.pendingProps,l),t.child;case 8:return Xe(e,t,t.pendingProps.children,l),t.child;case 12:return Xe(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,ll(t,t.type,a.value),Xe(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Hl(t),n=$e(n),a=a(n),t.flags|=1,Xe(e,t,a,l),t.child;case 14:return Df(e,t,t.type,t.pendingProps,l);case 15:return Rf(e,t,t.type,t.pendingProps,l);case 19:return qf(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Li(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Lt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Mf(e,t,l);case 24:return Hl(t),a=$e(Le),e===null?(n=ps(),n===null&&(n=ye,i=ms(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),t.memoizedState={parent:a,cache:n},xs(t),ll(t,Le,n)):((e.lanes&l)!==0&&(vs(e,t),on(t,null,null,l),rn()),n=e.memoizedState,i=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ll(t,Le,a)):(a=i.cache,ll(t,Le,a),a!==n.cache&&hs(t,[Le],l,!0))),Xe(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function Zt(e){e.flags|=4}function Gf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Id(t)){if(t=pt.current,t!==null&&((le&4194048)===le?Dt!==null:(le&62914560)!==le&&(le&536870912)===0||t!==Dt))throw sn=ys,Eo;e.flags|=8192}}function Hi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?pr():536870912,e.lanes|=t,Sa|=t)}function yn(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Te(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function E0(e,t,l){var a=t.pendingProps;switch(rs(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Te(t),null;case 1:return Te(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Gt(Le),Ql(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Fa(t)?Zt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,yo())),Te(t),null;case 26:return l=t.memoizedState,e===null?(Zt(t),l!==null?(Te(t),Gf(t,l)):(Te(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Zt(t),Te(t),Gf(t,l)):(Te(t),t.flags&=-16777217):(e.memoizedProps!==a&&Zt(t),Te(t),t.flags&=-16777217),null;case 27:In(t),l=Pt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Zt(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Te(t),null}e=qe.current,Fa(t)?go(t):(e=Gd(n,a,l),t.stateNode=e,Zt(t))}return Te(t),null;case 5:if(In(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Zt(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Te(t),null}if(e=qe.current,Fa(t))go(t);else{switch(n=Ji(Pt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Ke]=t,e[Je]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Qe(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zt(t)}}return Te(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Zt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(c(166));if(e=Pt.current,Fa(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=ke,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Ke]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Ud(e.nodeValue,l)),e||Cl(t)}else e=Ji(e).createTextNode(a),e[Ke]=t,t.stateNode=e}return Te(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Fa(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(c(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(c(317));n[Ke]=t}else en(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Te(t),n=!1}else n=yo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Yt(t),t):(Yt(t),null)}if(Yt(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Hi(t,t.updateQueue),Te(t),null;case 4:return Ql(),e===null&&Sc(t.stateNode.containerInfo),Te(t),null;case 10:return Gt(t.type),Te(t),null;case 19:if(Ne(He),n=t.memoizedState,n===null)return Te(t),null;if(a=(t.flags&128)!==0,i=n.rendering,i===null)if(a)yn(n,!1);else{if(we!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=Oi(e),i!==null){for(t.flags|=128,yn(n,!1),e=i.updateQueue,t.updateQueue=e,Hi(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)ho(l,e),l=l.sibling;return fe(He,He.current&1|2),t.child}e=e.sibling}n.tail!==null&&jt()>Gi&&(t.flags|=128,a=!0,yn(n,!1),t.lanes=4194304)}else{if(!a)if(e=Oi(i),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Hi(t,e),yn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!se)return Te(t),null}else 2*jt()-n.renderingStartTime>Gi&&l!==536870912&&(t.flags|=128,a=!0,yn(n,!1),t.lanes=4194304);n.isBackwards?(i.sibling=t.child,t.child=i):(e=n.last,e!==null?e.sibling=i:t.child=i,n.last=i)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=jt(),t.sibling=null,e=He.current,fe(He,a?e&1|2:e&1),t):(Te(t),null);case 22:case 23:return Yt(t),_s(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(Te(t),t.subtreeFlags&6&&(t.flags|=8192)):Te(t),l=t.updateQueue,l!==null&&Hi(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&Ne(ql),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Gt(Le),Te(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function _0(e,t){switch(rs(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gt(Le),Ql(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return In(t),null;case 13:if(Yt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));en()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ne(He),null;case 4:return Ql(),null;case 10:return Gt(t.type),null;case 22:case 23:return Yt(t),_s(),e!==null&&Ne(ql),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Gt(Le),null;case 25:return null;default:return null}}function Vf(e,t){switch(rs(t),t.tag){case 3:Gt(Le),Ql();break;case 26:case 27:case 5:In(t);break;case 4:Ql();break;case 13:Yt(t);break;case 19:Ne(He);break;case 10:Gt(t.type);break;case 22:case 23:Yt(t),_s(),e!==null&&Ne(ql);break;case 24:Gt(Le)}}function xn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var i=l.create,o=l.inst;a=i(),o.destroy=a}l=l.next}while(l!==n)}}catch(h){ge(t,t.return,h)}}function rl(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&e)===e){var o=a.inst,h=o.destroy;if(h!==void 0){o.destroy=void 0,n=t;var g=l,N=h;try{N()}catch(O){ge(n,g,O)}}}a=a.next}while(a!==i)}}catch(O){ge(t,t.return,O)}}function Yf(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{wo(t,l)}catch(a){ge(e,e.return,a)}}}function Xf(e,t,l){l.props=Gl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){ge(e,t,a)}}function vn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){ge(e,t,n)}}function Rt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){ge(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){ge(e,t,n)}else l.current=null}function Zf(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){ge(e,e.return,n)}}function Ws(e,t,l){try{var a=e.stateNode;Z0(a,e.type,l,t),a[Je]=t}catch(n){ge(e,e.return,n)}}function Qf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&yl(e.type)||e.tag===4}function Ps(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&yl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Fs(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=ki));else if(a!==4&&(a===27&&yl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Fs(e,t,l),e=e.sibling;e!==null;)Fs(e,t,l),e=e.sibling}function qi(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&yl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(qi(e,t,l),e=e.sibling;e!==null;)qi(e,t,l),e=e.sibling}function Kf(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Qe(t,a,l),t[Ke]=e,t[Je]=l}catch(i){ge(e,e.return,i)}}var Qt=!1,Me=!1,ec=!1,$f=typeof WeakSet=="function"?WeakSet:Set,Ve=null;function N0(e,t){if(e=e.containerInfo,Nc=lu,e=lo(e),Pu(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break e}var o=0,h=-1,g=-1,N=0,O=0,H=e,A=null;t:for(;;){for(var j;H!==l||n!==0&&H.nodeType!==3||(h=o+n),H!==i||a!==0&&H.nodeType!==3||(g=o+a),H.nodeType===3&&(o+=H.nodeValue.length),(j=H.firstChild)!==null;)A=H,H=j;for(;;){if(H===e)break t;if(A===l&&++N===n&&(h=o),A===i&&++O===a&&(g=o),(j=H.nextSibling)!==null)break;H=A,A=H.parentNode}H=j}l=h===-1||g===-1?null:{start:h,end:g}}else l=null}l=l||{start:0,end:0}}else l=null;for(Tc={focusedElem:e,selectionRange:l},lu=!1,Ve=t;Ve!==null;)if(t=Ve,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ve=e;else for(;Ve!==null;){switch(t=Ve,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,l=t,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var k=Gl(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(k,i),a.__reactInternalSnapshotBeforeUpdate=e}catch(K){ge(l,l.return,K)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)wc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":wc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Ve=e;break}Ve=t.return}}function If(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:ol(e,l),a&4&&xn(5,l);break;case 1:if(ol(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(o){ge(l,l.return,o)}else{var n=Gl(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){ge(l,l.return,o)}}a&64&&Yf(l),a&512&&vn(l,l.return);break;case 3:if(ol(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{wo(e,t)}catch(o){ge(l,l.return,o)}}break;case 27:t===null&&a&4&&Kf(l);case 26:case 5:ol(e,l),t===null&&a&4&&Zf(l),a&512&&vn(l,l.return);break;case 12:ol(e,l);break;case 13:ol(e,l),a&4&&Wf(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=U0.bind(null,l),W0(e,l))));break;case 22:if(a=l.memoizedState!==null||Qt,!a){t=t!==null&&t.memoizedState!==null||Me,n=Qt;var i=Me;Qt=a,(Me=t)&&!i?fl(e,l,(l.subtreeFlags&8772)!==0):ol(e,l),Qt=n,Me=i}break;case 30:break;default:ol(e,l)}}function kf(e){var t=e.alternate;t!==null&&(e.alternate=null,kf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Uu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Se=null,Fe=!1;function Kt(e,t,l){for(l=l.child;l!==null;)Jf(e,t,l),l=l.sibling}function Jf(e,t,l){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(Ba,l)}catch{}switch(l.tag){case 26:Me||Rt(l,t),Kt(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Me||Rt(l,t);var a=Se,n=Fe;yl(l.type)&&(Se=l.stateNode,Fe=!1),Kt(e,t,l),wn(l.stateNode),Se=a,Fe=n;break;case 5:Me||Rt(l,t);case 6:if(a=Se,n=Fe,Se=null,Kt(e,t,l),Se=a,Fe=n,Se!==null)if(Fe)try{(Se.nodeType===9?Se.body:Se.nodeName==="HTML"?Se.ownerDocument.body:Se).removeChild(l.stateNode)}catch(i){ge(l,t,i)}else try{Se.removeChild(l.stateNode)}catch(i){ge(l,t,i)}break;case 18:Se!==null&&(Fe?(e=Se,qd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Ln(e)):qd(Se,l.stateNode));break;case 4:a=Se,n=Fe,Se=l.stateNode.containerInfo,Fe=!0,Kt(e,t,l),Se=a,Fe=n;break;case 0:case 11:case 14:case 15:Me||rl(2,l,t),Me||rl(4,l,t),Kt(e,t,l);break;case 1:Me||(Rt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Xf(l,t,a)),Kt(e,t,l);break;case 21:Kt(e,t,l);break;case 22:Me=(a=Me)||l.memoizedState!==null,Kt(e,t,l),Me=a;break;default:Kt(e,t,l)}}function Wf(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ln(e)}catch(l){ge(t,t.return,l)}}function T0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new $f),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new $f),t;default:throw Error(c(435,e.tag))}}function tc(e,t){var l=T0(e);t.forEach(function(a){var n=z0.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function it(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=e,o=t,h=o;e:for(;h!==null;){switch(h.tag){case 27:if(yl(h.type)){Se=h.stateNode,Fe=!1;break e}break;case 5:Se=h.stateNode,Fe=!1;break e;case 3:case 4:Se=h.stateNode.containerInfo,Fe=!0;break e}h=h.return}if(Se===null)throw Error(c(160));Jf(i,o,n),Se=null,Fe=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Pf(t,e),t=t.sibling}var St=null;function Pf(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:it(t,e),ut(e),a&4&&(rl(3,e,e.return),xn(3,e),rl(5,e,e.return));break;case 1:it(t,e),ut(e),a&512&&(Me||l===null||Rt(l,l.return)),a&64&&Qt&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=St;if(it(t,e),ut(e),a&512&&(Me||l===null||Rt(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[Ya]||i[Ke]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),Qe(i,a,l),i[Ke]=e,Be(i),a=i;break e;case"link":var o=Kd("link","href",n).get(a+(l.href||""));if(o){for(var h=0;h<o.length;h++)if(i=o[h],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){o.splice(h,1);break t}}i=n.createElement(a),Qe(i,a,l),n.head.appendChild(i);break;case"meta":if(o=Kd("meta","content",n).get(a+(l.content||""))){for(h=0;h<o.length;h++)if(i=o[h],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){o.splice(h,1);break t}}i=n.createElement(a),Qe(i,a,l),n.head.appendChild(i);break;default:throw Error(c(468,a))}i[Ke]=e,Be(i),a=i}e.stateNode=a}else $d(n,e.type,e.stateNode);else e.stateNode=Qd(n,a,e.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?$d(n,e.type,e.stateNode):Qd(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Ws(e,e.memoizedProps,l.memoizedProps)}break;case 27:it(t,e),ut(e),a&512&&(Me||l===null||Rt(l,l.return)),l!==null&&a&4&&Ws(e,e.memoizedProps,l.memoizedProps);break;case 5:if(it(t,e),ut(e),a&512&&(Me||l===null||Rt(l,l.return)),e.flags&32){n=e.stateNode;try{Fl(n,"")}catch(j){ge(e,e.return,j)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,Ws(e,n,l!==null?l.memoizedProps:n)),a&1024&&(ec=!0);break;case 6:if(it(t,e),ut(e),a&4){if(e.stateNode===null)throw Error(c(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(j){ge(e,e.return,j)}}break;case 3:if(Fi=null,n=St,St=Wi(t.containerInfo),it(t,e),St=n,ut(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Ln(t.containerInfo)}catch(j){ge(e,e.return,j)}ec&&(ec=!1,Ff(e));break;case 4:a=St,St=Wi(e.stateNode.containerInfo),it(t,e),ut(e),St=a;break;case 12:it(t,e),ut(e);break;case 13:it(t,e),ut(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(sc=jt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,tc(e,a)));break;case 22:n=e.memoizedState!==null;var g=l!==null&&l.memoizedState!==null,N=Qt,O=Me;if(Qt=N||n,Me=O||g,it(t,e),Me=O,Qt=N,ut(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||g||Qt||Me||Vl(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){g=l=t;try{if(i=g.stateNode,n)o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{h=g.stateNode;var H=g.memoizedProps.style,A=H!=null&&H.hasOwnProperty("display")?H.display:null;h.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(j){ge(g,g.return,j)}}}else if(t.tag===6){if(l===null){g=t;try{g.stateNode.nodeValue=n?"":g.memoizedProps}catch(j){ge(g,g.return,j)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,tc(e,l))));break;case 19:it(t,e),ut(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,tc(e,a)));break;case 30:break;case 21:break;default:it(t,e),ut(e)}}function ut(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(Qf(a)){l=a;break}a=a.return}if(l==null)throw Error(c(160));switch(l.tag){case 27:var n=l.stateNode,i=Ps(e);qi(e,i,n);break;case 5:var o=l.stateNode;l.flags&32&&(Fl(o,""),l.flags&=-33);var h=Ps(e);qi(e,h,o);break;case 3:case 4:var g=l.stateNode.containerInfo,N=Ps(e);Fs(e,N,g);break;default:throw Error(c(161))}}catch(O){ge(e,e.return,O)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ff(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Ff(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ol(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)If(e,t.alternate,t),t=t.sibling}function Vl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:rl(4,t,t.return),Vl(t);break;case 1:Rt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Xf(t,t.return,l),Vl(t);break;case 27:wn(t.stateNode);case 26:case 5:Rt(t,t.return),Vl(t);break;case 22:t.memoizedState===null&&Vl(t);break;case 30:Vl(t);break;default:Vl(t)}e=e.sibling}}function fl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,i=t,o=i.flags;switch(i.tag){case 0:case 11:case 15:fl(n,i,l),xn(4,i);break;case 1:if(fl(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(N){ge(a,a.return,N)}if(a=i,n=a.updateQueue,n!==null){var h=a.stateNode;try{var g=n.shared.hiddenCallbacks;if(g!==null)for(n.shared.hiddenCallbacks=null,n=0;n<g.length;n++)jo(g[n],h)}catch(N){ge(a,a.return,N)}}l&&o&64&&Yf(i),vn(i,i.return);break;case 27:Kf(i);case 26:case 5:fl(n,i,l),l&&a===null&&o&4&&Zf(i),vn(i,i.return);break;case 12:fl(n,i,l);break;case 13:fl(n,i,l),l&&o&4&&Wf(n,i);break;case 22:i.memoizedState===null&&fl(n,i,l),vn(i,i.return);break;case 30:break;default:fl(n,i,l)}t=t.sibling}}function lc(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&an(l))}function ac(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&an(e))}function Mt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ed(e,t,l,a),t=t.sibling}function ed(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Mt(e,t,l,a),n&2048&&xn(9,t);break;case 1:Mt(e,t,l,a);break;case 3:Mt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&an(e)));break;case 12:if(n&2048){Mt(e,t,l,a),e=t.stateNode;try{var i=t.memoizedProps,o=i.id,h=i.onPostCommit;typeof h=="function"&&h(o,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(g){ge(t,t.return,g)}}else Mt(e,t,l,a);break;case 13:Mt(e,t,l,a);break;case 23:break;case 22:i=t.stateNode,o=t.alternate,t.memoizedState!==null?i._visibility&2?Mt(e,t,l,a):bn(e,t):i._visibility&2?Mt(e,t,l,a):(i._visibility|=2,xa(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&lc(o,t);break;case 24:Mt(e,t,l,a),n&2048&&ac(t.alternate,t);break;default:Mt(e,t,l,a)}}function xa(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,o=t,h=l,g=a,N=o.flags;switch(o.tag){case 0:case 11:case 15:xa(i,o,h,g,n),xn(8,o);break;case 23:break;case 22:var O=o.stateNode;o.memoizedState!==null?O._visibility&2?xa(i,o,h,g,n):bn(i,o):(O._visibility|=2,xa(i,o,h,g,n)),n&&N&2048&&lc(o.alternate,o);break;case 24:xa(i,o,h,g,n),n&&N&2048&&ac(o.alternate,o);break;default:xa(i,o,h,g,n)}t=t.sibling}}function bn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:bn(l,a),n&2048&&lc(a.alternate,a);break;case 24:bn(l,a),n&2048&&ac(a.alternate,a);break;default:bn(l,a)}t=t.sibling}}var Sn=8192;function va(e){if(e.subtreeFlags&Sn)for(e=e.child;e!==null;)td(e),e=e.sibling}function td(e){switch(e.tag){case 26:va(e),e.flags&Sn&&e.memoizedState!==null&&og(St,e.memoizedState,e.memoizedProps);break;case 5:va(e);break;case 3:case 4:var t=St;St=Wi(e.stateNode.containerInfo),va(e),St=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Sn,Sn=16777216,va(e),Sn=t):va(e));break;default:va(e)}}function ld(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function En(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Ve=a,nd(a,e)}ld(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ad(e),e=e.sibling}function ad(e){switch(e.tag){case 0:case 11:case 15:En(e),e.flags&2048&&rl(9,e,e.return);break;case 3:En(e);break;case 12:En(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Bi(e)):En(e);break;default:En(e)}}function Bi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Ve=a,nd(a,e)}ld(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:rl(8,t,t.return),Bi(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,Bi(t));break;default:Bi(t)}e=e.sibling}}function nd(e,t){for(;Ve!==null;){var l=Ve;switch(l.tag){case 0:case 11:case 15:rl(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:an(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Ve=a;else e:for(l=e;Ve!==null;){a=Ve;var n=a.sibling,i=a.return;if(kf(a),a===l){Ve=null;break e}if(n!==null){n.return=i,Ve=n;break e}Ve=i}}}var A0={getCacheForType:function(e){var t=$e(Le),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},j0=typeof WeakMap=="function"?WeakMap:Map,re=0,ye=null,ee=null,le=0,oe=0,st=null,dl=!1,ba=!1,nc=!1,$t=0,we=0,hl=0,Yl=0,ic=0,yt=0,Sa=0,_n=null,et=null,uc=!1,sc=0,Gi=1/0,Vi=null,ml=null,Ze=0,gl=null,Ea=null,_a=0,cc=0,rc=null,id=null,Nn=0,oc=null;function ct(){if((re&2)!==0&&le!==0)return le&-le;if(C.T!==null){var e=oa;return e!==0?e:yc()}return vr()}function ud(){yt===0&&(yt=(le&536870912)===0||se?gr():536870912);var e=pt.current;return e!==null&&(e.flags|=32),yt}function rt(e,t,l){(e===ye&&(oe===2||oe===9)||e.cancelPendingCommit!==null)&&(Na(e,0),pl(e,le,yt,!1)),Va(e,l),((re&2)===0||e!==ye)&&(e===ye&&((re&2)===0&&(Yl|=l),we===4&&pl(e,le,yt,!1)),Ot(e))}function sd(e,t,l){if((re&6)!==0)throw Error(c(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||Ga(e,t),n=a?R0(e,t):hc(e,t,!0),i=a;do{if(n===0){ba&&!a&&pl(e,t,0,!1);break}else{if(l=e.current.alternate,i&&!w0(l)){n=hc(e,t,!1),i=!1;continue}if(n===2){if(i=t,e.errorRecoveryDisabledLanes&i)var o=0;else o=e.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){t=o;e:{var h=e;n=_n;var g=h.current.memoizedState.isDehydrated;if(g&&(Na(h,o).flags|=256),o=hc(h,o,!1),o!==2){if(nc&&!g){h.errorRecoveryDisabledLanes|=i,Yl|=i,n=4;break e}i=et,et=n,i!==null&&(et===null?et=i:et.push.apply(et,i))}n=o}if(i=!1,n!==2)continue}}if(n===1){Na(e,0),pl(e,t,0,!0);break}e:{switch(a=e,i=n,i){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:pl(a,t,yt,!dl);break e;case 2:et=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(n=sc+300-jt(),10<n)){if(pl(a,t,yt,!dl),Pn(a,0,!0)!==0)break e;a.timeoutHandle=Ld(cd.bind(null,a,l,et,Vi,uc,t,yt,Yl,Sa,dl,i,2,-0,0),n);break e}cd(a,l,et,Vi,uc,t,yt,Yl,Sa,dl,i,0,-0,0)}}break}while(!0);Ot(e)}function cd(e,t,l,a,n,i,o,h,g,N,O,H,A,j){if(e.timeoutHandle=-1,H=t.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Mn={stylesheets:null,count:0,unsuspend:rg},td(t),H=fg(),H!==null)){e.cancelPendingCommit=H(gd.bind(null,e,t,i,l,a,n,o,h,g,O,1,A,j)),pl(e,i,o,!N);return}gd(e,t,i,l,a,n,o,h,g)}function w0(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!at(i(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pl(e,t,l,a){t&=~ic,t&=~Yl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var i=31-lt(n),o=1<<i;a[i]=-1,n&=~o}l!==0&&yr(e,l,t)}function Yi(){return(re&6)===0?(Tn(0),!1):!0}function fc(){if(ee!==null){if(oe===0)var e=ee.return;else e=ee,Bt=Ll=null,ws(e),pa=null,gn=0,e=ee;for(;e!==null;)Vf(e.alternate,e),e=e.return;ee=null}}function Na(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,K0(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),fc(),ye=e,ee=l=Lt(e.current,null),le=t,oe=0,st=null,dl=!1,ba=Ga(e,t),nc=!1,Sa=yt=ic=Yl=hl=we=0,et=_n=null,uc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-lt(a),i=1<<n;t|=e[n],a&=~i}return $t=t,oi(),l}function rd(e,t){P=null,C.H=Di,t===un||t===vi?(t=To(),oe=3):t===Eo?(t=To(),oe=4):oe=t===jf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,st=t,ee===null&&(we=1,zi(e,dt(t,e.current)))}function od(){var e=C.H;return C.H=Di,e===null?Di:e}function fd(){var e=C.A;return C.A=A0,e}function dc(){we=4,dl||(le&4194048)!==le&&pt.current!==null||(ba=!0),(hl&134217727)===0&&(Yl&134217727)===0||ye===null||pl(ye,le,yt,!1)}function hc(e,t,l){var a=re;re|=2;var n=od(),i=fd();(ye!==e||le!==t)&&(Vi=null,Na(e,t)),t=!1;var o=we;e:do try{if(oe!==0&&ee!==null){var h=ee,g=st;switch(oe){case 8:fc(),o=6;break e;case 3:case 2:case 9:case 6:pt.current===null&&(t=!0);var N=oe;if(oe=0,st=null,Ta(e,h,g,N),l&&ba){o=0;break e}break;default:N=oe,oe=0,st=null,Ta(e,h,g,N)}}D0(),o=we;break}catch(O){rd(e,O)}while(!0);return t&&e.shellSuspendCounter++,Bt=Ll=null,re=a,C.H=n,C.A=i,ee===null&&(ye=null,le=0,oi()),o}function D0(){for(;ee!==null;)dd(ee)}function R0(e,t){var l=re;re|=2;var a=od(),n=fd();ye!==e||le!==t?(Vi=null,Gi=jt()+500,Na(e,t)):ba=Ga(e,t);e:do try{if(oe!==0&&ee!==null){t=ee;var i=st;t:switch(oe){case 1:oe=0,st=null,Ta(e,t,i,1);break;case 2:case 9:if(_o(i)){oe=0,st=null,hd(t);break}t=function(){oe!==2&&oe!==9||ye!==e||(oe=7),Ot(e)},i.then(t,t);break e;case 3:oe=7;break e;case 4:oe=5;break e;case 7:_o(i)?(oe=0,st=null,hd(t)):(oe=0,st=null,Ta(e,t,i,7));break;case 5:var o=null;switch(ee.tag){case 26:o=ee.memoizedState;case 5:case 27:var h=ee;if(!o||Id(o)){oe=0,st=null;var g=h.sibling;if(g!==null)ee=g;else{var N=h.return;N!==null?(ee=N,Xi(N)):ee=null}break t}}oe=0,st=null,Ta(e,t,i,5);break;case 6:oe=0,st=null,Ta(e,t,i,6);break;case 8:fc(),we=6;break e;default:throw Error(c(462))}}M0();break}catch(O){rd(e,O)}while(!0);return Bt=Ll=null,C.H=a,C.A=n,re=l,ee!==null?0:(ye=null,le=0,oi(),we)}function M0(){for(;ee!==null&&!Fh();)dd(ee)}function dd(e){var t=Bf(e.alternate,e,$t);e.memoizedProps=e.pendingProps,t===null?Xi(e):ee=t}function hd(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Uf(l,t,t.pendingProps,t.type,void 0,le);break;case 11:t=Uf(l,t,t.pendingProps,t.type.render,t.ref,le);break;case 5:ws(t);default:Vf(l,t),t=ee=ho(t,$t),t=Bf(l,t,$t)}e.memoizedProps=e.pendingProps,t===null?Xi(e):ee=t}function Ta(e,t,l,a){Bt=Ll=null,ws(t),pa=null,gn=0;var n=t.return;try{if(b0(e,n,t,l,le)){we=1,zi(e,dt(l,e.current)),ee=null;return}}catch(i){if(n!==null)throw ee=n,i;we=1,zi(e,dt(l,e.current)),ee=null;return}t.flags&32768?(se||a===1?e=!0:ba||(le&536870912)!==0?e=!1:(dl=e=!0,(a===2||a===9||a===3||a===6)&&(a=pt.current,a!==null&&a.tag===13&&(a.flags|=16384))),md(t,e)):Xi(t)}function Xi(e){var t=e;do{if((t.flags&32768)!==0){md(t,dl);return}e=t.return;var l=E0(t.alternate,t,$t);if(l!==null){ee=l;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);we===0&&(we=5)}function md(e,t){do{var l=_0(e.alternate,e);if(l!==null){l.flags&=32767,ee=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){ee=e;return}ee=e=l}while(e!==null);we=6,ee=null}function gd(e,t,l,a,n,i,o,h,g){e.cancelPendingCommit=null;do Zi();while(Ze!==0);if((re&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(i=t.lanes|t.childLanes,i|=as,rm(e,l,i,o,h,g),e===ye&&(ee=ye=null,le=0),Ea=t,gl=e,_a=l,cc=i,rc=n,id=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,C0(kn,function(){return bd(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=C.T,C.T=null,n=G.p,G.p=2,o=re,re|=4;try{N0(e,t,l)}finally{re=o,G.p=n,C.T=a}}Ze=1,pd(),yd(),xd()}}function pd(){if(Ze===1){Ze=0;var e=gl,t=Ea,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=C.T,C.T=null;var a=G.p;G.p=2;var n=re;re|=4;try{Pf(t,e);var i=Tc,o=lo(e.containerInfo),h=i.focusedElem,g=i.selectionRange;if(o!==h&&h&&h.ownerDocument&&to(h.ownerDocument.documentElement,h)){if(g!==null&&Pu(h)){var N=g.start,O=g.end;if(O===void 0&&(O=N),"selectionStart"in h)h.selectionStart=N,h.selectionEnd=Math.min(O,h.value.length);else{var H=h.ownerDocument||document,A=H&&H.defaultView||window;if(A.getSelection){var j=A.getSelection(),k=h.textContent.length,K=Math.min(g.start,k),me=g.end===void 0?K:Math.min(g.end,k);!j.extend&&K>me&&(o=me,me=K,K=o);var S=eo(h,K),b=eo(h,me);if(S&&b&&(j.rangeCount!==1||j.anchorNode!==S.node||j.anchorOffset!==S.offset||j.focusNode!==b.node||j.focusOffset!==b.offset)){var _=H.createRange();_.setStart(S.node,S.offset),j.removeAllRanges(),K>me?(j.addRange(_),j.extend(b.node,b.offset)):(_.setEnd(b.node,b.offset),j.addRange(_))}}}}for(H=[],j=h;j=j.parentNode;)j.nodeType===1&&H.push({element:j,left:j.scrollLeft,top:j.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<H.length;h++){var z=H[h];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}lu=!!Nc,Tc=Nc=null}finally{re=n,G.p=a,C.T=l}}e.current=t,Ze=2}}function yd(){if(Ze===2){Ze=0;var e=gl,t=Ea,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=C.T,C.T=null;var a=G.p;G.p=2;var n=re;re|=4;try{If(e,t.alternate,t)}finally{re=n,G.p=a,C.T=l}}Ze=3}}function xd(){if(Ze===4||Ze===3){Ze=0,em();var e=gl,t=Ea,l=_a,a=id;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ze=5:(Ze=0,Ea=gl=null,vd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(ml=null),Mu(l),t=t.stateNode,tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(Ba,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=C.T,n=G.p,G.p=2,C.T=null;try{for(var i=e.onRecoverableError,o=0;o<a.length;o++){var h=a[o];i(h.value,{componentStack:h.stack})}}finally{C.T=t,G.p=n}}(_a&3)!==0&&Zi(),Ot(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===oc?Nn++:(Nn=0,oc=e):Nn=0,Tn(0)}}function vd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,an(t)))}function Zi(e){return pd(),yd(),xd(),bd()}function bd(){if(Ze!==5)return!1;var e=gl,t=cc;cc=0;var l=Mu(_a),a=C.T,n=G.p;try{G.p=32>l?32:l,C.T=null,l=rc,rc=null;var i=gl,o=_a;if(Ze=0,Ea=gl=null,_a=0,(re&6)!==0)throw Error(c(331));var h=re;if(re|=4,ad(i.current),ed(i,i.current,o,l),re=h,Tn(0,!1),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(Ba,i)}catch{}return!0}finally{G.p=n,C.T=a,vd(e,t)}}function Sd(e,t,l){t=dt(l,t),t=Ys(e.stateNode,t,2),e=il(e,t,2),e!==null&&(Va(e,2),Ot(e))}function ge(e,t,l){if(e.tag===3)Sd(e,e,l);else for(;t!==null;){if(t.tag===3){Sd(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ml===null||!ml.has(a))){e=dt(l,e),l=Tf(2),a=il(t,l,2),a!==null&&(Af(l,a,t,e),Va(a,2),Ot(a));break}}t=t.return}}function mc(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new j0;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(nc=!0,n.add(l),e=O0.bind(null,e,t,l),t.then(e,e))}function O0(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,ye===e&&(le&l)===l&&(we===4||we===3&&(le&62914560)===le&&300>jt()-sc?(re&2)===0&&Na(e,0):ic|=l,Sa===le&&(Sa=0)),Ot(e)}function Ed(e,t){t===0&&(t=pr()),e=ua(e,t),e!==null&&(Va(e,t),Ot(e))}function U0(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Ed(e,l)}function z0(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(c(314))}a!==null&&a.delete(t),Ed(e,l)}function C0(e,t){return ju(e,t)}var Qi=null,Aa=null,gc=!1,Ki=!1,pc=!1,Xl=0;function Ot(e){e!==Aa&&e.next===null&&(Aa===null?Qi=Aa=e:Aa=Aa.next=e),Ki=!0,gc||(gc=!0,H0())}function Tn(e,t){if(!pc&&Ki){pc=!0;do for(var l=!1,a=Qi;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var o=a.suspendedLanes,h=a.pingedLanes;i=(1<<31-lt(42|e)+1)-1,i&=n&~(o&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,Ad(a,i))}else i=le,i=Pn(a,a===ye?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||Ga(a,i)||(l=!0,Ad(a,i));a=a.next}while(l);pc=!1}}function L0(){_d()}function _d(){Ki=gc=!1;var e=0;Xl!==0&&(Q0()&&(e=Xl),Xl=0);for(var t=jt(),l=null,a=Qi;a!==null;){var n=a.next,i=Nd(a,t);i===0?(a.next=null,l===null?Qi=n:l.next=n,n===null&&(Aa=l)):(l=a,(e!==0||(i&3)!==0)&&(Ki=!0)),a=n}Tn(e)}function Nd(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var o=31-lt(i),h=1<<o,g=n[o];g===-1?((h&l)===0||(h&a)!==0)&&(n[o]=cm(h,t)):g<=t&&(e.expiredLanes|=h),i&=~h}if(t=ye,l=le,l=Pn(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(oe===2||oe===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&wu(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||Ga(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&wu(a),Mu(l)){case 2:case 8:l=hr;break;case 32:l=kn;break;case 268435456:l=mr;break;default:l=kn}return a=Td.bind(null,e),l=ju(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&wu(a),e.callbackPriority=2,e.callbackNode=null,2}function Td(e,t){if(Ze!==0&&Ze!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(Zi()&&e.callbackNode!==l)return null;var a=le;return a=Pn(e,e===ye?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(sd(e,a,t),Nd(e,jt()),e.callbackNode!=null&&e.callbackNode===l?Td.bind(null,e):null)}function Ad(e,t){if(Zi())return null;sd(e,t,!0)}function H0(){$0(function(){(re&6)!==0?ju(dr,L0):_d()})}function yc(){return Xl===0&&(Xl=gr()),Xl}function jd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ai(""+e)}function wd(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function q0(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var i=jd((n[Je]||null).action),o=a.submitter;o&&(t=(t=o[Je]||null)?jd(t.formAction):o.getAttribute("formAction"),t!==null&&(i=t,o=null));var h=new si("action","action",null,a,n);e.push({event:h,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Xl!==0){var g=o?wd(n,o):new FormData(n);Hs(l,{pending:!0,data:g,method:n.method,action:i},null,g)}}else typeof i=="function"&&(h.preventDefault(),g=o?wd(n,o):new FormData(n),Hs(l,{pending:!0,data:g,method:n.method,action:i},i,g))},currentTarget:n}]})}}for(var xc=0;xc<ls.length;xc++){var vc=ls[xc],B0=vc.toLowerCase(),G0=vc[0].toUpperCase()+vc.slice(1);bt(B0,"on"+G0)}bt(io,"onAnimationEnd"),bt(uo,"onAnimationIteration"),bt(so,"onAnimationStart"),bt("dblclick","onDoubleClick"),bt("focusin","onFocus"),bt("focusout","onBlur"),bt(a0,"onTransitionRun"),bt(n0,"onTransitionStart"),bt(i0,"onTransitionCancel"),bt(co,"onTransitionEnd"),Jl("onMouseEnter",["mouseout","mouseover"]),Jl("onMouseLeave",["mouseout","mouseover"]),Jl("onPointerEnter",["pointerout","pointerover"]),Jl("onPointerLeave",["pointerout","pointerover"]),jl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),jl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),jl("onBeforeInput",["compositionend","keypress","textInput","paste"]),jl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),jl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),jl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var An="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),V0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(An));function Dd(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var i=void 0;if(t)for(var o=a.length-1;0<=o;o--){var h=a[o],g=h.instance,N=h.currentTarget;if(h=h.listener,g!==i&&n.isPropagationStopped())break e;i=h,n.currentTarget=N;try{i(n)}catch(O){Ui(O)}n.currentTarget=null,i=g}else for(o=0;o<a.length;o++){if(h=a[o],g=h.instance,N=h.currentTarget,h=h.listener,g!==i&&n.isPropagationStopped())break e;i=h,n.currentTarget=N;try{i(n)}catch(O){Ui(O)}n.currentTarget=null,i=g}}}}function te(e,t){var l=t[Ou];l===void 0&&(l=t[Ou]=new Set);var a=e+"__bubble";l.has(a)||(Rd(t,e,2,!1),l.add(a))}function bc(e,t,l){var a=0;t&&(a|=4),Rd(l,e,a,t)}var $i="_reactListening"+Math.random().toString(36).slice(2);function Sc(e){if(!e[$i]){e[$i]=!0,Sr.forEach(function(l){l!=="selectionchange"&&(V0.has(l)||bc(l,!1,e),bc(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[$i]||(t[$i]=!0,bc("selectionchange",!1,t))}}function Rd(e,t,l,a){switch(eh(t)){case 2:var n=mg;break;case 8:n=gg;break;default:n=Cc}l=n.bind(null,t,l,e),n=void 0,!Xu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Ec(e,t,l,a,n){var i=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var o=a.tag;if(o===3||o===4){var h=a.stateNode.containerInfo;if(h===n)break;if(o===4)for(o=a.return;o!==null;){var g=o.tag;if((g===3||g===4)&&o.stateNode.containerInfo===n)return;o=o.return}for(;h!==null;){if(o=$l(h),o===null)return;if(g=o.tag,g===5||g===6||g===26||g===27){a=i=o;continue e}h=h.parentNode}}a=a.return}Cr(function(){var N=i,O=Vu(l),H=[];e:{var A=ro.get(e);if(A!==void 0){var j=si,k=e;switch(e){case"keypress":if(ii(l)===0)break e;case"keydown":case"keyup":j=Cm;break;case"focusin":k="focus",j=$u;break;case"focusout":k="blur",j=$u;break;case"beforeblur":case"afterblur":j=$u;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=qr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=_m;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=qm;break;case io:case uo:case so:j=Am;break;case co:j=Gm;break;case"scroll":case"scrollend":j=Sm;break;case"wheel":j=Ym;break;case"copy":case"cut":case"paste":j=wm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=Gr;break;case"toggle":case"beforetoggle":j=Zm}var K=(t&4)!==0,me=!K&&(e==="scroll"||e==="scrollend"),S=K?A!==null?A+"Capture":null:A;K=[];for(var b=N,_;b!==null;){var z=b;if(_=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||_===null||S===null||(z=Za(b,S),z!=null&&K.push(jn(b,z,_))),me)break;b=b.return}0<K.length&&(A=new j(A,k,null,l,O),H.push({event:A,listeners:K}))}}if((t&7)===0){e:{if(A=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",A&&l!==Gu&&(k=l.relatedTarget||l.fromElement)&&($l(k)||k[Kl]))break e;if((j||A)&&(A=O.window===O?O:(A=O.ownerDocument)?A.defaultView||A.parentWindow:window,j?(k=l.relatedTarget||l.toElement,j=N,k=k?$l(k):null,k!==null&&(me=m(k),K=k.tag,k!==me||K!==5&&K!==27&&K!==6)&&(k=null)):(j=null,k=N),j!==k)){if(K=qr,z="onMouseLeave",S="onMouseEnter",b="mouse",(e==="pointerout"||e==="pointerover")&&(K=Gr,z="onPointerLeave",S="onPointerEnter",b="pointer"),me=j==null?A:Xa(j),_=k==null?A:Xa(k),A=new K(z,b+"leave",j,l,O),A.target=me,A.relatedTarget=_,z=null,$l(O)===N&&(K=new K(S,b+"enter",k,l,O),K.target=_,K.relatedTarget=me,z=K),me=z,j&&k)t:{for(K=j,S=k,b=0,_=K;_;_=ja(_))b++;for(_=0,z=S;z;z=ja(z))_++;for(;0<b-_;)K=ja(K),b--;for(;0<_-b;)S=ja(S),_--;for(;b--;){if(K===S||S!==null&&K===S.alternate)break t;K=ja(K),S=ja(S)}K=null}else K=null;j!==null&&Md(H,A,j,K,!1),k!==null&&me!==null&&Md(H,me,k,K,!0)}}e:{if(A=N?Xa(N):window,j=A.nodeName&&A.nodeName.toLowerCase(),j==="select"||j==="input"&&A.type==="file")var Y=Ir;else if(Kr(A))if(kr)Y=e0;else{Y=Pm;var F=Wm}else j=A.nodeName,!j||j.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?N&&Bu(N.elementType)&&(Y=Ir):Y=Fm;if(Y&&(Y=Y(e,N))){$r(H,Y,l,O);break e}F&&F(e,A,N),e==="focusout"&&N&&A.type==="number"&&N.memoizedProps.value!=null&&qu(A,"number",A.value)}switch(F=N?Xa(N):window,e){case"focusin":(Kr(F)||F.contentEditable==="true")&&(aa=F,Fu=N,Pa=null);break;case"focusout":Pa=Fu=aa=null;break;case"mousedown":es=!0;break;case"contextmenu":case"mouseup":case"dragend":es=!1,ao(H,l,O);break;case"selectionchange":if(l0)break;case"keydown":case"keyup":ao(H,l,O)}var Z;if(ku)e:{switch(e){case"compositionstart":var I="onCompositionStart";break e;case"compositionend":I="onCompositionEnd";break e;case"compositionupdate":I="onCompositionUpdate";break e}I=void 0}else la?Zr(e,l)&&(I="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(I="onCompositionStart");I&&(Vr&&l.locale!=="ko"&&(la||I!=="onCompositionStart"?I==="onCompositionEnd"&&la&&(Z=Lr()):(tl=O,Zu="value"in tl?tl.value:tl.textContent,la=!0)),F=Ii(N,I),0<F.length&&(I=new Br(I,e,null,l,O),H.push({event:I,listeners:F}),Z?I.data=Z:(Z=Qr(l),Z!==null&&(I.data=Z)))),(Z=Km?$m(e,l):Im(e,l))&&(I=Ii(N,"onBeforeInput"),0<I.length&&(F=new Br("onBeforeInput","beforeinput",null,l,O),H.push({event:F,listeners:I}),F.data=Z)),q0(H,e,N,l,O)}Dd(H,t)})}function jn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Ii(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=Za(e,l),n!=null&&a.unshift(jn(e,n,i)),n=Za(e,t),n!=null&&a.push(jn(e,n,i))),e.tag===3)return a;e=e.return}return[]}function ja(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Md(e,t,l,a,n){for(var i=t._reactName,o=[];l!==null&&l!==a;){var h=l,g=h.alternate,N=h.stateNode;if(h=h.tag,g!==null&&g===a)break;h!==5&&h!==26&&h!==27||N===null||(g=N,n?(N=Za(l,i),N!=null&&o.unshift(jn(l,N,g))):n||(N=Za(l,i),N!=null&&o.push(jn(l,N,g)))),l=l.return}o.length!==0&&e.push({event:t,listeners:o})}var Y0=/\r\n?/g,X0=/\u0000|\uFFFD/g;function Od(e){return(typeof e=="string"?e:""+e).replace(Y0,`
`).replace(X0,"")}function Ud(e,t){return t=Od(t),Od(e)===t}function ki(){}function he(e,t,l,a,n,i){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Fl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Fl(e,""+a);break;case"className":ei(e,"class",a);break;case"tabIndex":ei(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":ei(e,l,a);break;case"style":Ur(e,a,i);break;case"data":if(t!=="object"){ei(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=ai(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(t!=="input"&&he(e,t,"name",n.name,n,null),he(e,t,"formEncType",n.formEncType,n,null),he(e,t,"formMethod",n.formMethod,n,null),he(e,t,"formTarget",n.formTarget,n,null)):(he(e,t,"encType",n.encType,n,null),he(e,t,"method",n.method,n,null),he(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=ai(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=ki);break;case"onScroll":a!=null&&te("scroll",e);break;case"onScrollEnd":a!=null&&te("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=ai(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":te("beforetoggle",e),te("toggle",e),Fn(e,"popover",a);break;case"xlinkActuate":zt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":zt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":zt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":zt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":zt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":zt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":zt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":zt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":zt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Fn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=vm.get(l)||l,Fn(e,l,a))}}function _c(e,t,l,a,n,i){switch(l){case"style":Ur(e,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Fl(e,a):(typeof a=="number"||typeof a=="bigint")&&Fl(e,""+a);break;case"onScroll":a!=null&&te("scroll",e);break;case"onScrollEnd":a!=null&&te("scrollend",e);break;case"onClick":a!=null&&(e.onclick=ki);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Er.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),i=e[Je]||null,i=i!=null?i[l]:null,typeof i=="function"&&e.removeEventListener(t,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Fn(e,l,a)}}}function Qe(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":te("error",e),te("load",e);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var o=l[i];if(o!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:he(e,t,i,o,l,null)}}n&&he(e,t,"srcSet",l.srcSet,l,null),a&&he(e,t,"src",l.src,l,null);return;case"input":te("invalid",e);var h=i=o=n=null,g=null,N=null;for(a in l)if(l.hasOwnProperty(a)){var O=l[a];if(O!=null)switch(a){case"name":n=O;break;case"type":o=O;break;case"checked":g=O;break;case"defaultChecked":N=O;break;case"value":i=O;break;case"defaultValue":h=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(c(137,t));break;default:he(e,t,a,O,l,null)}}Dr(e,i,h,g,N,o,n,!1),ti(e);return;case"select":te("invalid",e),a=o=i=null;for(n in l)if(l.hasOwnProperty(n)&&(h=l[n],h!=null))switch(n){case"value":i=h;break;case"defaultValue":o=h;break;case"multiple":a=h;default:he(e,t,n,h,l,null)}t=i,l=o,e.multiple=!!a,t!=null?Pl(e,!!a,t,!1):l!=null&&Pl(e,!!a,l,!0);return;case"textarea":te("invalid",e),i=n=a=null;for(o in l)if(l.hasOwnProperty(o)&&(h=l[o],h!=null))switch(o){case"value":a=h;break;case"defaultValue":n=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(c(91));break;default:he(e,t,o,h,l,null)}Mr(e,a,n,i),ti(e);return;case"option":for(g in l)if(l.hasOwnProperty(g)&&(a=l[g],a!=null))switch(g){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:he(e,t,g,a,l,null)}return;case"dialog":te("beforetoggle",e),te("toggle",e),te("cancel",e),te("close",e);break;case"iframe":case"object":te("load",e);break;case"video":case"audio":for(a=0;a<An.length;a++)te(An[a],e);break;case"image":te("error",e),te("load",e);break;case"details":te("toggle",e);break;case"embed":case"source":case"link":te("error",e),te("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in l)if(l.hasOwnProperty(N)&&(a=l[N],a!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:he(e,t,N,a,l,null)}return;default:if(Bu(t)){for(O in l)l.hasOwnProperty(O)&&(a=l[O],a!==void 0&&_c(e,t,O,a,l,void 0));return}}for(h in l)l.hasOwnProperty(h)&&(a=l[h],a!=null&&he(e,t,h,a,l,null))}function Z0(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,o=null,h=null,g=null,N=null,O=null;for(j in l){var H=l[j];if(l.hasOwnProperty(j)&&H!=null)switch(j){case"checked":break;case"value":break;case"defaultValue":g=H;default:a.hasOwnProperty(j)||he(e,t,j,null,a,H)}}for(var A in a){var j=a[A];if(H=l[A],a.hasOwnProperty(A)&&(j!=null||H!=null))switch(A){case"type":i=j;break;case"name":n=j;break;case"checked":N=j;break;case"defaultChecked":O=j;break;case"value":o=j;break;case"defaultValue":h=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(c(137,t));break;default:j!==H&&he(e,t,A,j,a,H)}}Hu(e,o,h,g,N,O,i,n);return;case"select":j=o=h=A=null;for(i in l)if(g=l[i],l.hasOwnProperty(i)&&g!=null)switch(i){case"value":break;case"multiple":j=g;default:a.hasOwnProperty(i)||he(e,t,i,null,a,g)}for(n in a)if(i=a[n],g=l[n],a.hasOwnProperty(n)&&(i!=null||g!=null))switch(n){case"value":A=i;break;case"defaultValue":h=i;break;case"multiple":o=i;default:i!==g&&he(e,t,n,i,a,g)}t=h,l=o,a=j,A!=null?Pl(e,!!l,A,!1):!!a!=!!l&&(t!=null?Pl(e,!!l,t,!0):Pl(e,!!l,l?[]:"",!1));return;case"textarea":j=A=null;for(h in l)if(n=l[h],l.hasOwnProperty(h)&&n!=null&&!a.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:he(e,t,h,null,a,n)}for(o in a)if(n=a[o],i=l[o],a.hasOwnProperty(o)&&(n!=null||i!=null))switch(o){case"value":A=n;break;case"defaultValue":j=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(c(91));break;default:n!==i&&he(e,t,o,n,a,i)}Rr(e,A,j);return;case"option":for(var k in l)if(A=l[k],l.hasOwnProperty(k)&&A!=null&&!a.hasOwnProperty(k))switch(k){case"selected":e.selected=!1;break;default:he(e,t,k,null,a,A)}for(g in a)if(A=a[g],j=l[g],a.hasOwnProperty(g)&&A!==j&&(A!=null||j!=null))switch(g){case"selected":e.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:he(e,t,g,A,a,j)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var K in l)A=l[K],l.hasOwnProperty(K)&&A!=null&&!a.hasOwnProperty(K)&&he(e,t,K,null,a,A);for(N in a)if(A=a[N],j=l[N],a.hasOwnProperty(N)&&A!==j&&(A!=null||j!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(c(137,t));break;default:he(e,t,N,A,a,j)}return;default:if(Bu(t)){for(var me in l)A=l[me],l.hasOwnProperty(me)&&A!==void 0&&!a.hasOwnProperty(me)&&_c(e,t,me,void 0,a,A);for(O in a)A=a[O],j=l[O],!a.hasOwnProperty(O)||A===j||A===void 0&&j===void 0||_c(e,t,O,A,a,j);return}}for(var S in l)A=l[S],l.hasOwnProperty(S)&&A!=null&&!a.hasOwnProperty(S)&&he(e,t,S,null,a,A);for(H in a)A=a[H],j=l[H],!a.hasOwnProperty(H)||A===j||A==null&&j==null||he(e,t,H,A,a,j)}var Nc=null,Tc=null;function Ji(e){return e.nodeType===9?e:e.ownerDocument}function zd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Cd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ac(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var jc=null;function Q0(){var e=window.event;return e&&e.type==="popstate"?e===jc?!1:(jc=e,!0):(jc=null,!1)}var Ld=typeof setTimeout=="function"?setTimeout:void 0,K0=typeof clearTimeout=="function"?clearTimeout:void 0,Hd=typeof Promise=="function"?Promise:void 0,$0=typeof queueMicrotask=="function"?queueMicrotask:typeof Hd<"u"?function(e){return Hd.resolve(null).then(e).catch(I0)}:Ld;function I0(e){setTimeout(function(){throw e})}function yl(e){return e==="head"}function qd(e,t){var l=t,a=0,n=0;do{var i=l.nextSibling;if(e.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var o=e.ownerDocument;if(l&1&&wn(o.documentElement),l&2&&wn(o.body),l&4)for(l=o.head,wn(l),o=l.firstChild;o;){var h=o.nextSibling,g=o.nodeName;o[Ya]||g==="SCRIPT"||g==="STYLE"||g==="LINK"&&o.rel.toLowerCase()==="stylesheet"||l.removeChild(o),o=h}}if(n===0){e.removeChild(i),Ln(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);Ln(t)}function wc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":wc(l),Uu(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function k0(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ya])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=Et(e.nextSibling),e===null)break}return null}function J0(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Et(e.nextSibling),e===null))return null;return e}function Dc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function W0(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Rc=null;function Bd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function Gd(e,t,l){switch(t=Ji(l),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function wn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Uu(e)}var xt=new Map,Vd=new Set;function Wi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var It=G.d;G.d={f:P0,r:F0,D:eg,C:tg,L:lg,m:ag,X:ig,S:ng,M:ug};function P0(){var e=It.f(),t=Yi();return e||t}function F0(e){var t=Il(e);t!==null&&t.tag===5&&t.type==="form"?sf(t):It.r(e)}var wa=typeof document>"u"?null:document;function Yd(e,t,l){var a=wa;if(a&&typeof t=="string"&&t){var n=ft(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Vd.has(n)||(Vd.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),Qe(t,"link",e),Be(t),a.head.appendChild(t)))}}function eg(e){It.D(e),Yd("dns-prefetch",e,null)}function tg(e,t){It.C(e,t),Yd("preconnect",e,t)}function lg(e,t,l){It.L(e,t,l);var a=wa;if(a&&e&&t){var n='link[rel="preload"][as="'+ft(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+ft(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+ft(l.imageSizes)+'"]')):n+='[href="'+ft(e)+'"]';var i=n;switch(t){case"style":i=Da(e);break;case"script":i=Ra(e)}xt.has(i)||(e=E({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),xt.set(i,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(Dn(i))||t==="script"&&a.querySelector(Rn(i))||(t=a.createElement("link"),Qe(t,"link",e),Be(t),a.head.appendChild(t)))}}function ag(e,t){It.m(e,t);var l=wa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+ft(a)+'"][href="'+ft(e)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ra(e)}if(!xt.has(i)&&(e=E({rel:"modulepreload",href:e},t),xt.set(i,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Rn(i)))return}a=l.createElement("link"),Qe(a,"link",e),Be(a),l.head.appendChild(a)}}}function ng(e,t,l){It.S(e,t,l);var a=wa;if(a&&e){var n=kl(a).hoistableStyles,i=Da(e);t=t||"default";var o=n.get(i);if(!o){var h={loading:0,preload:null};if(o=a.querySelector(Dn(i)))h.loading=5;else{e=E({rel:"stylesheet",href:e,"data-precedence":t},l),(l=xt.get(i))&&Mc(e,l);var g=o=a.createElement("link");Be(g),Qe(g,"link",e),g._p=new Promise(function(N,O){g.onload=N,g.onerror=O}),g.addEventListener("load",function(){h.loading|=1}),g.addEventListener("error",function(){h.loading|=2}),h.loading|=4,Pi(o,t,a)}o={type:"stylesheet",instance:o,count:1,state:h},n.set(i,o)}}}function ig(e,t){It.X(e,t);var l=wa;if(l&&e){var a=kl(l).hoistableScripts,n=Ra(e),i=a.get(n);i||(i=l.querySelector(Rn(n)),i||(e=E({src:e,async:!0},t),(t=xt.get(n))&&Oc(e,t),i=l.createElement("script"),Be(i),Qe(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function ug(e,t){It.M(e,t);var l=wa;if(l&&e){var a=kl(l).hoistableScripts,n=Ra(e),i=a.get(n);i||(i=l.querySelector(Rn(n)),i||(e=E({src:e,async:!0,type:"module"},t),(t=xt.get(n))&&Oc(e,t),i=l.createElement("script"),Be(i),Qe(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function Xd(e,t,l,a){var n=(n=Pt.current)?Wi(n):null;if(!n)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Da(l.href),l=kl(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Da(l.href);var i=kl(n).hoistableStyles,o=i.get(e);if(o||(n=n.ownerDocument||n,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,o),(i=n.querySelector(Dn(e)))&&!i._p&&(o.instance=i,o.state.loading=5),xt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},xt.set(e,l),i||sg(n,e,l,o.state))),t&&a===null)throw Error(c(528,""));return o}if(t&&a!==null)throw Error(c(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ra(l),l=kl(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Da(e){return'href="'+ft(e)+'"'}function Dn(e){return'link[rel="stylesheet"]['+e+"]"}function Zd(e){return E({},e,{"data-precedence":e.precedence,precedence:null})}function sg(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Qe(t,"link",l),Be(t),e.head.appendChild(t))}function Ra(e){return'[src="'+ft(e)+'"]'}function Rn(e){return"script[async]"+e}function Qd(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+ft(l.href)+'"]');if(a)return t.instance=a,Be(a),a;var n=E({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Be(a),Qe(a,"style",n),Pi(a,l.precedence,e),t.instance=a;case"stylesheet":n=Da(l.href);var i=e.querySelector(Dn(n));if(i)return t.state.loading|=4,t.instance=i,Be(i),i;a=Zd(l),(n=xt.get(n))&&Mc(a,n),i=(e.ownerDocument||e).createElement("link"),Be(i);var o=i;return o._p=new Promise(function(h,g){o.onload=h,o.onerror=g}),Qe(i,"link",a),t.state.loading|=4,Pi(i,l.precedence,e),t.instance=i;case"script":return i=Ra(l.src),(n=e.querySelector(Rn(i)))?(t.instance=n,Be(n),n):(a=l,(n=xt.get(i))&&(a=E({},l),Oc(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Be(n),Qe(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Pi(a,l.precedence,e));return t.instance}function Pi(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,o=0;o<a.length;o++){var h=a[o];if(h.dataset.precedence===t)i=h;else if(i!==n)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function Mc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Oc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Fi=null;function Kd(e,t,l){if(Fi===null){var a=new Map,n=Fi=new Map;n.set(l,a)}else n=Fi,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var i=l[n];if(!(i[Ya]||i[Ke]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var o=i.getAttribute(t)||"";o=e+o;var h=a.get(o);h?h.push(i):a.set(o,[i])}}return a}function $d(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function cg(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Id(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Mn=null;function rg(){}function og(e,t,l){if(Mn===null)throw Error(c(475));var a=Mn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Da(l.href),i=e.querySelector(Dn(n));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=eu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=i,Be(i);return}i=e.ownerDocument||e,l=Zd(l),(n=xt.get(n))&&Mc(l,n),i=i.createElement("link"),Be(i);var o=i;o._p=new Promise(function(h,g){o.onload=h,o.onerror=g}),Qe(i,"link",l),t.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=eu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function fg(){if(Mn===null)throw Error(c(475));var e=Mn;return e.stylesheets&&e.count===0&&Uc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&Uc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function eu(){if(this.count--,this.count===0){if(this.stylesheets)Uc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var tu=null;function Uc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,tu=new Map,t.forEach(dg,e),tu=null,eu.call(e))}function dg(e,t){if(!(t.state.loading&4)){var l=tu.get(e);if(l)var a=l.get(null);else{l=new Map,tu.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var o=n[i];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(l.set(o.dataset.precedence,o),a=o)}a&&l.set(null,a)}n=t.instance,o=n.getAttribute("data-precedence"),i=l.get(o)||a,i===a&&l.set(null,n),l.set(o,n),this.count++,a=eu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var On={$$typeof:B,Provider:null,Consumer:null,_currentValue:J,_currentValue2:J,_threadCount:0};function hg(e,t,l,a,n,i,o,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Du(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Du(0),this.hiddenUpdates=Du(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function kd(e,t,l,a,n,i,o,h,g,N,O,H){return e=new hg(e,t,l,o,h,g,N,H),t=1,i===!0&&(t|=24),i=nt(3,null,null,t),e.current=i,i.stateNode=e,t=ms(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:t},xs(i),e}function Jd(e){return e?(e=sa,e):sa}function Wd(e,t,l,a,n,i){n=Jd(n),a.context===null?a.context=n:a.pendingContext=n,a=nl(t),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=il(e,a,t),l!==null&&(rt(l,e,t),cn(l,e,t))}function Pd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function zc(e,t){Pd(e,t),(e=e.alternate)&&Pd(e,t)}function Fd(e){if(e.tag===13){var t=ua(e,67108864);t!==null&&rt(t,e,67108864),zc(e,67108864)}}var lu=!0;function mg(e,t,l,a){var n=C.T;C.T=null;var i=G.p;try{G.p=2,Cc(e,t,l,a)}finally{G.p=i,C.T=n}}function gg(e,t,l,a){var n=C.T;C.T=null;var i=G.p;try{G.p=8,Cc(e,t,l,a)}finally{G.p=i,C.T=n}}function Cc(e,t,l,a){if(lu){var n=Lc(a);if(n===null)Ec(e,t,a,au,l),th(e,a);else if(yg(n,e,t,l,a))a.stopPropagation();else if(th(e,a),t&4&&-1<pg.indexOf(e)){for(;n!==null;){var i=Il(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var o=Al(i.pendingLanes);if(o!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;o;){var g=1<<31-lt(o);h.entanglements[1]|=g,o&=~g}Ot(i),(re&6)===0&&(Gi=jt()+500,Tn(0))}}break;case 13:h=ua(i,2),h!==null&&rt(h,i,2),Yi(),zc(i,2)}if(i=Lc(a),i===null&&Ec(e,t,a,au,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else Ec(e,t,a,null,l)}}function Lc(e){return e=Vu(e),Hc(e)}var au=null;function Hc(e){if(au=null,e=$l(e),e!==null){var t=m(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=y(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return au=e,null}function eh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(tm()){case dr:return 2;case hr:return 8;case kn:case lm:return 32;case mr:return 268435456;default:return 32}default:return 32}}var qc=!1,xl=null,vl=null,bl=null,Un=new Map,zn=new Map,Sl=[],pg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function th(e,t){switch(e){case"focusin":case"focusout":xl=null;break;case"dragenter":case"dragleave":vl=null;break;case"mouseover":case"mouseout":bl=null;break;case"pointerover":case"pointerout":Un.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zn.delete(t.pointerId)}}function Cn(e,t,l,a,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},t!==null&&(t=Il(t),t!==null&&Fd(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function yg(e,t,l,a,n){switch(t){case"focusin":return xl=Cn(xl,e,t,l,a,n),!0;case"dragenter":return vl=Cn(vl,e,t,l,a,n),!0;case"mouseover":return bl=Cn(bl,e,t,l,a,n),!0;case"pointerover":var i=n.pointerId;return Un.set(i,Cn(Un.get(i)||null,e,t,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,zn.set(i,Cn(zn.get(i)||null,e,t,l,a,n)),!0}return!1}function lh(e){var t=$l(e.target);if(t!==null){var l=m(t);if(l!==null){if(t=l.tag,t===13){if(t=y(l),t!==null){e.blockedOn=t,om(e.priority,function(){if(l.tag===13){var a=ct();a=Ru(a);var n=ua(l,a);n!==null&&rt(n,l,a),zc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function nu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=Lc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);Gu=a,l.target.dispatchEvent(a),Gu=null}else return t=Il(l),t!==null&&Fd(t),e.blockedOn=l,!1;t.shift()}return!0}function ah(e,t,l){nu(e)&&l.delete(t)}function xg(){qc=!1,xl!==null&&nu(xl)&&(xl=null),vl!==null&&nu(vl)&&(vl=null),bl!==null&&nu(bl)&&(bl=null),Un.forEach(ah),zn.forEach(ah)}function iu(e,t){e.blockedOn===t&&(e.blockedOn=null,qc||(qc=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,xg)))}var uu=null;function nh(e){uu!==e&&(uu=e,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){uu===e&&(uu=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(Hc(a||l)===null)continue;break}var i=Il(l);i!==null&&(e.splice(t,3),t-=3,Hs(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Ln(e){function t(g){return iu(g,e)}xl!==null&&iu(xl,e),vl!==null&&iu(vl,e),bl!==null&&iu(bl,e),Un.forEach(t),zn.forEach(t);for(var l=0;l<Sl.length;l++){var a=Sl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Sl.length&&(l=Sl[0],l.blockedOn===null);)lh(l),l.blockedOn===null&&Sl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],o=n[Je]||null;if(typeof i=="function")o||nh(l);else if(o){var h=null;if(i&&i.hasAttribute("formAction")){if(n=i,o=i[Je]||null)h=o.formAction;else if(Hc(n)!==null)continue}else h=o.action;typeof h=="function"?l[a+1]=h:(l.splice(a,3),a-=3),nh(l)}}}function Bc(e){this._internalRoot=e}su.prototype.render=Bc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var l=t.current,a=ct();Wd(l,a,e,t,null,null)},su.prototype.unmount=Bc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Wd(e.current,2,null,e,null,null),Yi(),t[Kl]=null}};function su(e){this._internalRoot=e}su.prototype.unstable_scheduleHydration=function(e){if(e){var t=vr();e={blockedOn:null,target:e,priority:t};for(var l=0;l<Sl.length&&t!==0&&t<Sl[l].priority;l++);Sl.splice(l,0,e),l===0&&lh(e)}};var ih=f.version;if(ih!=="19.1.0")throw Error(c(527,ih,"19.1.0"));G.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=p(t),e=e!==null?x(e):null,e=e===null?null:e.stateNode,e};var vg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var cu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!cu.isDisabled&&cu.supportsFiber)try{Ba=cu.inject(vg),tt=cu}catch{}}return Hn.createRoot=function(e,t){if(!d(e))throw Error(c(299));var l=!1,a="",n=Sf,i=Ef,o=_f,h=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(o=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=kd(e,1,!1,null,null,l,a,n,i,o,h,null),e[Kl]=t.current,Sc(e),new Bc(t)},Hn.hydrateRoot=function(e,t,l){if(!d(e))throw Error(c(299));var a=!1,n="",i=Sf,o=Ef,h=_f,g=null,N=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(o=l.onCaughtError),l.onRecoverableError!==void 0&&(h=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(g=l.unstable_transitionCallbacks),l.formState!==void 0&&(N=l.formState)),t=kd(e,1,!0,t,l??null,a,n,i,o,h,g,N),t.context=Jd(null),l=t.current,a=ct(),a=Ru(a),n=nl(a),n.callback=null,il(l,n,a),l=a,t.current.lanes=l,Va(t,l),Ot(t),e[Kl]=t.current,Sc(e),new su(t)},Hn.version="19.1.0",Hn}var dh;function Pg(){if(dh)return Gc.exports;dh=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(f){console.error(f)}}return u(),Gc.exports=Wg(),Gc.exports}var Fg=Pg();const ep=_g(Fg);/**
 * react-router v7.7.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var hh="popstate";function tp(u={}){function f(c,d){let{pathname:m,search:y,hash:v}=c.location;return Jc("",{pathname:m,search:y,hash:v},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function r(c,d){return typeof d=="string"?d:Vn(d)}return ap(f,r,null,u)}function Ee(u,f){if(u===!1||u===null||typeof u>"u")throw new Error(f)}function _t(u,f){if(!u){typeof console<"u"&&console.warn(f);try{throw new Error(f)}catch{}}}function lp(){return Math.random().toString(36).substring(2,10)}function mh(u,f){return{usr:u.state,key:u.key,idx:f}}function Jc(u,f,r=null,c){return{pathname:typeof u=="string"?u:u.pathname,search:"",hash:"",...typeof f=="string"?za(f):f,state:r,key:f&&f.key||c||lp()}}function Vn({pathname:u="/",search:f="",hash:r=""}){return f&&f!=="?"&&(u+=f.charAt(0)==="?"?f:"?"+f),r&&r!=="#"&&(u+=r.charAt(0)==="#"?r:"#"+r),u}function za(u){let f={};if(u){let r=u.indexOf("#");r>=0&&(f.hash=u.substring(r),u=u.substring(0,r));let c=u.indexOf("?");c>=0&&(f.search=u.substring(c),u=u.substring(0,c)),u&&(f.pathname=u)}return f}function ap(u,f,r,c={}){let{window:d=document.defaultView,v5Compat:m=!1}=c,y=d.history,v="POP",p=null,x=E();x==null&&(x=0,y.replaceState({...y.state,idx:x},""));function E(){return(y.state||{idx:null}).idx}function M(){v="POP";let L=E(),U=L==null?null:L-x;x=L,p&&p({action:v,location:D.location,delta:U})}function T(L,U){v="PUSH";let V=Jc(D.location,L,U);x=E()+1;let B=mh(V,x),X=D.createHref(V);try{y.pushState(B,"",X)}catch(W){if(W instanceof DOMException&&W.name==="DataCloneError")throw W;d.location.assign(X)}m&&p&&p({action:v,location:D.location,delta:1})}function q(L,U){v="REPLACE";let V=Jc(D.location,L,U);x=E();let B=mh(V,x),X=D.createHref(V);y.replaceState(B,"",X),m&&p&&p({action:v,location:D.location,delta:0})}function R(L){return np(L)}let D={get action(){return v},get location(){return u(d,y)},listen(L){if(p)throw new Error("A history only accepts one active listener");return d.addEventListener(hh,M),p=L,()=>{d.removeEventListener(hh,M),p=null}},createHref(L){return f(d,L)},createURL:R,encodeLocation(L){let U=R(L);return{pathname:U.pathname,search:U.search,hash:U.hash}},push:T,replace:q,go(L){return y.go(L)}};return D}function np(u,f=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Ee(r,"No window.location.(origin|href) available to create URL");let c=typeof u=="string"?u:Vn(u);return c=c.replace(/ $/,"%20"),!f&&c.startsWith("//")&&(c=r+c),new URL(c,r)}function Dh(u,f,r="/"){return ip(u,f,r,!1)}function ip(u,f,r,c){let d=typeof f=="string"?za(f):f,m=Jt(d.pathname||"/",r);if(m==null)return null;let y=Rh(u);up(y);let v=null;for(let p=0;v==null&&p<y.length;++p){let x=yp(m);v=gp(y[p],x,c)}return v}function Rh(u,f=[],r=[],c=""){let d=(m,y,v)=>{let p={relativePath:v===void 0?m.path||"":v,caseSensitive:m.caseSensitive===!0,childrenIndex:y,route:m};p.relativePath.startsWith("/")&&(Ee(p.relativePath.startsWith(c),`Absolute route path "${p.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(c.length));let x=kt([c,p.relativePath]),E=r.concat(p);m.children&&m.children.length>0&&(Ee(m.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${x}".`),Rh(m.children,f,E,x)),!(m.path==null&&!m.index)&&f.push({path:x,score:hp(x,m.index),routesMeta:E})};return u.forEach((m,y)=>{if(m.path===""||!m.path?.includes("?"))d(m,y);else for(let v of Mh(m.path))d(m,y,v)}),f}function Mh(u){let f=u.split("/");if(f.length===0)return[];let[r,...c]=f,d=r.endsWith("?"),m=r.replace(/\?$/,"");if(c.length===0)return d?[m,""]:[m];let y=Mh(c.join("/")),v=[];return v.push(...y.map(p=>p===""?m:[m,p].join("/"))),d&&v.push(...y),v.map(p=>u.startsWith("/")&&p===""?"/":p)}function up(u){u.sort((f,r)=>f.score!==r.score?r.score-f.score:mp(f.routesMeta.map(c=>c.childrenIndex),r.routesMeta.map(c=>c.childrenIndex)))}var sp=/^:[\w-]+$/,cp=3,rp=2,op=1,fp=10,dp=-2,gh=u=>u==="*";function hp(u,f){let r=u.split("/"),c=r.length;return r.some(gh)&&(c+=dp),f&&(c+=rp),r.filter(d=>!gh(d)).reduce((d,m)=>d+(sp.test(m)?cp:m===""?op:fp),c)}function mp(u,f){return u.length===f.length&&u.slice(0,-1).every((c,d)=>c===f[d])?u[u.length-1]-f[f.length-1]:0}function gp(u,f,r=!1){let{routesMeta:c}=u,d={},m="/",y=[];for(let v=0;v<c.length;++v){let p=c[v],x=v===c.length-1,E=m==="/"?f:f.slice(m.length)||"/",M=pu({path:p.relativePath,caseSensitive:p.caseSensitive,end:x},E),T=p.route;if(!M&&x&&r&&!c[c.length-1].route.index&&(M=pu({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},E)),!M)return null;Object.assign(d,M.params),y.push({params:d,pathname:kt([m,M.pathname]),pathnameBase:Sp(kt([m,M.pathnameBase])),route:T}),M.pathnameBase!=="/"&&(m=kt([m,M.pathnameBase]))}return y}function pu(u,f){typeof u=="string"&&(u={path:u,caseSensitive:!1,end:!0});let[r,c]=pp(u.path,u.caseSensitive,u.end),d=f.match(r);if(!d)return null;let m=d[0],y=m.replace(/(.)\/+$/,"$1"),v=d.slice(1);return{params:c.reduce((x,{paramName:E,isOptional:M},T)=>{if(E==="*"){let R=v[T]||"";y=m.slice(0,m.length-R.length).replace(/(.)\/+$/,"$1")}const q=v[T];return M&&!q?x[E]=void 0:x[E]=(q||"").replace(/%2F/g,"/"),x},{}),pathname:m,pathnameBase:y,pattern:u}}function pp(u,f=!1,r=!0){_t(u==="*"||!u.endsWith("*")||u.endsWith("/*"),`Route path "${u}" will be treated as if it were "${u.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${u.replace(/\*$/,"/*")}".`);let c=[],d="^"+u.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(y,v,p)=>(c.push({paramName:v,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return u.endsWith("*")?(c.push({paramName:"*"}),d+=u==="*"||u==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?d+="\\/*$":u!==""&&u!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,f?void 0:"i"),c]}function yp(u){try{return u.split("/").map(f=>decodeURIComponent(f).replace(/\//g,"%2F")).join("/")}catch(f){return _t(!1,`The URL path "${u}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${f}).`),u}}function Jt(u,f){if(f==="/")return u;if(!u.toLowerCase().startsWith(f.toLowerCase()))return null;let r=f.endsWith("/")?f.length-1:f.length,c=u.charAt(r);return c&&c!=="/"?null:u.slice(r)||"/"}function xp(u,f="/"){let{pathname:r,search:c="",hash:d=""}=typeof u=="string"?za(u):u;return{pathname:r?r.startsWith("/")?r:vp(r,f):f,search:Ep(c),hash:_p(d)}}function vp(u,f){let r=f.replace(/\/+$/,"").split("/");return u.split("/").forEach(d=>{d===".."?r.length>1&&r.pop():d!=="."&&r.push(d)}),r.length>1?r.join("/"):"/"}function Xc(u,f,r,c){return`Cannot include a '${u}' character in a manually specified \`to.${f}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function bp(u){return u.filter((f,r)=>r===0||f.route.path&&f.route.path.length>0)}function er(u){let f=bp(u);return f.map((r,c)=>c===f.length-1?r.pathname:r.pathnameBase)}function tr(u,f,r,c=!1){let d;typeof u=="string"?d=za(u):(d={...u},Ee(!d.pathname||!d.pathname.includes("?"),Xc("?","pathname","search",d)),Ee(!d.pathname||!d.pathname.includes("#"),Xc("#","pathname","hash",d)),Ee(!d.search||!d.search.includes("#"),Xc("#","search","hash",d)));let m=u===""||d.pathname==="",y=m?"/":d.pathname,v;if(y==null)v=r;else{let M=f.length-1;if(!c&&y.startsWith("..")){let T=y.split("/");for(;T[0]==="..";)T.shift(),M-=1;d.pathname=T.join("/")}v=M>=0?f[M]:"/"}let p=xp(d,v),x=y&&y!=="/"&&y.endsWith("/"),E=(m||y===".")&&r.endsWith("/");return!p.pathname.endsWith("/")&&(x||E)&&(p.pathname+="/"),p}var kt=u=>u.join("/").replace(/\/\/+/g,"/"),Sp=u=>u.replace(/\/+$/,"").replace(/^\/*/,"/"),Ep=u=>!u||u==="?"?"":u.startsWith("?")?u:"?"+u,_p=u=>!u||u==="#"?"":u.startsWith("#")?u:"#"+u;function Np(u){return u!=null&&typeof u.status=="number"&&typeof u.statusText=="string"&&typeof u.internal=="boolean"&&"data"in u}var Oh=["POST","PUT","PATCH","DELETE"];new Set(Oh);var Tp=["GET",...Oh];new Set(Tp);var Ca=w.createContext(null);Ca.displayName="DataRouter";var vu=w.createContext(null);vu.displayName="DataRouterState";w.createContext(!1);var Uh=w.createContext({isTransitioning:!1});Uh.displayName="ViewTransition";var Ap=w.createContext(new Map);Ap.displayName="Fetchers";var jp=w.createContext(null);jp.displayName="Await";var Nt=w.createContext(null);Nt.displayName="Navigation";var Xn=w.createContext(null);Xn.displayName="Location";var Tt=w.createContext({outlet:null,matches:[],isDataRoute:!1});Tt.displayName="Route";var lr=w.createContext(null);lr.displayName="RouteError";function wp(u,{relative:f}={}){Ee(La(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:c}=w.useContext(Nt),{hash:d,pathname:m,search:y}=Zn(u,{relative:f}),v=m;return r!=="/"&&(v=m==="/"?r:kt([r,m])),c.createHref({pathname:v,search:y,hash:d})}function La(){return w.useContext(Xn)!=null}function Nl(){return Ee(La(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(Xn).location}var zh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Ch(u){w.useContext(Nt).static||w.useLayoutEffect(u)}function Lh(){let{isDataRoute:u}=w.useContext(Tt);return u?Yp():Dp()}function Dp(){Ee(La(),"useNavigate() may be used only in the context of a <Router> component.");let u=w.useContext(Ca),{basename:f,navigator:r}=w.useContext(Nt),{matches:c}=w.useContext(Tt),{pathname:d}=Nl(),m=JSON.stringify(er(c)),y=w.useRef(!1);return Ch(()=>{y.current=!0}),w.useCallback((p,x={})=>{if(_t(y.current,zh),!y.current)return;if(typeof p=="number"){r.go(p);return}let E=tr(p,JSON.parse(m),d,x.relative==="path");u==null&&f!=="/"&&(E.pathname=E.pathname==="/"?f:kt([f,E.pathname])),(x.replace?r.replace:r.push)(E,x.state,x)},[f,r,m,d,u])}w.createContext(null);function Rp(){let{matches:u}=w.useContext(Tt),f=u[u.length-1];return f?f.params:{}}function Zn(u,{relative:f}={}){let{matches:r}=w.useContext(Tt),{pathname:c}=Nl(),d=JSON.stringify(er(r));return w.useMemo(()=>tr(u,JSON.parse(d),c,f==="path"),[u,d,c,f])}function Mp(u,f){return Hh(u,f)}function Hh(u,f,r,c){Ee(La(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=w.useContext(Nt),{matches:m}=w.useContext(Tt),y=m[m.length-1],v=y?y.params:{},p=y?y.pathname:"/",x=y?y.pathnameBase:"/",E=y&&y.route;{let U=E&&E.path||"";qh(p,!E||U.endsWith("*")||U.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${U}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${U}"> to <Route path="${U==="/"?"*":`${U}/*`}">.`)}let M=Nl(),T;if(f){let U=typeof f=="string"?za(f):f;Ee(x==="/"||U.pathname?.startsWith(x),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${x}" but pathname "${U.pathname}" was given in the \`location\` prop.`),T=U}else T=M;let q=T.pathname||"/",R=q;if(x!=="/"){let U=x.replace(/^\//,"").split("/");R="/"+q.replace(/^\//,"").split("/").slice(U.length).join("/")}let D=Dh(u,{pathname:R});_t(E||D!=null,`No routes matched location "${T.pathname}${T.search}${T.hash}" `),_t(D==null||D[D.length-1].route.element!==void 0||D[D.length-1].route.Component!==void 0||D[D.length-1].route.lazy!==void 0,`Matched leaf route at location "${T.pathname}${T.search}${T.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=Lp(D&&D.map(U=>Object.assign({},U,{params:Object.assign({},v,U.params),pathname:kt([x,d.encodeLocation?d.encodeLocation(U.pathname).pathname:U.pathname]),pathnameBase:U.pathnameBase==="/"?x:kt([x,d.encodeLocation?d.encodeLocation(U.pathnameBase).pathname:U.pathnameBase])})),m,r,c);return f&&L?w.createElement(Xn.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...T},navigationType:"POP"}},L):L}function Op(){let u=Vp(),f=Np(u)?`${u.status} ${u.statusText}`:u instanceof Error?u.message:JSON.stringify(u),r=u instanceof Error?u.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},m={padding:"2px 4px",backgroundColor:c},y=null;return console.error("Error handled by React Router default ErrorBoundary:",u),y=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:m},"ErrorBoundary")," or"," ",w.createElement("code",{style:m},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},f),r?w.createElement("pre",{style:d},r):null,y)}var Up=w.createElement(Op,null),zp=class extends w.Component{constructor(u){super(u),this.state={location:u.location,revalidation:u.revalidation,error:u.error}}static getDerivedStateFromError(u){return{error:u}}static getDerivedStateFromProps(u,f){return f.location!==u.location||f.revalidation!=="idle"&&u.revalidation==="idle"?{error:u.error,location:u.location,revalidation:u.revalidation}:{error:u.error!==void 0?u.error:f.error,location:f.location,revalidation:u.revalidation||f.revalidation}}componentDidCatch(u,f){console.error("React Router caught the following error during render",u,f)}render(){return this.state.error!==void 0?w.createElement(Tt.Provider,{value:this.props.routeContext},w.createElement(lr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Cp({routeContext:u,match:f,children:r}){let c=w.useContext(Ca);return c&&c.static&&c.staticContext&&(f.route.errorElement||f.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=f.route.id),w.createElement(Tt.Provider,{value:u},r)}function Lp(u,f=[],r=null,c=null){if(u==null){if(!r)return null;if(r.errors)u=r.matches;else if(f.length===0&&!r.initialized&&r.matches.length>0)u=r.matches;else return null}let d=u,m=r?.errors;if(m!=null){let p=d.findIndex(x=>x.route.id&&m?.[x.route.id]!==void 0);Ee(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(m).join(",")}`),d=d.slice(0,Math.min(d.length,p+1))}let y=!1,v=-1;if(r)for(let p=0;p<d.length;p++){let x=d[p];if((x.route.HydrateFallback||x.route.hydrateFallbackElement)&&(v=p),x.route.id){let{loaderData:E,errors:M}=r,T=x.route.loader&&!E.hasOwnProperty(x.route.id)&&(!M||M[x.route.id]===void 0);if(x.route.lazy||T){y=!0,v>=0?d=d.slice(0,v+1):d=[d[0]];break}}}return d.reduceRight((p,x,E)=>{let M,T=!1,q=null,R=null;r&&(M=m&&x.route.id?m[x.route.id]:void 0,q=x.route.errorElement||Up,y&&(v<0&&E===0?(qh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),T=!0,R=null):v===E&&(T=!0,R=x.route.hydrateFallbackElement||null)));let D=f.concat(d.slice(0,E+1)),L=()=>{let U;return M?U=q:T?U=R:x.route.Component?U=w.createElement(x.route.Component,null):x.route.element?U=x.route.element:U=p,w.createElement(Cp,{match:x,routeContext:{outlet:p,matches:D,isDataRoute:r!=null},children:U})};return r&&(x.route.ErrorBoundary||x.route.errorElement||E===0)?w.createElement(zp,{location:r.location,revalidation:r.revalidation,component:q,error:M,children:L(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):L()},null)}function ar(u){return`${u} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Hp(u){let f=w.useContext(Ca);return Ee(f,ar(u)),f}function qp(u){let f=w.useContext(vu);return Ee(f,ar(u)),f}function Bp(u){let f=w.useContext(Tt);return Ee(f,ar(u)),f}function nr(u){let f=Bp(u),r=f.matches[f.matches.length-1];return Ee(r.route.id,`${u} can only be used on routes that contain a unique "id"`),r.route.id}function Gp(){return nr("useRouteId")}function Vp(){let u=w.useContext(lr),f=qp("useRouteError"),r=nr("useRouteError");return u!==void 0?u:f.errors?.[r]}function Yp(){let{router:u}=Hp("useNavigate"),f=nr("useNavigate"),r=w.useRef(!1);return Ch(()=>{r.current=!0}),w.useCallback(async(d,m={})=>{_t(r.current,zh),r.current&&(typeof d=="number"?u.navigate(d):await u.navigate(d,{fromRouteId:f,...m}))},[u,f])}var ph={};function qh(u,f,r){!f&&!ph[u]&&(ph[u]=!0,_t(!1,r))}w.memo(Xp);function Xp({routes:u,future:f,state:r}){return Hh(u,void 0,r,f)}function Ua({to:u,replace:f,state:r,relative:c}){Ee(La(),"<Navigate> may be used only in the context of a <Router> component.");let{static:d}=w.useContext(Nt);_t(!d,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:m}=w.useContext(Tt),{pathname:y}=Nl(),v=Lh(),p=tr(u,er(m),y,c==="path"),x=JSON.stringify(p);return w.useEffect(()=>{v(JSON.parse(x),{replace:f,state:r,relative:c})},[v,x,c,f,r]),null}function Ma(u){Ee(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Zp({basename:u="/",children:f=null,location:r,navigationType:c="POP",navigator:d,static:m=!1}){Ee(!La(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let y=u.replace(/^\/*/,"/"),v=w.useMemo(()=>({basename:y,navigator:d,static:m,future:{}}),[y,d,m]);typeof r=="string"&&(r=za(r));let{pathname:p="/",search:x="",hash:E="",state:M=null,key:T="default"}=r,q=w.useMemo(()=>{let R=Jt(p,y);return R==null?null:{location:{pathname:R,search:x,hash:E,state:M,key:T},navigationType:c}},[y,p,x,E,M,T,c]);return _t(q!=null,`<Router basename="${y}"> is not able to match the URL "${p}${x}${E}" because it does not start with the basename, so the <Router> won't render anything.`),q==null?null:w.createElement(Nt.Provider,{value:v},w.createElement(Xn.Provider,{children:f,value:q}))}function Qp({children:u,location:f}){return Mp(Wc(u),f)}function Wc(u,f=[]){let r=[];return w.Children.forEach(u,(c,d)=>{if(!w.isValidElement(c))return;let m=[...f,d];if(c.type===w.Fragment){r.push.apply(r,Wc(c.props.children,m));return}Ee(c.type===Ma,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ee(!c.props.index||!c.props.children,"An index route cannot have child routes.");let y={id:c.props.id||m.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(y.children=Wc(c.props.children,m)),r.push(y)}),r}var fu="get",du="application/x-www-form-urlencoded";function bu(u){return u!=null&&typeof u.tagName=="string"}function Kp(u){return bu(u)&&u.tagName.toLowerCase()==="button"}function $p(u){return bu(u)&&u.tagName.toLowerCase()==="form"}function Ip(u){return bu(u)&&u.tagName.toLowerCase()==="input"}function kp(u){return!!(u.metaKey||u.altKey||u.ctrlKey||u.shiftKey)}function Jp(u,f){return u.button===0&&(!f||f==="_self")&&!kp(u)}var ru=null;function Wp(){if(ru===null)try{new FormData(document.createElement("form"),0),ru=!1}catch{ru=!0}return ru}var Pp=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Zc(u){return u!=null&&!Pp.has(u)?(_t(!1,`"${u}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${du}"`),null):u}function Fp(u,f){let r,c,d,m,y;if($p(u)){let v=u.getAttribute("action");c=v?Jt(v,f):null,r=u.getAttribute("method")||fu,d=Zc(u.getAttribute("enctype"))||du,m=new FormData(u)}else if(Kp(u)||Ip(u)&&(u.type==="submit"||u.type==="image")){let v=u.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=u.getAttribute("formaction")||v.getAttribute("action");if(c=p?Jt(p,f):null,r=u.getAttribute("formmethod")||v.getAttribute("method")||fu,d=Zc(u.getAttribute("formenctype"))||Zc(v.getAttribute("enctype"))||du,m=new FormData(v,u),!Wp()){let{name:x,type:E,value:M}=u;if(E==="image"){let T=x?`${x}.`:"";m.append(`${T}x`,"0"),m.append(`${T}y`,"0")}else x&&m.append(x,M)}}else{if(bu(u))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=fu,c=null,d=du,y=u}return m&&d==="text/plain"&&(y=m,m=void 0),{action:c,method:r.toLowerCase(),encType:d,formData:m,body:y}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function ir(u,f){if(u===!1||u===null||typeof u>"u")throw new Error(f)}function ey(u,f,r){let c=typeof u=="string"?new URL(u,typeof window>"u"?"server://singlefetch/":window.location.origin):u;return c.pathname==="/"?c.pathname=`_root.${r}`:f&&Jt(c.pathname,f)==="/"?c.pathname=`${f.replace(/\/$/,"")}/_root.${r}`:c.pathname=`${c.pathname.replace(/\/$/,"")}.${r}`,c}async function ty(u,f){if(u.id in f)return f[u.id];try{let r=await import(u.module);return f[u.id]=r,r}catch(r){return console.error(`Error loading route module \`${u.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ly(u){return u==null?!1:u.href==null?u.rel==="preload"&&typeof u.imageSrcSet=="string"&&typeof u.imageSizes=="string":typeof u.rel=="string"&&typeof u.href=="string"}async function ay(u,f,r){let c=await Promise.all(u.map(async d=>{let m=f.routes[d.route.id];if(m){let y=await ty(m,r);return y.links?y.links():[]}return[]}));return sy(c.flat(1).filter(ly).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function yh(u,f,r,c,d,m){let y=(p,x)=>r[x]?p.route.id!==r[x].route.id:!0,v=(p,x)=>r[x].pathname!==p.pathname||r[x].route.path?.endsWith("*")&&r[x].params["*"]!==p.params["*"];return m==="assets"?f.filter((p,x)=>y(p,x)||v(p,x)):m==="data"?f.filter((p,x)=>{let E=c.routes[p.route.id];if(!E||!E.hasLoader)return!1;if(y(p,x)||v(p,x))return!0;if(p.route.shouldRevalidate){let M=p.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(u,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof M=="boolean")return M}return!0}):[]}function ny(u,f,{includeHydrateFallback:r}={}){return iy(u.map(c=>{let d=f.routes[c.route.id];if(!d)return[];let m=[d.module];return d.clientActionModule&&(m=m.concat(d.clientActionModule)),d.clientLoaderModule&&(m=m.concat(d.clientLoaderModule)),r&&d.hydrateFallbackModule&&(m=m.concat(d.hydrateFallbackModule)),d.imports&&(m=m.concat(d.imports)),m}).flat(1))}function iy(u){return[...new Set(u)]}function uy(u){let f={},r=Object.keys(u).sort();for(let c of r)f[c]=u[c];return f}function sy(u,f){let r=new Set;return new Set(f),u.reduce((c,d)=>{let m=JSON.stringify(uy(d));return r.has(m)||(r.add(m),c.push({key:m,link:d})),c},[])}function Bh(){let u=w.useContext(Ca);return ir(u,"You must render this element inside a <DataRouterContext.Provider> element"),u}function cy(){let u=w.useContext(vu);return ir(u,"You must render this element inside a <DataRouterStateContext.Provider> element"),u}var ur=w.createContext(void 0);ur.displayName="FrameworkContext";function Gh(){let u=w.useContext(ur);return ir(u,"You must render this element inside a <HydratedRouter> element"),u}function ry(u,f){let r=w.useContext(ur),[c,d]=w.useState(!1),[m,y]=w.useState(!1),{onFocus:v,onBlur:p,onMouseEnter:x,onMouseLeave:E,onTouchStart:M}=f,T=w.useRef(null);w.useEffect(()=>{if(u==="render"&&y(!0),u==="viewport"){let D=U=>{U.forEach(V=>{y(V.isIntersecting)})},L=new IntersectionObserver(D,{threshold:.5});return T.current&&L.observe(T.current),()=>{L.disconnect()}}},[u]),w.useEffect(()=>{if(c){let D=setTimeout(()=>{y(!0)},100);return()=>{clearTimeout(D)}}},[c]);let q=()=>{d(!0)},R=()=>{d(!1),y(!1)};return r?u!=="intent"?[m,T,{}]:[m,T,{onFocus:qn(v,q),onBlur:qn(p,R),onMouseEnter:qn(x,q),onMouseLeave:qn(E,R),onTouchStart:qn(M,q)}]:[!1,T,{}]}function qn(u,f){return r=>{u&&u(r),r.defaultPrevented||f(r)}}function oy({page:u,...f}){let{router:r}=Bh(),c=w.useMemo(()=>Dh(r.routes,u,r.basename),[r.routes,u,r.basename]);return c?w.createElement(dy,{page:u,matches:c,...f}):null}function fy(u){let{manifest:f,routeModules:r}=Gh(),[c,d]=w.useState([]);return w.useEffect(()=>{let m=!1;return ay(u,f,r).then(y=>{m||d(y)}),()=>{m=!0}},[u,f,r]),c}function dy({page:u,matches:f,...r}){let c=Nl(),{manifest:d,routeModules:m}=Gh(),{basename:y}=Bh(),{loaderData:v,matches:p}=cy(),x=w.useMemo(()=>yh(u,f,p,d,c,"data"),[u,f,p,d,c]),E=w.useMemo(()=>yh(u,f,p,d,c,"assets"),[u,f,p,d,c]),M=w.useMemo(()=>{if(u===c.pathname+c.search+c.hash)return[];let R=new Set,D=!1;if(f.forEach(U=>{let V=d.routes[U.route.id];!V||!V.hasLoader||(!x.some(B=>B.route.id===U.route.id)&&U.route.id in v&&m[U.route.id]?.shouldRevalidate||V.hasClientLoader?D=!0:R.add(U.route.id))}),R.size===0)return[];let L=ey(u,y,"data");return D&&R.size>0&&L.searchParams.set("_routes",f.filter(U=>R.has(U.route.id)).map(U=>U.route.id).join(",")),[L.pathname+L.search]},[y,v,c,d,x,f,u,m]),T=w.useMemo(()=>ny(E,d),[E,d]),q=fy(E);return w.createElement(w.Fragment,null,M.map(R=>w.createElement("link",{key:R,rel:"prefetch",as:"fetch",href:R,...r})),T.map(R=>w.createElement("link",{key:R,rel:"modulepreload",href:R,...r})),q.map(({key:R,link:D})=>w.createElement("link",{key:R,...D})))}function hy(...u){return f=>{u.forEach(r=>{typeof r=="function"?r(f):r!=null&&(r.current=f)})}}var Vh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Vh&&(window.__reactRouterVersion="7.7.0")}catch{}function my({basename:u,children:f,window:r}){let c=w.useRef();c.current==null&&(c.current=tp({window:r,v5Compat:!0}));let d=c.current,[m,y]=w.useState({action:d.action,location:d.location}),v=w.useCallback(p=>{w.startTransition(()=>y(p))},[y]);return w.useLayoutEffect(()=>d.listen(v),[d,v]),w.createElement(Zp,{basename:u,children:f,location:m.location,navigationType:m.action,navigator:d})}var Yh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Oa=w.forwardRef(function({onClick:f,discover:r="render",prefetch:c="none",relative:d,reloadDocument:m,replace:y,state:v,target:p,to:x,preventScrollReset:E,viewTransition:M,...T},q){let{basename:R}=w.useContext(Nt),D=typeof x=="string"&&Yh.test(x),L,U=!1;if(typeof x=="string"&&D&&(L=x,Vh))try{let De=new URL(window.location.href),ne=x.startsWith("//")?new URL(De.protocol+x):new URL(x),Ce=Jt(ne.pathname,R);ne.origin===De.origin&&Ce!=null?x=Ce+ne.search+ne.hash:U=!0}catch{_t(!1,`<Link to="${x}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let V=wp(x,{relative:d}),[B,X,W]=ry(c,T),xe=xy(x,{replace:y,state:v,target:p,preventScrollReset:E,relative:d,viewTransition:M});function ue(De){f&&f(De),De.defaultPrevented||xe(De)}let _e=w.createElement("a",{...T,...W,href:L||V,onClick:U||m?f:ue,ref:hy(q,X),target:p,"data-discover":!D&&r==="render"?"true":void 0});return B&&!D?w.createElement(w.Fragment,null,_e,w.createElement(oy,{page:V})):_e});Oa.displayName="Link";var gy=w.forwardRef(function({"aria-current":f="page",caseSensitive:r=!1,className:c="",end:d=!1,style:m,to:y,viewTransition:v,children:p,...x},E){let M=Zn(y,{relative:x.relative}),T=Nl(),q=w.useContext(vu),{navigator:R,basename:D}=w.useContext(Nt),L=q!=null&&_y(M)&&v===!0,U=R.encodeLocation?R.encodeLocation(M).pathname:M.pathname,V=T.pathname,B=q&&q.navigation&&q.navigation.location?q.navigation.location.pathname:null;r||(V=V.toLowerCase(),B=B?B.toLowerCase():null,U=U.toLowerCase()),B&&D&&(B=Jt(B,D)||B);const X=U!=="/"&&U.endsWith("/")?U.length-1:U.length;let W=V===U||!d&&V.startsWith(U)&&V.charAt(X)==="/",xe=B!=null&&(B===U||!d&&B.startsWith(U)&&B.charAt(U.length)==="/"),ue={isActive:W,isPending:xe,isTransitioning:L},_e=W?f:void 0,De;typeof c=="function"?De=c(ue):De=[c,W?"active":null,xe?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let ne=typeof m=="function"?m(ue):m;return w.createElement(Oa,{...x,"aria-current":_e,className:De,ref:E,style:ne,to:y,viewTransition:v},typeof p=="function"?p(ue):p)});gy.displayName="NavLink";var py=w.forwardRef(({discover:u="render",fetcherKey:f,navigate:r,reloadDocument:c,replace:d,state:m,method:y=fu,action:v,onSubmit:p,relative:x,preventScrollReset:E,viewTransition:M,...T},q)=>{let R=Sy(),D=Ey(v,{relative:x}),L=y.toLowerCase()==="get"?"get":"post",U=typeof v=="string"&&Yh.test(v),V=B=>{if(p&&p(B),B.defaultPrevented)return;B.preventDefault();let X=B.nativeEvent.submitter,W=X?.getAttribute("formmethod")||y;R(X||B.currentTarget,{fetcherKey:f,method:W,navigate:r,replace:d,state:m,relative:x,preventScrollReset:E,viewTransition:M})};return w.createElement("form",{ref:q,method:L,action:D,onSubmit:c?p:V,...T,"data-discover":!U&&u==="render"?"true":void 0})});py.displayName="Form";function yy(u){return`${u} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Xh(u){let f=w.useContext(Ca);return Ee(f,yy(u)),f}function xy(u,{target:f,replace:r,state:c,preventScrollReset:d,relative:m,viewTransition:y}={}){let v=Lh(),p=Nl(),x=Zn(u,{relative:m});return w.useCallback(E=>{if(Jp(E,f)){E.preventDefault();let M=r!==void 0?r:Vn(p)===Vn(x);v(u,{replace:M,state:c,preventScrollReset:d,relative:m,viewTransition:y})}},[p,v,x,r,c,f,u,d,m,y])}var vy=0,by=()=>`__${String(++vy)}__`;function Sy(){let{router:u}=Xh("useSubmit"),{basename:f}=w.useContext(Nt),r=Gp();return w.useCallback(async(c,d={})=>{let{action:m,method:y,encType:v,formData:p,body:x}=Fp(c,f);if(d.navigate===!1){let E=d.fetcherKey||by();await u.fetch(E,r,d.action||m,{preventScrollReset:d.preventScrollReset,formData:p,body:x,formMethod:d.method||y,formEncType:d.encType||v,flushSync:d.flushSync})}else await u.navigate(d.action||m,{preventScrollReset:d.preventScrollReset,formData:p,body:x,formMethod:d.method||y,formEncType:d.encType||v,replace:d.replace,state:d.state,fromRouteId:r,flushSync:d.flushSync,viewTransition:d.viewTransition})},[u,f,r])}function Ey(u,{relative:f}={}){let{basename:r}=w.useContext(Nt),c=w.useContext(Tt);Ee(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),m={...Zn(u||".",{relative:f})},y=Nl();if(u==null){m.search=y.search;let v=new URLSearchParams(m.search),p=v.getAll("index");if(p.some(E=>E==="")){v.delete("index"),p.filter(M=>M).forEach(M=>v.append("index",M));let E=v.toString();m.search=E?`?${E}`:""}}return(!u||u===".")&&d.route.index&&(m.search=m.search?m.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(m.pathname=m.pathname==="/"?r:kt([r,m.pathname])),Vn(m)}function _y(u,f={}){let r=w.useContext(Uh);Ee(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=Xh("useViewTransitionState"),d=Zn(u,{relative:f.relative});if(!r.isTransitioning)return!1;let m=Jt(r.currentLocation.pathname,c)||r.currentLocation.pathname,y=Jt(r.nextLocation.pathname,c)||r.nextLocation.pathname;return pu(d.pathname,y)!=null||pu(d.pathname,m)!=null}function Ny(u){return new Fc(function(f,r){var c=Ng(f,[]);return new Nh(function(d){var m,y=!1;return Promise.resolve(c).then(function(v){return u(v,f.getContext())}).then(f.setContext).then(function(){y||(m=r(f).subscribe({next:d.next.bind(d),error:d.error.bind(d),complete:d.complete.bind(d)}))}).catch(d.error.bind(d)),function(){y=!0,m&&m.unsubscribe()}})})}function Zh(u){return new Fc(function(f,r){return new Nh(function(c){var d,m,y;try{d=r(f).subscribe({next:function(v){if(v.errors?y=u({graphQLErrors:v.errors,response:v,operation:f,forward:r}):Tg(v)&&(y=u({protocolErrors:v.extensions[Ag],response:v,operation:f,forward:r})),y){m=y.subscribe({next:c.next.bind(c),error:c.error.bind(c),complete:c.complete.bind(c)});return}c.next(v)},error:function(v){if(y=u({operation:f,networkError:v,graphQLErrors:v&&v.result&&v.result.errors||void 0,forward:r}),y){m=y.subscribe({next:c.next.bind(c),error:c.error.bind(c),complete:c.complete.bind(c)});return}c.error(v)},complete:function(){y||c.complete.bind(c)()}})}catch(v){u({networkError:v,operation:f,forward:r}),c.error(v)}return function(){d&&d.unsubscribe(),m&&d.unsubscribe()}})})}(function(u){jg(f,u);function f(r){var c=u.call(this)||this;return c.link=Zh(r),c}return f.prototype.request=function(r,c){return this.link.request(r,c)},f})(Fc);class Bn extends Error{}Bn.prototype.name="InvalidTokenError";function Ty(u){return decodeURIComponent(atob(u).replace(/(.)/g,(f,r)=>{let c=r.charCodeAt(0).toString(16).toUpperCase();return c.length<2&&(c="0"+c),"%"+c}))}function Ay(u){let f=u.replace(/-/g,"+").replace(/_/g,"/");switch(f.length%4){case 0:break;case 2:f+="==";break;case 3:f+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return Ty(f)}catch{return atob(f)}}function Qh(u,f){if(typeof u!="string")throw new Bn("Invalid token specified: must be a string");f||(f={});const r=f.header===!0?0:1,c=u.split(".")[r];if(typeof c!="string")throw new Bn(`Invalid token specified: missing part #${r+1}`);let d;try{d=Ay(c)}catch(m){throw new Bn(`Invalid token specified: invalid base64 for part #${r+1} (${m.message})`)}try{return JSON.parse(d)}catch(m){throw new Bn(`Invalid token specified: invalid json for part #${r+1} (${m.message})`)}}const sr={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_DEBUG_MODE:"true",VITE_SHOW_DEV_TOOLS:"true",VITE_USE_TEST_AUTH:"true"},Q=(u,f="")=>sr[u]||f,Oe=(u,f)=>{const r=sr[u];return r?parseInt(r,10):f},ce=(u,f)=>{const r=sr[u];return r?r.toLowerCase()==="true":f},jy={baseURL:Q("VITE_API_BASE_URL","https://learn.reboot01.com/api"),graphqlEndpoint:Q("VITE_GRAPHQL_ENDPOINT","https://learn.reboot01.com/api/graphql-engine/v1/graphql"),authEndpoint:Q("VITE_AUTH_ENDPOINT","https://learn.reboot01.com/api/auth/signin"),timeout:Oe("VITE_API_TIMEOUT",3e4),retryAttempts:Oe("VITE_API_RETRY_ATTEMPTS",3),retryDelay:Oe("VITE_API_RETRY_DELAY",1e3),defaultHeaders:{"Content-Type":"application/json",Accept:"application/json"}},wy={tokenKey:Q("VITE_AUTH_TOKEN_KEY","auth_token"),userKey:Q("VITE_AUTH_USER_KEY","auth_user"),refreshTokenKey:Q("VITE_AUTH_REFRESH_TOKEN_KEY","refresh_token"),tokenExpiry:Oe("VITE_AUTH_TOKEN_EXPIRY",24*60*60*1e3),autoRefresh:ce("VITE_AUTH_AUTO_REFRESH",!0),sessionTimeout:Oe("VITE_AUTH_SESSION_TIMEOUT",30*60*1e3),rememberMeDuration:Oe("VITE_AUTH_REMEMBER_DURATION",30*24*60*60*1e3)},Dy={enabled:ce("VITE_CACHE_ENABLED",!0),durations:{userData:Oe("VITE_CACHE_USER_DATA",5*60*1e3),statistics:Oe("VITE_CACHE_STATISTICS",10*60*1e3),analytics:Oe("VITE_CACHE_ANALYTICS",15*60*1e3),charts:Oe("VITE_CACHE_CHARTS",20*60*1e3),achievements:Oe("VITE_CACHE_ACHIEVEMENTS",30*60*1e3)},maxEntries:Oe("VITE_CACHE_MAX_ENTRIES",100),maxMemoryMB:Oe("VITE_CACHE_MAX_MEMORY_MB",50),getKey:(u,f)=>{const r=f?`_${f}`:"";return`${u}${r}`}},Ry={theme:{primary:Q("VITE_THEME_PRIMARY","#14b8a6"),secondary:Q("VITE_THEME_SECONDARY","#64748b"),accent:Q("VITE_THEME_ACCENT","#d946ef"),background:Q("VITE_THEME_BACKGROUND","#0f172a"),surface:Q("VITE_THEME_SURFACE","#1e293b")},sizes:{avatar:{xs:Q("VITE_SIZE_AVATAR_XS","w-6 h-6"),sm:Q("VITE_SIZE_AVATAR_SM","w-8 h-8"),md:Q("VITE_SIZE_AVATAR_MD","w-12 h-12"),lg:Q("VITE_SIZE_AVATAR_LG","w-16 h-16"),xl:Q("VITE_SIZE_AVATAR_XL","w-24 h-24"),"2xl":Q("VITE_SIZE_AVATAR_2XL","w-32 h-32")},avatarIcon:{xs:Q("VITE_SIZE_AVATAR_ICON_XS","w-3 h-3"),sm:Q("VITE_SIZE_AVATAR_ICON_SM","w-4 h-4"),md:Q("VITE_SIZE_AVATAR_ICON_MD","w-6 h-6"),lg:Q("VITE_SIZE_AVATAR_ICON_LG","w-8 h-8"),xl:Q("VITE_SIZE_AVATAR_ICON_XL","w-12 h-12"),"2xl":Q("VITE_SIZE_AVATAR_ICON_2XL","w-16 h-16")},avatarStatus:{xs:Q("VITE_SIZE_AVATAR_STATUS_XS","w-2 h-2"),sm:Q("VITE_SIZE_AVATAR_STATUS_SM","w-2 h-2"),md:Q("VITE_SIZE_AVATAR_STATUS_MD","w-3 h-3"),lg:Q("VITE_SIZE_AVATAR_STATUS_LG","w-4 h-4"),xl:Q("VITE_SIZE_AVATAR_STATUS_XL","w-5 h-5"),"2xl":Q("VITE_SIZE_AVATAR_STATUS_2XL","w-6 h-6")},button:{sm:Q("VITE_SIZE_BUTTON_SM","px-3 py-1.5 text-sm rounded-md"),md:Q("VITE_SIZE_BUTTON_MD","px-4 py-2 text-sm rounded-lg"),lg:Q("VITE_SIZE_BUTTON_LG","px-6 py-3 text-base rounded-lg"),xl:Q("VITE_SIZE_BUTTON_XL","px-8 py-4 text-lg rounded-xl")}},animations:{enabled:ce("VITE_ANIMATIONS_ENABLED",!0),duration:Oe("VITE_ANIMATION_DURATION",200),easing:Q("VITE_ANIMATION_EASING","ease-out")},layout:{maxWidth:Q("VITE_LAYOUT_MAX_WIDTH","1200px"),sidebarWidth:Q("VITE_LAYOUT_SIDEBAR_WIDTH","280px"),headerHeight:Q("VITE_LAYOUT_HEADER_HEIGHT","64px")}},My={chunkSizeWarning:Oe("VITE_CHUNK_SIZE_WARNING",5e5),bundleSizeWarning:Oe("VITE_BUNDLE_SIZE_WARNING",2e6),enableLazyLoading:ce("VITE_LAZY_LOADING",!0),enableServiceWorker:ce("VITE_SERVICE_WORKER",!0),enableCompression:ce("VITE_COMPRESSION",!0),defaultPageSize:Oe("VITE_DEFAULT_PAGE_SIZE",20),maxPageSize:Oe("VITE_MAX_PAGE_SIZE",100),queryTimeout:Oe("VITE_QUERY_TIMEOUT",1e4)},Oy={enableAdvancedCharts:ce("VITE_FEATURE_ADVANCED_CHARTS",!0),enableRealTimeUpdates:ce("VITE_FEATURE_REALTIME_UPDATES",!1),enableOfflineMode:ce("VITE_FEATURE_OFFLINE_MODE",!1),enablePWA:ce("VITE_FEATURE_PWA",!0),enableAnalytics:ce("VITE_FEATURE_ANALYTICS",!0),enableErrorTracking:ce("VITE_FEATURE_ERROR_TRACKING",!0),enablePerformanceTracking:ce("VITE_FEATURE_PERFORMANCE_TRACKING",!0),enableUserTracking:ce("VITE_FEATURE_USER_TRACKING",!1),enableDarkMode:ce("VITE_FEATURE_DARK_MODE",!0),enableAnimations:ce("VITE_FEATURE_ANIMATIONS",!0),enableNotifications:ce("VITE_FEATURE_NOTIFICATIONS",!0),enableKeyboardShortcuts:ce("VITE_FEATURE_KEYBOARD_SHORTCUTS",!0)},Uy={enableCSP:ce("VITE_SECURITY_CSP",!0),enableHTTPS:ce("VITE_SECURITY_HTTPS",!0),enableHSTS:ce("VITE_SECURITY_HSTS",!0),enableXSSProtection:ce("VITE_SECURITY_XSS_PROTECTION",!0),cspDirectives:{defaultSrc:Q("VITE_CSP_DEFAULT_SRC","'self'"),scriptSrc:Q("VITE_CSP_SCRIPT_SRC","'self' 'unsafe-inline' 'unsafe-eval'"),styleSrc:Q("VITE_CSP_STYLE_SRC","'self' 'unsafe-inline'"),imgSrc:Q("VITE_CSP_IMG_SRC","'self' data: https:"),connectSrc:Q("VITE_CSP_CONNECT_SRC","'self' https://learn.reboot01.com")}},zy={name:Q("VITE_APP_NAME","Student Dashboard"),shortName:Q("VITE_APP_SHORT_NAME","Dashboard"),description:Q("VITE_APP_DESCRIPTION","Professional student analytics dashboard"),version:Q("VITE_APP_VERSION","1.0.0"),startUrl:Q("VITE_APP_START_URL","/"),display:Q("VITE_APP_DISPLAY","standalone"),backgroundColor:Q("VITE_APP_BACKGROUND_COLOR","#0f172a"),themeColor:Q("VITE_APP_THEME_COLOR","#14b8a6"),supportEmail:Q("VITE_SUPPORT_EMAIL","<EMAIL>"),documentationUrl:Q("VITE_DOCS_URL","https://docs.example.com")},Cy={providers:{backblaze:{baseUrl:Q("VITE_AVATAR_BACKBLAZE_URL","https://f002.backblazeb2.com/file/01-edu-system"),apiUrl:Q("VITE_AVATAR_BACKBLAZE_API","https://f002.backblazeb2.com/b2api/v1/b2_download_file_by_id")},github:{baseUrl:Q("VITE_AVATAR_GITHUB_URL","https://github.com"),size:Oe("VITE_AVATAR_GITHUB_SIZE",128)},gravatar:{baseUrl:Q("VITE_AVATAR_GRAVATAR_URL","https://www.gravatar.com/avatar"),defaultImage:Q("VITE_AVATAR_GRAVATAR_DEFAULT","identicon")}},enableFallbacks:ce("VITE_AVATAR_FALLBACKS",!0),fallbackToInitials:ce("VITE_AVATAR_FALLBACK_INITIALS",!0),fallbackToGithub:ce("VITE_AVATAR_FALLBACK_GITHUB",!0)},Ly={enableDebugLogs:ce("VITE_DEBUG_LOGS",!1),enablePerformanceLogs:ce("VITE_DEBUG_PERFORMANCE",!1),enableGraphQLLogs:ce("VITE_DEBUG_GRAPHQL",!1),enableMockData:ce("VITE_MOCK_DATA",!1),mockDelay:Oe("VITE_MOCK_DELAY",1e3),enableHotReload:ce("VITE_HOT_RELOAD",!1)},Hy=()=>{const u="production";return{environment:u,isDevelopment:u==="development",isProduction:u==="production",isStaging:u==="staging",api:jy,auth:wy,cache:Dy,ui:Ry,performance:My,features:Oy,security:Uy,app:zy,avatar:Cy,dev:Ly}},Su=Hy(),qy=()=>({token:Kh(),user:Zy()}),By=Su.api.authEndpoint,Gy=Su.api.graphqlEndpoint,Eu=Su.auth.tokenKey,Vy=Su.auth.userKey,Yy=(u,f)=>{const r=`${u}:${f}`;return btoa(r)},Xy=async(u,f)=>{try{const r=Yy(u,f),c=await fetch(By,{method:"POST",headers:{Authorization:`Basic ${r}`,"Content-Type":"application/json"}});if(!c.ok)throw c.status===401?new Error("Invalid credentials. Please check your username/email and password."):c.status===403?new Error("Access forbidden. Please contact support."):c.status>=500?new Error("Server error. Please try again later."):new Error(`Authentication failed: ${c.statusText}`);const d=await c.text();if(!d||d.trim()==="")throw new Error("No token received from server");const m=d.trim().replace(/^["']|["']$/g,"");if(!m.includes(".")||m.split(".").length!==3)throw console.error("Invalid token format. Token:",m.substring(0,50)+"..."),new Error("Invalid token format received from server");if(!cr(m))throw console.error("Token failed format validation"),new Error("Token format validation failed");const y=Qh(m),v={id:parseInt(y.sub,10),username:null,email:null,exp:y.exp,iat:y.iat};return{success:!0,token:m,user:v}}catch(r){throw r.name==="InvalidTokenError"?new Error("Invalid token received from server"):r}},Kh=()=>{const u=localStorage.getItem(Eu);if(!u)return null;try{return JSON.parse(u).state?.token??null}catch{return null}},Zy=()=>{const u=localStorage.getItem(Eu);if(!u)return null;try{return JSON.parse(u).state?.user??null}catch(f){return console.warn("Error parsing persisted user data:",f),null}},cr=u=>{if(!u||typeof u!="string")return!1;const f=u.split(".");if(f.length!==3)return!1;try{return f.forEach(r=>{if(!r)throw new Error("Empty part");if(!/^[A-Za-z0-9_-]+$/.test(r))throw new Error("Invalid characters")}),!0}catch{return!1}},Qy=u=>{try{if(!cr(u))return!0;const f=Qh(u),r=Date.now()/1e3;return f.exp<r}catch(f){return console.warn("Token validation error:",f.message),!0}},hu=()=>{localStorage.removeItem(Eu),localStorage.removeItem(Vy)},Ky=()=>{const u=Kh();return!u||!cr(u)||Qy(u)?(u&&(console.warn("Clearing invalid or expired token"),hu()),{}):{Authorization:`Bearer ${u}`}},$y=wg({uri:Gy}),Iy=Ny((u,{headers:f})=>{const r=Ky();return{headers:{...f,...r,"Content-Type":"application/json"}}}),ky=Zh(({graphQLErrors:u,networkError:f})=>{if(u&&u.forEach(({message:r,locations:c,path:d,extensions:m})=>{if(console.error(`[GraphQL error]: Message: ${r}, Location: ${c}, Path: ${d}`),r.includes("JWT")||r.includes("JWS")||r.includes("verify")){console.warn("JWT verification error detected, clearing auth data"),hu();return}if(m?.code==="UNAUTHENTICATED"||m?.code==="FORBIDDEN"){console.warn("Authentication error detected, clearing auth data"),hu();return}}),f){console.error(`[Network error]: ${f}`);const r=f?.statusCode;if(r===401||r===403){console.warn("Network authentication error, clearing auth data"),hu();return}}}),Jy=new Dg({typePolicies:{Query:{fields:{user:{merge(u,f){return f}},transaction:{merge(u,f){return f}},audit:{merge(u,f){return f}},progress:{merge(u,f){return f}},result:{merge(u,f){return f}}}},User:{fields:{login:{read(u){return u}}}}},resultCaching:!0,dataIdFromObject:u=>u.__typename&&u.id?`${u.__typename}:${u.id}`:u.__typename&&u.login?`${u.__typename}:${u.login}`:null}),$h=new Rg({link:Mg([ky,Iy,$y]),cache:Jy,defaultOptions:{watchQuery:{errorPolicy:"all",fetchPolicy:"cache-first",notifyOnNetworkStatusChange:!0},query:{errorPolicy:"all",fetchPolicy:"cache-first"}},connectToDevTools:!1}),xh=u=>{let f;const r=new Set,c=(x,E)=>{const M=typeof x=="function"?x(f):x;if(!Object.is(M,f)){const T=f;f=E??(typeof M!="object"||M===null)?M:Object.assign({},f,M),r.forEach(q=>q(f,T))}},d=()=>f,v={setState:c,getState:d,getInitialState:()=>p,subscribe:x=>(r.add(x),()=>r.delete(x))},p=f=u(c,d,v);return v},Wy=u=>u?xh(u):xh,Py=u=>u;function Fy(u,f=Py){const r=mu.useSyncExternalStore(u.subscribe,()=>f(u.getState()),()=>f(u.getInitialState()));return mu.useDebugValue(r),r}const ex=u=>{const f=Wy(u),r=c=>Fy(f,c);return Object.assign(r,f),r},rr=u=>ex,vh={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_DEBUG_MODE:"true",VITE_SHOW_DEV_TOOLS:"true",VITE_USE_TEST_AUTH:"true"},Yn=new Map,ou=u=>{const f=Yn.get(u);return f?Object.fromEntries(Object.entries(f.stores).map(([r,c])=>[r,c.getState()])):{}},tx=(u,f,r)=>{if(u===void 0)return{type:"untracked",connection:f.connect(r)};const c=Yn.get(r.name);if(c)return{type:"tracked",store:u,...c};const d={connection:f.connect(r),stores:{}};return Yn.set(r.name,d),{type:"tracked",store:u,...d}},lx=(u,f)=>{if(f===void 0)return;const r=Yn.get(u);r&&(delete r.stores[f],Object.keys(r.stores).length===0&&Yn.delete(u))},ax=u=>{var f,r;if(!u)return;const c=u.split(`
`),d=c.findIndex(y=>y.includes("api.setState"));if(d<0)return;const m=((f=c[d+1])==null?void 0:f.trim())||"";return(r=/.+ (.+) .+/.exec(m))==null?void 0:r[1]},nx=(u,f={})=>(r,c,d)=>{const{enabled:m,anonymousActionType:y,store:v,...p}=f;let x;try{x=(m??(vh?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!x)return u(r,c,d);const{connection:E,...M}=tx(v,x,p);let T=!0;d.setState=(D,L,U)=>{const V=r(D,L);if(!T)return V;const B=U===void 0?{type:y||ax(new Error().stack)||"anonymous"}:typeof U=="string"?{type:U}:U;return v===void 0?(E?.send(B,c()),V):(E?.send({...B,type:`${v}/${B.type}`},{...ou(p.name),[v]:d.getState()}),V)},d.devtools={cleanup:()=>{E&&typeof E.unsubscribe=="function"&&E.unsubscribe(),lx(p.name,v)}};const q=(...D)=>{const L=T;T=!1,r(...D),T=L},R=u(d.setState,c,d);if(M.type==="untracked"?E?.init(R):(M.stores[M.store]=d,E?.init(Object.fromEntries(Object.entries(M.stores).map(([D,L])=>[D,D===M.store?R:L.getState()])))),d.dispatchFromDevtools&&typeof d.dispatch=="function"){let D=!1;const L=d.dispatch;d.dispatch=(...U)=>{(vh?"production":void 0)!=="production"&&U[0].type==="__setState"&&!D&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),D=!0),L(...U)}}return E.subscribe(D=>{var L;switch(D.type){case"ACTION":if(typeof D.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Qc(D.payload,U=>{if(U.type==="__setState"){if(v===void 0){q(U.state);return}Object.keys(U.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const V=U.state[v];if(V==null)return;JSON.stringify(d.getState())!==JSON.stringify(V)&&q(V);return}d.dispatchFromDevtools&&typeof d.dispatch=="function"&&d.dispatch(U)});case"DISPATCH":switch(D.payload.type){case"RESET":return q(R),v===void 0?E?.init(d.getState()):E?.init(ou(p.name));case"COMMIT":if(v===void 0){E?.init(d.getState());return}return E?.init(ou(p.name));case"ROLLBACK":return Qc(D.state,U=>{if(v===void 0){q(U),E?.init(d.getState());return}q(U[v]),E?.init(ou(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Qc(D.state,U=>{if(v===void 0){q(U);return}JSON.stringify(d.getState())!==JSON.stringify(U[v])&&q(U[v])});case"IMPORT_STATE":{const{nextLiftedState:U}=D.payload,V=(L=U.computedStates.slice(-1)[0])==null?void 0:L.state;if(!V)return;q(v===void 0?V:V[v]),E?.send(null,U);return}case"PAUSE_RECORDING":return T=!T}return}}),R},Ih=nx,Qc=(u,f)=>{let r;try{r=JSON.parse(u)}catch(c){console.error("[zustand devtools middleware] Could not parse the received json",c)}r!==void 0&&f(r)};function kh(u,f){let r;try{r=u()}catch{return}return{getItem:d=>{var m;const y=p=>p===null?null:JSON.parse(p,void 0),v=(m=r.getItem(d))!=null?m:null;return v instanceof Promise?v.then(y):y(v)},setItem:(d,m)=>r.setItem(d,JSON.stringify(m,void 0)),removeItem:d=>r.removeItem(d)}}const Pc=u=>f=>{try{const r=u(f);return r instanceof Promise?r:{then(c){return Pc(c)(r)},catch(c){return this}}}catch(r){return{then(c){return this},catch(c){return Pc(c)(r)}}}},ix=(u,f)=>(r,c,d)=>{let m={storage:kh(()=>localStorage),partialize:D=>D,version:0,merge:(D,L)=>({...L,...D}),...f},y=!1;const v=new Set,p=new Set;let x=m.storage;if(!x)return u((...D)=>{console.warn(`[zustand persist middleware] Unable to update item '${m.name}', the given storage is currently unavailable.`),r(...D)},c,d);const E=()=>{const D=m.partialize({...c()});return x.setItem(m.name,{state:D,version:m.version})},M=d.setState;d.setState=(D,L)=>{M(D,L),E()};const T=u((...D)=>{r(...D),E()},c,d);d.getInitialState=()=>T;let q;const R=()=>{var D,L;if(!x)return;y=!1,v.forEach(V=>{var B;return V((B=c())!=null?B:T)});const U=((L=m.onRehydrateStorage)==null?void 0:L.call(m,(D=c())!=null?D:T))||void 0;return Pc(x.getItem.bind(x))(m.name).then(V=>{if(V)if(typeof V.version=="number"&&V.version!==m.version){if(m.migrate){const B=m.migrate(V.state,V.version);return B instanceof Promise?B.then(X=>[!0,X]):[!0,B]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,V.state];return[!1,void 0]}).then(V=>{var B;const[X,W]=V;if(q=m.merge(W,(B=c())!=null?B:T),r(q,!0),X)return E()}).then(()=>{U?.(q,void 0),q=c(),y=!0,p.forEach(V=>V(q))}).catch(V=>{U?.(void 0,V)})};return d.persist={setOptions:D=>{m={...m,...D},D.storage&&(x=D.storage)},clearStorage:()=>{x?.removeItem(m.name)},getOptions:()=>m,rehydrate:()=>R(),hasHydrated:()=>y,onHydrate:D=>(v.add(D),()=>{v.delete(D)}),onFinishHydration:D=>(p.add(D),()=>{p.delete(D)})},m.skipHydration||R(),q||T},ux=ix,Wt=rr()(ux((u,f)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:(r,c)=>{u({user:r,token:c,isAuthenticated:!0,error:null,isLoading:!1})},logout:()=>{u({user:null,token:null,isAuthenticated:!1,error:null,isLoading:!1})},setLoading:r=>{u({isLoading:r})},setError:r=>{u({error:r,isLoading:!1})},clearError:()=>{u({error:null})},updateUser:r=>{const c=f().user;c&&u({user:{...c,...r}})}}),{name:Eu,storage:kh(()=>localStorage),partialize:u=>({user:u.user,token:u.token,isAuthenticated:u.isAuthenticated})})),sx=()=>Wt(u=>u.user),_u=()=>Wt(u=>u.isAuthenticated),cx=()=>Wt(u=>u.isLoading),rx=()=>Wt(u=>u.error),Jh=()=>Wt(u=>u.login),ox=()=>Wt(u=>u.logout),Wh=()=>Wt(u=>u.setLoading),fx=()=>Wt(u=>u.setError),dx=()=>Wt(u=>u.clearError),hx={xpTransactions:[],progress:[],audits:[],skills:[],leaderboard:[],lastUpdated:null},mx={theme:"dark",compactMode:!1,showAnimations:!0,defaultTab:"dashboard"},yu=rr()(Ih((u,f)=>({data:hx,activeTab:"dashboard",isLoading:!1,error:null,preferences:mx,setData:r=>{u(c=>({data:{...c.data,...r,lastUpdated:new Date},error:null}))},setActiveTab:r=>{u({activeTab:r})},setLoading:r=>{u({isLoading:r})},setError:r=>{u({error:r,isLoading:!1})},clearError:()=>{u({error:null})},updatePreferences:r=>{u(d=>({preferences:{...d.preferences,...r}}));const c={...f().preferences,...r};localStorage.setItem("dashboard-preferences",JSON.stringify(c))},refreshData:()=>{u({isLoading:!0,error:null})}}),{name:"dashboard-store"})),gx=()=>yu(u=>u.activeTab),px=()=>yu(u=>u.setActiveTab);if(typeof window<"u"){const u=localStorage.getItem("dashboard-preferences");if(u)try{const f=JSON.parse(u),r=yu.getState();yu.setState({preferences:{...r.preferences,...f}})}catch(f){console.warn("Failed to parse saved preferences:",f)}}const yx={user:null,xpTransactions:[],progress:[],audits:[],skills:[],stats:null},or=rr()(Ih((u,f)=>({profiles:new Map,currentProfileId:null,isLoading:!1,error:null,setProfile:(r,c)=>{u(d=>{const m=new Map(d.profiles),y=m.get(r)||{...yx};return m.set(r,{...y,...c}),{profiles:m,error:null}})},setCurrentProfile:r=>{u({currentProfileId:r})},setLoading:r=>{u({isLoading:r})},setError:r=>{u({error:r,isLoading:!1})},clearError:()=>{u({error:null})},clearProfile:r=>{u(c=>{const d=new Map(c.profiles);return d.delete(r),{profiles:d,currentProfileId:c.currentProfileId===r?null:c.currentProfileId}})},clearAllProfiles:()=>{u({profiles:new Map,currentProfileId:null,error:null})}}),{name:"user-profile-store"})),xx=()=>or(u=>({setProfile:u.setProfile,setCurrentProfile:u.setCurrentProfile,setLoading:u.setLoading,setError:u.setError,clearError:u.clearError,clearProfile:u.clearProfile,clearAllProfiles:u.clearAllProfiles})),vx=()=>or(u=>({isLoading:u.isLoading,error:u.error})),bx=u=>{const f=or(d=>u?d.profiles.get(u):null),{isLoading:r,error:c}=vx();return{profile:f,isLoading:r,error:c,hasData:f?.user!==null}},Sx=()=>{const[u,f]=w.useState(""),[r,c]=w.useState(""),[d,m]=w.useState(!1),y=_u(),v=Jh(),p=Wh(),x=fx(),E=dx(),M=cx(),T=rx();if(y)return s.jsx(Ua,{to:"/dashboard",replace:!0});const q=u.includes("@"),R=async D=>{if(D.preventDefault(),!u.trim()||!r.trim()){x("Please fill in all fields");return}E(),p(!0);try{const L=await Xy(u,r);if(L.success&&L.user&&L.token)v(L.user,L.token);else throw new Error("Failed to fetch user data")}catch(L){x(L instanceof Error?L.message:"Authentication failed. Please check your credentials.")}finally{p(!1)}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center p-4",children:s.jsxs($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx($.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200,delay:.1},className:"w-20 h-20 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:s.jsx(uh,{className:"w-10 h-10 text-white"})}),s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome Back"}),s.jsx("p",{className:"text-white/70",children:"Sign in to your Reboot01 account"})]}),s.jsx($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-xl border border-white/20",children:s.jsxs("form",{onSubmit:R,className:"space-y-6",children:[T&&s.jsxs($.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4 flex items-center space-x-3",children:[s.jsx(Og,{className:"w-5 h-5 text-red-400 flex-shrink-0"}),s.jsx("p",{className:"text-red-200 text-sm",children:T})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"identifier",className:"block text-sm font-medium text-white/80",children:q?"Email Address":"Login"}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx($c,{className:"h-5 w-5 text-white/40"})}),s.jsx("input",{id:"identifier",type:"text",value:u,onChange:D=>f(D.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all",placeholder:"Enter your email or login",disabled:M})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-white/80",children:"Password"}),s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(Ug,{className:"h-5 w-5 text-white/40"})}),s.jsx("input",{id:"password",type:d?"text":"password",value:r,onChange:D=>c(D.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all",placeholder:"Enter your password",disabled:M}),s.jsx("button",{type:"button",onClick:()=>m(!d),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-white/40 hover:text-white/60 transition-colors",disabled:M,children:d?s.jsx(zg,{className:"h-5 w-5"}):s.jsx(Cg,{className:"h-5 w-5"})})]})]}),s.jsx($.button,{type:"submit",disabled:M,whileHover:{scale:M?1:1.02},whileTap:{scale:M?1:.98},className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed shadow-lg",children:M?s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),s.jsx("span",{children:"Signing in..."})]}):s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(uh,{className:"w-5 h-5"}),s.jsx("span",{children:"Sign In"})]})})]})}),s.jsx($.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.4},className:"text-center mt-8",children:s.jsx("p",{className:"text-white/50 text-sm",children:"Reboot01 Student Dashboard"})})]})})},Ex="modulepreload",_x=function(u){return"/"+u},bh={},Zl=function(f,r,c){let d=Promise.resolve();if(r&&r.length>0){let p=function(x){return Promise.all(x.map(E=>Promise.resolve(E).then(M=>({status:"fulfilled",value:M}),M=>({status:"rejected",reason:M}))))};document.getElementsByTagName("link");const y=document.querySelector("meta[property=csp-nonce]"),v=y?.nonce||y?.getAttribute("nonce");d=p(r.map(x=>{if(x=_x(x),x in bh)return;bh[x]=!0;const E=x.endsWith(".css"),M=E?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${x}"]${M}`))return;const T=document.createElement("link");if(T.rel=E?"stylesheet":Ex,E||(T.as="script"),T.crossOrigin="",T.href=x,v&&T.setAttribute("nonce",v),document.head.appendChild(T),E)return new Promise((q,R)=>{T.addEventListener("load",q),T.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${x}`)))})}))}function m(y){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=y,window.dispatchEvent(v),!v.defaultPrevented)throw y}return d.then(y=>{for(const v of y||[])v.status==="rejected"&&m(v.reason);return f().catch(m)})},vt=({size:u="md",className:f="",text:r="Loading..."})=>{const c={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"},d={sm:"text-sm",md:"text-base",lg:"text-lg"};return s.jsxs("div",{className:`flex flex-col items-center justify-center p-8 ${f}`,children:[s.jsx($.div,{className:`${c[u]} border-4 border-primary-500 border-t-transparent rounded-full`,animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),r&&s.jsx($.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:`mt-4 text-white/70 ${d[u]}`,children:r})]})},Sh=w.lazy(()=>Zl(()=>import("./MainDashboard-DY2OdiLg.js"),__vite__mapDeps([0,1,2,3,4]))),Nx=w.lazy(()=>Zl(()=>import("./LeaderboardSection-p9OipZjv.js"),__vite__mapDeps([5,1,2,3,4]))),Tx=w.lazy(()=>Zl(()=>import("./SearchSection-C3K7A1kI.js"),__vite__mapDeps([6,1,2,4,3]))),Ax=w.lazy(()=>Zl(()=>import("./ExportSection-rRXF8O1w.js"),__vite__mapDeps([7,1,2,3,4]))),jx=w.lazy(()=>Zl(()=>import("./PiscineSection-2jx3wjQL.js"),__vite__mapDeps([8,1,2,3,4]))),wx=w.lazy(()=>Zl(()=>import("./NotificationSystem-BGD0Bgny.js"),__vite__mapDeps([9,1,2,3]))),Dx=w.lazy(()=>Zl(()=>import("./UserPreferences-eeJ58MU7.js"),__vite__mapDeps([10,1,2,3,4]))),Rx=()=>{const u=sx(),f=ox(),r=gx(),c=px(),[d,m]=w.useState([]),[y,v]=w.useState(!1);if(!u)return null;const{data:p}=_l(ae`
    query GetUserPiscineTypes($userId: Int!) {
      # Standard piscines: /bahrain/bh-module/piscine-{{name}}/
      standard_piscines: transaction(
        where: {
          userId: { _eq: $userId }
          path: { _regex: "bh-module/piscine-" }
        }
        distinct_on: path
      ) {
        path
      }
      # Go piscine: /bahrain/bh-piscine/
      go_piscine: transaction(
        where: {
          userId: { _eq: $userId }
          path: { _regex: "bh-piscine/" }
        }
        limit: 1
      ) {
        path
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"});w.useEffect(()=>{if(p){const R=new Set;p.standard_piscines&&p.standard_piscines.forEach(D=>{const L=D.path?.match(/piscine-(\w+)/);L&&R.add(L[1])}),p.go_piscine&&p.go_piscine.length>0&&R.add("go"),m(Array.from(R))}},[p]);const x=w.useCallback(()=>{f()},[f]),E=[{id:"dashboard",label:"Dashboard",icon:Ic},{id:"leaderboard",label:"Leaderboard",icon:Hg},{id:"search",label:"Search",icon:Ah},{id:"export",label:"Export",icon:qg}],M=d.map(R=>({id:`piscine-${R}`,label:`Piscine ${R.toUpperCase()}`,icon:Lg})),T=w.useMemo(()=>[...E,...M],[M]),q=()=>{if(r.startsWith("piscine-")){const R=r.replace("piscine-","");return s.jsx(jx,{user:u,piscineType:R})}switch(r){case"dashboard":return s.jsx(Sh,{user:u});case"leaderboard":return s.jsx(Nx,{user:u});case"search":return s.jsx(Tx,{user:u});case"export":return s.jsx(Ax,{user:u});default:return s.jsx(Sh,{user:u})}};return s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900",children:[s.jsx("header",{className:"bg-white/10 backdrop-blur-lg border-b border-white/20",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex items-center justify-between h-14 sm:h-16",children:[s.jsxs("div",{className:"flex items-center min-w-0",children:[s.jsx($.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200},className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0",children:s.jsx(xu,{className:"w-4 h-4 sm:w-6 sm:h-6 text-white"})}),s.jsxs("div",{className:"min-w-0",children:[s.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white truncate",children:"Student Dashboard"}),s.jsxs("p",{className:"text-xs sm:text-sm text-white/70 truncate",children:["Welcome back, ",u.login]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[s.jsx(w.Suspense,{fallback:s.jsx("div",{className:"w-6 h-6"}),children:s.jsx(wx,{userId:u.id})}),s.jsx($.button,{onClick:()=>v(!0),whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors","aria-label":"Open preferences",title:"Preferences",children:s.jsx(Bg,{className:"w-5 h-5"})}),s.jsxs($.button,{onClick:x,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center px-3 sm:px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-200 rounded-lg transition-colors","aria-label":"Logout from dashboard",title:"Logout",children:[s.jsx(Gg,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),s.jsx("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})})}),s.jsx("nav",{className:"bg-white/5 backdrop-blur-sm border-b border-white/10",role:"navigation","aria-label":"Dashboard navigation",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex space-x-2 sm:space-x-4 lg:space-x-8 overflow-x-auto scrollbar-hide",role:"tablist",children:T.map(R=>{const D=R.icon,L=r===R.id;return s.jsxs($.button,{onClick:()=>c(R.id),whileHover:{y:-2},whileTap:{y:0},className:`flex items-center px-3 sm:px-4 py-3 sm:py-4 text-xs sm:text-sm font-medium border-b-2 transition-all whitespace-nowrap min-w-0 ${L?"border-primary-500 text-primary-400":"border-transparent text-white/70 hover:text-white hover:border-white/30"}`,"aria-label":`Switch to ${R.label} tab`,"aria-current":L?"page":void 0,role:"tab",tabIndex:0,children:[s.jsx(D,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 flex-shrink-0"}),s.jsx("span",{className:"hidden sm:inline",children:R.label}),s.jsx("span",{className:"sm:hidden",children:R.label.split(" ")[0]})]},R.id)})})})}),s.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",role:"main","aria-label":"Dashboard content",children:s.jsx($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:q()})},r)}),y&&s.jsx(w.Suspense,{fallback:null,children:s.jsx(Dx,{userId:u.id,onClose:()=>v(!1)})})]})},Mx=()=>_u()?s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:s.jsx(Rx,{})}):s.jsx(Ua,{to:"/login",replace:!0}),Ha=ae`
  fragment UserBasic on user {
    id
    login
    firstName
    lastName
    email
    campus
    createdAt
    updatedAt
  }
`,Qn=ae`
  fragment UserAudit on user {
    auditRatio
    totalUp
    totalDown
  }
`;ae`
  ${Ha}
  ${Qn}
  
  query GetUserProfile($userId: Int!) {
    user(where: { id: { _eq: $userId } }) {
      ...UserBasic
      ...UserAudit
    }
  }
`;const uv=ae`
  ${Ha}
  
  query SearchUsers(
    $searchTerm: String!
    $limit: Int = 20
    $offset: Int = 0
  ) {
    user(
      where: {
        _or: [
          { login: { _ilike: $searchTerm } }
          { firstName: { _ilike: $searchTerm } }
          { lastName: { _ilike: $searchTerm } }
          { email: { _ilike: $searchTerm } }
        ]
      }
      limit: $limit
      offset: $offset
      order_by: { login: asc }
    ) {
      ...UserBasic
    }
    
    user_aggregate(
      where: {
        _or: [
          { login: { _ilike: $searchTerm } }
          { firstName: { _ilike: $searchTerm } }
          { lastName: { _ilike: $searchTerm } }
          { email: { _ilike: $searchTerm } }
        ]
      }
    ) {
      aggregate {
        count
      }
    }
  }
`;ae`
  ${Ha}
  ${Qn}
  
  query GetUserByLogin($login: String!) {
    user(where: { login: { _eq: $login } }) {
      ...UserBasic
      ...UserAudit
    }
  }
`;ae`
  ${Ha}
  ${Qn}
  
  query GetUserByEmail($email: String!) {
    user(where: { email: { _eq: $email } }) {
      ...UserBasic
      ...UserAudit
    }
  }
`;const Ox=ae`
  ${Ha}
  ${Qn}

  query GetUserById($userId: Int!) {
    user(where: { id: { _eq: $userId } }) {
      ...UserBasic
      ...UserAudit
      attrs
    }
  }
`;ae`
  ${Ha}
  ${Qn}

  query GetUsersByIds($userIds: [Int!]!) {
    user(where: { id: { _in: $userIds } }) {
      ...UserBasic
      ...UserAudit
    }
  }
`;const Nu=ae`
  fragment TransactionData on transaction {
    id
    amount
    type
    createdAt
    path
    object {
      id
      name
      type
    }
  }
`;ae`
  ${Nu}
  
  query GetUserXPTransactions(
    $userId: Int!
    $limit: Int = 50
    $offset: Int = 0
  ) {
    transaction(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
      order_by: { createdAt: desc }
      limit: $limit
      offset: $offset
    ) {
      ...TransactionData
    }
    
    transaction_aggregate(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
    ) {
      aggregate {
        count
        sum {
          amount
        }
      }
    }
  }
`;ae`
  ${Nu}
  
  query GetUserPiscineXP(
    $userId: Int!
    $piscineType: String!
    $limit: Int = 50
  ) {
    transaction(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _regex: $piscineType }
      }
      order_by: { createdAt: desc }
      limit: $limit
    ) {
      ...TransactionData
    }
    
    transaction_aggregate(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _regex: $piscineType }
      }
    ) {
      aggregate {
        count
        sum {
          amount
        }
      }
    }
  }
`;ae`
  ${Nu}
  
  query GetAllUserXP($userId: Int!) {
    # BH Module XP
    bhModule: transaction(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
      order_by: { createdAt: desc }
    ) {
      ...TransactionData
    }
    
    # Piscine XP
    piscines: transaction(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _regex: "piscine" }
      }
      order_by: { createdAt: desc }
    ) {
      ...TransactionData
    }
    
    # Total aggregates
    bhModuleAggregate: transaction_aggregate(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
    ) {
      aggregate {
        count
        sum {
          amount
        }
      }
    }
    
    piscineAggregate: transaction_aggregate(
      where: {
        userId: { _eq: $userId }
        type: { _eq: "xp" }
        path: { _regex: "piscine" }
      }
    ) {
      aggregate {
        count
        sum {
          amount
        }
      }
    }
  }
`;ae`
  query GetXPLeaderboard($limit: Int = 50) {
    transaction_aggregate(
      where: {
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
      group_by: [userId]
      order_by: { sum: { amount: desc } }
      limit: $limit
    ) {
      aggregate {
        sum {
          amount
        }
      }
      group_key
    }
  }
`;ae`
  ${Nu}
  
  query GetRecentXPActivity($limit: Int = 20) {
    transaction(
      where: {
        type: { _eq: "xp" }
        path: { _nregex: "piscine" }
      }
      order_by: { createdAt: desc }
      limit: $limit
    ) {
      ...TransactionData
      user {
        id
        login
        firstName
        lastName
      }
    }
  }
`;const fr=ae`
  fragment ProgressData on progress {
    id
    grade
    isDone
    path
    version
    createdAt
    updatedAt
    object {
      id
      name
      type
    }
  }
`,sv=ae`
  ${fr}
  
  query GetUserProgress(
    $userId: Int!
    $limit: Int = 100
    $offset: Int = 0
  ) {
    progress(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
      }
      order_by: { updatedAt: desc }
      limit: $limit
      offset: $offset
    ) {
      ...ProgressData
    }
    
    progress_aggregate(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
      }
    ) {
      aggregate {
        count
      }
    }
  }
`;ae`
  ${fr}
  
  query GetUserPiscineProgress(
    $userId: Int!
    $piscineType: String!
    $limit: Int = 100
  ) {
    progress(
      where: {
        userId: { _eq: $userId }
        path: { _regex: $piscineType }
      }
      order_by: { updatedAt: desc }
      limit: $limit
    ) {
      ...ProgressData
    }
    
    progress_aggregate(
      where: {
        userId: { _eq: $userId }
        path: { _regex: $piscineType }
      }
    ) {
      aggregate {
        count
      }
    }
  }
`;ae`
  query GetUserProjectStats($userId: Int!) {
    # BH Module stats
    bhModulePassed: progress_aggregate(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
        isDone: { _eq: true }
        grade: { _gte: 1 }
      }
    ) {
      aggregate {
        count
      }
    }
    
    bhModuleFailed: progress_aggregate(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
        isDone: { _eq: true }
        grade: { _lt: 1 }
      }
    ) {
      aggregate {
        count
      }
    }
    
    bhModuleTotal: progress_aggregate(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
        isDone: { _eq: true }
      }
    ) {
      aggregate {
        count
      }
    }
    
    # Recent completions
    recentCompletions: progress(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
        isDone: { _eq: true }
        grade: { _gte: 1 }
      }
      order_by: { updatedAt: desc }
      limit: 10
    ) {
      ...ProgressData
    }
  }
`;ae`
  query GetProjectDetails($path: String!) {
    object(where: { path: { _eq: $path } }) {
      id
      name
      type
      path
      createdAt
      updatedAt
    }
    
    # Get completion stats for this project
    progress_aggregate(
      where: {
        path: { _eq: $path }
        isDone: { _eq: true }
      }
    ) {
      aggregate {
        count
      }
    }
    
    # Get pass rate
    passedCount: progress_aggregate(
      where: {
        path: { _eq: $path }
        isDone: { _eq: true }
        grade: { _gte: 1 }
      }
    ) {
      aggregate {
        count
      }
    }
  }
`;ae`
  ${fr}
  
  query GetAllUserProgress($userId: Int!) {
    # BH Module progress
    bhModule: progress(
      where: {
        userId: { _eq: $userId }
        path: { _nregex: "piscine" }
      }
      order_by: { updatedAt: desc }
    ) {
      ...ProgressData
    }
    
    # Piscine progress
    piscines: progress(
      where: {
        userId: { _eq: $userId }
        path: { _regex: "piscine" }
      }
      order_by: { updatedAt: desc }
    ) {
      ...ProgressData
    }
  }
`;const Ph=ae`
  fragment AuditData on audit {
    id
    grade
    createdAt
    updatedAt
  }
`;ae`
  ${Ph}
  
  query GetAuditsGiven(
    $userId: Int!
    $limit: Int = 50
    $offset: Int = 0
  ) {
    audit(
      where: { auditorId: { _eq: $userId } }
      order_by: { createdAt: desc }
      limit: $limit
      offset: $offset
    ) {
      ...AuditData
      group {
        id
        path
        object {
          id
          name
          type
        }
      }
    }
    
    audit_aggregate(
      where: { auditorId: { _eq: $userId } }
    ) {
      aggregate {
        count
        sum {
          grade
        }
        avg {
          grade
        }
      }
    }
  }
`;ae`
  ${Ph}
  
  query GetAuditsReceived(
    $userId: Int!
    $limit: Int = 50
    $offset: Int = 0
  ) {
    audit(
      where: {
        group: {
          group_users: {
            userId: { _eq: $userId }
          }
        }
      }
      order_by: { createdAt: desc }
      limit: $limit
      offset: $offset
    ) {
      ...AuditData
      auditor {
        id
        login
        firstName
        lastName
      }
      group {
        id
        path
        object {
          id
          name
          type
        }
      }
    }
    
    audit_aggregate(
      where: {
        group: {
          group_users: {
            userId: { _eq: $userId }
          }
        }
      }
    ) {
      aggregate {
        count
        sum {
          grade
        }
        avg {
          grade
        }
      }
    }
  }
`;ae`
  query GetUserAuditStats($userId: Int!) {
    # Audits given stats
    auditsGiven: audit_aggregate(
      where: { auditorId: { _eq: $userId } }
    ) {
      aggregate {
        count
        sum {
          grade
        }
        avg {
          grade
        }
      }
    }
    
    # Audits received stats
    auditsReceived: audit_aggregate(
      where: {
        group: {
          group_users: {
            userId: { _eq: $userId }
          }
        }
      }
    ) {
      aggregate {
        count
        sum {
          grade
        }
        avg {
          grade
        }
      }
    }
    
    # Recent audits given
    recentAuditsGiven: audit(
      where: { auditorId: { _eq: $userId } }
      order_by: { createdAt: desc }
      limit: 10
    ) {
      ...AuditData
      group {
        id
        path
        object {
          name
          type
        }
        group_users {
          user {
            id
            login
            firstName
            lastName
          }
        }
      }
    }
    
    # Recent audits received
    recentAuditsReceived: audit(
      where: {
        group: {
          group_users: {
            userId: { _eq: $userId }
          }
        }
      }
      order_by: { createdAt: desc }
      limit: 10
    ) {
      ...AuditData
      auditor {
        id
        login
        firstName
        lastName
      }
      group {
        id
        path
        object {
          name
          type
        }
      }
    }
  }
`;ae`
  query GetAuditPerformance($userId: Int!, $fromDate: timestamptz!) {
    # Monthly audit activity
    auditsGivenByMonth: audit(
      where: {
        auditorId: { _eq: $userId }
        createdAt: { _gte: $fromDate }
      }
      order_by: { createdAt: asc }
    ) {
      grade
      createdAt
    }
    
    auditsReceivedByMonth: audit(
      where: {
        group: {
          group_users: {
            userId: { _eq: $userId }
          }
        }
        createdAt: { _gte: $fromDate }
      }
      order_by: { createdAt: asc }
    ) {
      grade
      createdAt
    }
  }
`;ae`
  query GetAuditLeaderboard($limit: Int = 50) {
    user(
      where: {
        auditRatio: { _is_null: false }
      }
      order_by: { auditRatio: desc }
      limit: $limit
    ) {
      id
      login
      firstName
      lastName
      auditRatio
      totalUp
      totalDown
    }
  }
`;const Ux=({user:u,size:f="md",className:r="",showBorder:c=!1,animate:d=!0,onClick:m,...y})=>{const[v,p]=w.useState(!1),x={xs:"w-6 h-6",sm:"w-8 h-8",md:"w-12 h-12",lg:"w-16 h-16",xl:"w-24 h-24","2xl":"w-32 h-32"},E={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12","2xl":"w-16 h-16"},M=X=>{if(!X)return null;const W=X.attrs||{};return W.avatar?W.avatar:W.avatarUrl?W.avatarUrl:W.picture?W.picture:X.login?`https://github.com/${X.login}.png`:null},T=X=>X?X.firstName&&X.lastName?`${X.firstName[0]}${X.lastName[0]}`:X.firstName?X.firstName[0]:X.lastName?X.lastName[0]:X.login?X.login[0].toUpperCase():"U":"U",q=M(u),R=T(u),D=q&&!v,U=((X="default")=>{const W=["from-blue-500 to-purple-600","from-green-500 to-teal-600","from-pink-500 to-rose-600","from-yellow-500 to-orange-600","from-indigo-500 to-blue-600","from-purple-500 to-pink-600","from-teal-500 to-green-600","from-orange-500 to-red-600"],xe=X.split("").reduce((ue,_e)=>(ue=(ue<<5)-ue+_e.charCodeAt(0),ue&ue),0);return W[Math.abs(xe)%W.length]})(u?.login),V=`
    ${x[f]}
    rounded-full
    flex items-center justify-center
    overflow-hidden
    ${c?"ring-2 ring-primary-500 ring-offset-2 ring-offset-transparent":""}
    ${m?"cursor-pointer hover:ring-2 hover:ring-primary-400 transition-all":""}
    ${r}
  `,B=s.jsx("div",{className:V,onClick:m,...y,children:D?s.jsx("img",{src:q,alt:`${u?.login||"User"} avatar`,className:"w-full h-full object-cover",onError:()=>p(!0)}):s.jsx("div",{className:`w-full h-full bg-gradient-to-br ${U} flex items-center justify-center`,children:R.length>1?s.jsx("span",{className:`font-semibold text-white ${f==="xs"?"text-xs":f==="sm"?"text-sm":f==="md"?"text-base":f==="lg"?"text-lg":f==="xl"?"text-2xl":"text-3xl"}`,children:R}):s.jsx(xu,{className:`${E[f]} text-white`})})});return d?s.jsx($.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:300,damping:20},children:B}):B},pe=({children:u,className:f="",hover:r=!1,animate:c=!0,onClick:d})=>{const m=`
    bg-white/10 
    backdrop-blur-lg 
    rounded-2xl 
    border 
    border-white/20 
    ${r?"hover:bg-white/15 hover:border-white/30 transition-all duration-200":""}
    ${d?"cursor-pointer":""}
    ${f}
  `,y=s.jsx("div",{className:m,onClick:d,children:u});return c?s.jsx($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},whileHover:r?{y:-2}:void 0,children:y}):y},Kc=u=>!u||isNaN(u)?"0K":`${Math.round(u/1e3)}K`,Eh=u=>!u||isNaN(u)?"0K":`${Math.round(u/1e3)}K`,_h=u=>!u||isNaN(u)?"0.0MB":`${(u/1e6).toFixed(1)}MB`,zx=u=>{if(!u)return"";try{const f=new Date(u);return isNaN(f.getTime())?"":f.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return""}},Cx=u=>u==null||isNaN(u)?"0.0":u.toFixed(1),Lx=u=>{if(!u)return"";try{const f=new Date(u),c=new Date().getTime()-f.getTime(),d=Math.floor(c/(1e3*60*60*24));return d===0?"Today":d===1?"Yesterday":d<7?`${d} days ago`:d<30?`${Math.floor(d/7)} weeks ago`:d<365?`${Math.floor(d/30)} months ago`:`${Math.floor(d/365)} years ago`}catch{return""}},Hx=u=>{if(!u||!Array.isArray(u))return{mainModule:[],piscines:{},allPiscines:[],all:[]};const f=[],r={},c=[];return u.forEach(d=>{if(!d.path){f.push(d);return}if(d.path.includes("/bh-piscine/")){const m="go";r[m]||(r[m]=[]),r[m].push(d),c.push(d)}else if(d.path.includes("/bh-module/piscine-")){const m=d.path.match(/piscine-(\w+)/);if(m){const y=m[1];r[y]||(r[y]=[]),r[y].push(d),c.push(d)}}else f.push(d)}),{mainModule:f,piscines:r,allPiscines:c,all:u}},qx=u=>{if(!u||!Array.isArray(u))return{total:0,bhModule:0,piscines:{},allPiscines:0};const f=u.filter(c=>c.type==="xp"),r={total:0,bhModule:0,piscines:{},allPiscines:0};return f.forEach(c=>{const d=c.amount||0;if(r.total+=d,c.path&&c.path.includes("piscine")){const m=c.path.match(/piscine-(\w+)/);if(m){const y=m[1];r.piscines[y]||(r.piscines[y]=0),r.piscines[y]+=d,r.allPiscines+=d}}else r.bhModule+=d}),r},Bx=u=>!u||u<=0?1:Math.floor(Math.sqrt(u/1e3))+1,cv=u=>{const f={};u.forEach(m=>{f[m.path]||(f[m.path]=[]),f[m.path].push(m)});let r=0,c=0,d=0;return Object.keys(f).forEach(m=>{const v=f[m].reduce((p,x)=>new Date(x.createdAt)>new Date(p.createdAt)?x:p);r++,v.isDone&&v.grade>=1?c++:d++}),{total:r,passed:c,failed:d,passRate:r>0?Math.round(c/r*100):0}},Gx=(u,f=0,r=0)=>u>=2?{notation:"Exceptional Developer",description:"Outstanding audit performance",color:"text-purple-400",badge:"🏆"}:u>=1.8?{notation:"Advanced Developer",description:"Excellent audit skills",color:"text-blue-400",badge:"⭐"}:u>=1.5?{notation:"Skilled Developer",description:"Strong audit performance",color:"text-green-400",badge:"✨"}:u>=1.2?{notation:"Developing Skills",description:"Good audit progress",color:"text-yellow-400",badge:"📈"}:u>=1?{notation:"Meeting Standards",description:"Audit requirements met",color:"text-orange-400",badge:"✅"}:u>=.8?{notation:"Aspiring Developer",description:"Building audit skills",color:"text-orange-500",badge:"🚀"}:{notation:"Needs Work",description:"Focus on audit improvement",color:"text-red-400",badge:"📚"},Vx=u=>u?{personal:{email:u.email||null,phone:u.Phone||u.PhoneNumber||null,dateOfBirth:u.dateOfBirth||null,placeOfBirth:u.placeOfBirth||null,nationality:u.countryOfBirth||u.addressCountry||null,cprNumber:u.CPRnumber||null,gender:u.gender||u.genders||null},address:{street:u.addressStreet||null,complement:u.addressComplementStreet||null,city:u.addressCity||null,country:u.addressCountry||null,postalCode:u.addressPostalCode||null},emergency:{firstName:u.emergencyFirstName||null,lastName:u.emergencyLastName||null,fullName:`${u.emergencyFirstName||""} ${u.emergencyLastName||""}`.trim()||null,phone:u.emergencyTel||null,relation:u.emergencyAffiliation||null},education:{degree:u.Degree||u.schoolanddegree||null,qualification:u.qualification||u.qualifica||null,graduationDate:u.graddate||null},employment:{status:u.employment||null,jobTitle:u.jobtitle||null,other:u.otheremp||null},uploads:{profilePicture:u["pro-picUploadId"]||null,idCard:u["id-cardUploadId"]||null},other:{medicalInfo:u.medicalInfo||null,howDidYouHear:u.howdidyou||null,conditionsAccepted:u["general-conditionsAccepted"]||!1}}:null,Yx=({user:u})=>{const{data:f,loading:r,error:c}=_l(ae(Ox),{variables:{userId:u.id},errorPolicy:"all"});if(r)return s.jsx(vt,{});if(c)return s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"text-center text-red-400",children:[s.jsx("p",{children:"Error loading profile data"}),s.jsx("p",{className:"text-sm text-white/60 mt-2",children:c.message})]})});const d=f?.user?.[0];if(!d)return s.jsx(pe,{className:"p-6",children:s.jsx("div",{className:"text-center text-white/60",children:s.jsx("p",{children:"No profile data available"})})});const m=d.profile||{},y=d.attrs||{},v=Vx(y),p=Gx(d.auditRatio||0);return s.jsxs("div",{className:"space-y-6",children:[s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6",children:[s.jsx($.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200},children:s.jsx(Ux,{user:d,size:"2xl",showBorder:!0,animate:!0})}),s.jsx("div",{className:"flex-1",children:s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},children:[s.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:d.firstName&&d.lastName?`${d.firstName} ${d.lastName}`:d.login}),s.jsxs("p",{className:"text-primary-400 font-medium mb-1",children:["@",d.login]}),s.jsxs("div",{className:`flex items-center mb-2 ${p.color}`,children:[s.jsx("span",{className:"mr-2",children:p.badge}),s.jsx("span",{className:"font-medium",children:p.notation}),s.jsxs("span",{className:"text-white/60 text-sm ml-2",children:["• ",p.description]})]}),d.campus&&s.jsxs("div",{className:"flex items-center text-white/70 mb-2",children:[s.jsx(sh,{className:"w-4 h-4 mr-2"}),s.jsx("span",{className:"capitalize",children:d.campus})]}),s.jsxs("div",{className:"flex items-center text-white/70",children:[s.jsx(Vg,{className:"w-4 h-4 mr-2"}),s.jsxs("span",{children:["Joined ",zx(d.createdAt)]})]})]})}),s.jsxs($.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},className:"grid grid-cols-3 gap-4 text-center",children:[s.jsxs("div",{className:"bg-white/10 rounded-lg p-3",children:[s.jsx("div",{className:"text-2xl font-bold text-primary-400",children:Cx(d.auditRatio)}),s.jsx("div",{className:"text-xs text-white/60",children:"Audit Ratio"})]}),s.jsxs("div",{className:"bg-white/10 rounded-lg p-3",children:[s.jsx("div",{className:"text-2xl font-bold text-green-400",children:_h(d.totalUp)}),s.jsx("div",{className:"text-xs text-white/60",children:"Audits Given"})]}),s.jsxs("div",{className:"bg-white/10 rounded-lg p-3",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-400",children:_h(d.totalDown)}),s.jsx("div",{className:"text-xs text-white/60",children:"Audits Received"})]})]})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx($c,{className:"w-5 h-5 mr-2 text-primary-400"}),"Contact Information"]}),s.jsxs("div",{className:"space-y-3",children:[v?.personal?.email&&s.jsxs("div",{className:"flex items-center text-white/80",children:[s.jsx($c,{className:"w-4 h-4 mr-3 text-white/50"}),s.jsx("span",{children:v.personal.email})]}),v?.personal?.phone&&s.jsxs("div",{className:"flex items-center text-white/80",children:[s.jsx(Yg,{className:"w-4 h-4 mr-3 text-white/50"}),s.jsx("span",{children:v.personal.phone})]}),v?.address?.city&&v?.address?.country&&s.jsxs("div",{className:"flex items-center text-white/80",children:[s.jsx(sh,{className:"w-4 h-4 mr-3 text-white/50"}),s.jsxs("span",{children:[v.address.city,", ",v.address.country]})]}),!v?.personal?.email&&!v?.personal?.phone&&s.jsx("p",{className:"text-white/50 text-sm",children:"No contact information available"})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(xu,{className:"w-5 h-5 mr-2 text-primary-400"}),"Profile Details"]}),s.jsxs("div",{className:"space-y-3",children:[y.country&&s.jsxs("div",{children:[s.jsx("span",{className:"text-white/60 text-sm",children:"Country:"}),s.jsx("p",{className:"text-white/80",children:y.country})]}),y.degree&&s.jsxs("div",{children:[s.jsx("span",{className:"text-white/60 text-sm",children:"Education:"}),s.jsx("p",{className:"text-white/80",children:y.degree})]}),m.bio&&s.jsxs("div",{children:[s.jsx("span",{className:"text-white/60 text-sm",children:"Bio:"}),s.jsx("p",{className:"text-white/80",children:m.bio})]}),!y.country&&!y.degree&&!m.bio&&s.jsx("p",{className:"text-white/50 text-sm",children:"No additional details available"})]})]})]})]})},Xx=({data:u,width:f=800,height:r=300})=>{const c=w.useMemo(()=>{if(!u||u.length===0)return null;const T={top:20,right:30,bottom:40,left:60},q=f-T.left-T.right,R=r-T.top-T.bottom,D=Math.max(...u.map(ne=>ne.cumulative)),L=new Date(Math.min(...u.map(ne=>new Date(ne.date).getTime()))),V=new Date(Math.max(...u.map(ne=>new Date(ne.date).getTime()))).getTime()-L.getTime(),B=u.map((ne,Ce)=>{const Ae=(new Date(ne.date).getTime()-L.getTime())/V*q,Tl=R-ne.cumulative/D*R;return{x:Ae+T.left,y:Tl+T.top,data:ne,index:Ce}}),X=B.reduce((ne,Ce,Ae)=>`${ne} ${Ae===0?"M":"L"} ${Ce.x} ${Ce.y}`,""),W=`${X} L ${B[B.length-1].x} ${R+T.top} L ${T.left} ${R+T.top} Z`,xe=[],ue=5;for(let ne=0;ne<=ue;ne++){const Ce=D/ue*ne,Ae=R+T.top-ne/ue*R;xe.push({value:Math.round(Ce/1e3)+"K",y:Ae,rawValue:Ce})}const _e=[],De=Math.min(5,B.length);for(let ne=0;ne<De;ne++){const Ce=Math.floor((B.length-1)*(ne/(De-1))),Ae=B[Ce];Ae&&_e.push({label:new Date(Ae.data.date).toLocaleDateString("en-US",{month:"short",day:"numeric"}),x:Ae.x})}return{points:B,pathData:X,areaPath:W,yAxisLabels:xe,xAxisLabels:_e,maxXP:D,chartWidth:q,chartHeight:R,margin:T}},[u,f,r]);if(!c)return s.jsx("div",{className:"flex items-center justify-center h-64 text-white/60",children:s.jsx("p",{children:"No data to display"})});const{points:d,pathData:m,areaPath:y,yAxisLabels:v,xAxisLabels:p,margin:x,chartWidth:E,chartHeight:M}=c;return s.jsx("div",{className:"w-full overflow-x-auto",children:s.jsxs("svg",{width:f,height:r,className:"text-white",children:[s.jsx("defs",{children:s.jsxs("linearGradient",{id:"xpGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[s.jsx("stop",{offset:"0%",stopColor:"#14b8a6",stopOpacity:"0.3"}),s.jsx("stop",{offset:"100%",stopColor:"#14b8a6",stopOpacity:"0.05"})]})}),v.map((T,q)=>s.jsx("line",{x1:x.left,y1:T.y,x2:x.left+E,y2:T.y,stroke:"rgba(255,255,255,0.1)",strokeWidth:"1"},q)),p.map((T,q)=>s.jsx("line",{x1:T.x,y1:x.top,x2:T.x,y2:x.top+M,stroke:"rgba(255,255,255,0.1)",strokeWidth:"1"},q)),s.jsx($.path,{d:y,fill:"url(#xpGradient)",initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:.5}}),s.jsx($.path,{d:m,fill:"none",stroke:"#14b8a6",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:2,ease:"easeInOut"}}),d.map((T,q)=>s.jsxs($.g,{children:[s.jsx($.circle,{cx:T.x,cy:T.y,r:"4",fill:"#14b8a6",stroke:"#ffffff",strokeWidth:"2",initial:{scale:0},animate:{scale:1},transition:{delay:.1*q,duration:.3},className:"hover:r-6 transition-all cursor-pointer"}),s.jsxs($.g,{initial:{opacity:0},whileHover:{opacity:1},className:"pointer-events-none",children:[s.jsx("rect",{x:T.x-60,y:T.y-50,width:"120",height:"40",fill:"rgba(0,0,0,0.8)",rx:"4"}),s.jsxs("text",{x:T.x,y:T.y-35,textAnchor:"middle",fill:"white",fontSize:"12",fontWeight:"bold",children:[Math.round(T.data.cumulative/1e3),"K XP"]}),s.jsx("text",{x:T.x,y:T.y-20,textAnchor:"middle",fill:"rgba(255,255,255,0.7)",fontSize:"10",children:T.data.project})]})]},q)),v.map((T,q)=>s.jsx("text",{x:x.left-10,y:T.y+4,textAnchor:"end",fill:"rgba(255,255,255,0.7)",fontSize:"12",children:T.value},q)),p.map((T,q)=>s.jsx("text",{x:T.x,y:x.top+M+20,textAnchor:"middle",fill:"rgba(255,255,255,0.7)",fontSize:"12",children:T.label},q)),s.jsx("line",{x1:x.left,y1:x.top+M,x2:x.left+E,y2:x.top+M,stroke:"rgba(255,255,255,0.3)",strokeWidth:"2"}),s.jsx("line",{x1:x.left,y1:x.top,x2:x.left,y2:x.top+M,stroke:"rgba(255,255,255,0.3)",strokeWidth:"2"})]})})},Zx=({user:u})=>{const{data:f,loading:r}=_l(ae`
    query GetUserXPTransactions($userId: Int!) {
      transaction(
        where: {
          userId: { _eq: $userId }
          type: { _eq: "xp" }
        }
        order_by: { createdAt: desc }
      ) {
        id
        amount
        path
        createdAt
        object {
          name
          type
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"}),{data:c,loading:d}=_l(ae`
    query GetTransactionAggregates($userId: Int!) {
      xp_total: transaction_aggregate(
        where: {
          userId: { _eq: $userId }
          type: { _eq: "xp" }
        }
      ) {
        aggregate {
          sum {
            amount
          }
          count
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"});if(r||d)return s.jsx(vt,{});const y=f?.transaction||[],v=c?.xp_total?.aggregate?.sum?.amount||0,p=c?.xp_total?.aggregate?.count||0,x=Hx(y),E=qx(y),M=Bx(v),T=x.mainModule.slice(0,10),q=y.map(L=>({date:L.createdAt,xp:L.amount,project:L.object?.name||"Unknown",path:L.path})).sort((L,U)=>new Date(L.date).getTime()-new Date(U.date).getTime());let R=0;const D=q.map(L=>(R+=L.xp,{...L,cumulative:R}));return y.slice(0,10).map(L=>({amount:L.amount,project:L.object?.name||"Unknown Project",date:L.createdAt,path:L.path})),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"XP & Progress Analytics"}),s.jsx("p",{className:"text-white/60",children:"Your BH Module learning journey and achievements"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsx(pe,{className:"p-6 bg-gradient-to-br from-primary-500/10 to-primary-600/10 border border-primary-500/20",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm font-medium",children:"Total XP"}),s.jsx("p",{className:"text-3xl font-bold text-primary-400",children:Kc(v)}),s.jsx("p",{className:"text-white/50 text-xs mt-1",children:"All modules combined"})]}),s.jsx("div",{className:"w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Xg,{className:"w-6 h-6 text-primary-400"})})]})}),s.jsx(pe,{className:"p-6 bg-gradient-to-br from-green-500/10 to-green-600/10 border border-green-500/20",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm font-medium",children:"BH Module XP"}),s.jsx("p",{className:"text-3xl font-bold text-green-400",children:Eh(E.bhModule)}),s.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Main curriculum progress"})]}),s.jsx("div",{className:"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Zg,{className:"w-6 h-6 text-green-400"})})]})}),s.jsx(pe,{className:"p-6 bg-gradient-to-br from-blue-500/10 to-blue-600/10 border border-blue-500/20",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm font-medium",children:"Projects Completed"}),s.jsx("p",{className:"text-3xl font-bold text-blue-400",children:p}),s.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Successful submissions"})]}),s.jsx("div",{className:"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center",children:s.jsx(jh,{className:"w-6 h-6 text-blue-400"})})]})}),s.jsx(pe,{className:"p-6 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border border-yellow-500/20",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm font-medium",children:"Current Level"}),s.jsx("p",{className:"text-3xl font-bold text-yellow-400",children:M}),s.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Experience milestone"})]}),s.jsx("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center",children:s.jsx(gu,{className:"w-6 h-6 text-yellow-400"})})]})})]}),s.jsxs(pe,{className:"p-6 bg-gradient-to-r from-gray-800/50 to-gray-900/50 border border-primary-500/20",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(gu,{className:"w-5 h-5 mr-2 text-primary-400"}),"Level Progress"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"text-white/70",children:["Level ",M-1]}),s.jsxs("span",{className:"text-white/70",children:["Level ",M+1]})]}),s.jsx("div",{className:"relative",children:s.jsx("div",{className:"w-full bg-white/10 rounded-full h-4",children:s.jsx($.div,{className:"bg-gradient-to-r from-primary-500 to-primary-600 h-4 rounded-full flex items-center justify-end pr-2",initial:{width:0},animate:{width:`${Math.min(v%1e3/1e3*100,100)}%`},transition:{duration:1.5,ease:"easeOut"},children:s.jsxs("span",{className:"text-xs font-bold text-white",children:[Math.round(v%1e3/1e3*100),"%"]})})})}),s.jsx("div",{className:"text-center",children:s.jsxs("p",{className:"text-white/60 text-sm",children:[Kc(v%1e3)," / ",Kc(1e3)," XP to next level"]})})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(Gn,{className:"w-5 h-5 mr-2 text-primary-400"}),"Recent BH Module Achievements"]}),s.jsxs("div",{className:"space-y-3",children:[T.map((L,U)=>s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:U*.1},className:"flex items-center justify-between p-3 bg-gradient-to-r from-white/5 to-white/10 rounded-lg border border-white/10",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 rounded-full bg-gradient-to-r from-green-400 to-green-500 shadow-lg"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:L.object?.name||"Unknown Project"}),s.jsxs("p",{className:"text-white/60 text-sm",children:["🎯 BH Module • ",Lx(L.createdAt)]})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:"text-primary-400 font-bold",children:["+",Eh(L.amount)]}),s.jsx("p",{className:"text-white/50 text-xs capitalize",children:L.object?.type||"project"})]})]},L.id)),T.length===0&&s.jsxs("div",{className:"text-center text-white/60 py-8",children:[s.jsx("div",{className:"w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Gn,{className:"w-8 h-8 text-white/40"})}),s.jsx("p",{children:"No recent BH Module XP gains"}),s.jsx("p",{className:"text-sm text-white/40 mt-1",children:"Complete projects to see your progress here"})]})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Level Progress"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"text-white/70",children:["Level ",M]}),s.jsxs("span",{className:"text-white/70",children:["Level ",M+1]})]}),s.jsx("div",{className:"w-full bg-white/10 rounded-full h-3",children:s.jsx($.div,{className:"bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full",initial:{width:0},animate:{width:`${progressToNextLevel}%`},transition:{duration:1,ease:"easeOut"}})}),s.jsxs("div",{className:"text-center text-white/60 text-sm",children:[currentLevelXP," / 1000 XP (",progressToNextLevel.toFixed(1),"%)"]})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(Gn,{className:"w-5 h-5 mr-2 text-primary-400"}),"XP Progress Over Time"]}),D.length>0?s.jsx(Xx,{data:D}):s.jsx("div",{className:"text-center text-white/60 py-8",children:s.jsx("p",{children:"No XP data available"})})]})]})},Qx=({user:u})=>{const{data:f,loading:r}=_l(ae`
    query GetUserAudits($userId: Int!) {
      audit(
        where: { auditorId: { _eq: $userId } }
        order_by: { createdAt: desc }
      ) {
        id
        grade
        createdAt
        group {
          id
          path
          object {
            name
            type
          }
          group_users {
            user {
              login
            }
          }
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"}),{data:c,loading:d}=_l(ae`
    query GetAuditStats($userId: Int!) {
      audits_given: audit_aggregate(
        where: { auditorId: { _eq: $userId } }
      ) {
        aggregate {
          count
          avg {
            grade
          }
        }
      }
      audits_received: audit_aggregate(
        where: {
          group: {
            group_users: {
              userId: { _eq: $userId }
            }
          }
        }
      ) {
        aggregate {
          count
          avg {
            grade
          }
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"});if(r||d)return s.jsx(vt,{});const y=f?.audit||[],v=c?.audits_given?.aggregate?.count||0,p=c?.audits_received?.aggregate?.count||0,x=c?.audits_given?.aggregate?.avg?.grade||0;c?.audits_received?.aggregate?.avg?.grade;const E=p>0?v/p:0,T=(q=>q>=1.5?{level:"Excellent",color:"text-green-400",bgColor:"bg-green-500/20"}:q>=1?{level:"Good",color:"text-blue-400",bgColor:"bg-blue-500/20"}:q>=.8?{level:"Average",color:"text-yellow-400",bgColor:"bg-yellow-500/20"}:{level:"Needs Improvement",color:"text-red-400",bgColor:"bg-red-500/20"})(E);return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[s.jsxs(pe,{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Audit Ratio"}),s.jsx("p",{className:"text-3xl font-bold text-primary-400",children:E.toFixed(2)})]}),s.jsx("div",{className:"w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center",children:s.jsx(gu,{className:"w-6 h-6 text-primary-400"})})]}),s.jsx("div",{className:`mt-2 px-2 py-1 rounded-full text-xs font-medium ${T.bgColor} ${T.color}`,children:T.level})]}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Audits Given"}),s.jsx("p",{className:"text-3xl font-bold text-green-400",children:v})]}),s.jsx("div",{className:"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Gn,{className:"w-6 h-6 text-green-400"})})]})}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Audits Received"}),s.jsx("p",{className:"text-3xl font-bold text-blue-400",children:p})]}),s.jsx("div",{className:"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Qg,{className:"w-6 h-6 text-blue-400"})})]})}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Avg Grade Given"}),s.jsx("p",{className:"text-3xl font-bold text-purple-400",children:x.toFixed(1)})]}),s.jsx("div",{className:"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Kg,{className:"w-6 h-6 text-purple-400"})})]})})]}),s.jsxs(pe,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Audit Balance"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-white/70",children:"Given"}),s.jsx("span",{className:"text-white/70",children:"Received"})]}),s.jsx("div",{className:"relative",children:s.jsxs("div",{className:"flex h-8 bg-white/10 rounded-lg overflow-hidden",children:[s.jsx($.div,{className:"bg-green-500 flex items-center justify-center text-white text-sm font-medium",initial:{width:0},animate:{width:`${v/(v+p)*100}%`},transition:{duration:1,ease:"easeOut"},children:v>0&&s.jsx("span",{children:v})}),s.jsx($.div,{className:"bg-blue-500 flex items-center justify-center text-white text-sm font-medium",initial:{width:0},animate:{width:`${p/(v+p)*100}%`},transition:{duration:1,ease:"easeOut",delay:.2},children:p>0&&s.jsx("span",{children:p})})]})}),s.jsxs("div",{className:"text-center text-white/60 text-sm",children:["Ratio: ",E.toFixed(2)," (",T.level,")"]})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Audits"}),s.jsx("div",{className:"space-y-3",children:y.length>0?y.slice(0,10).map((q,R)=>s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:R*.1},className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-white font-medium",children:q.group?.object?.name||"Unknown Project"}),s.jsx("p",{className:"text-white/60 text-sm",children:new Date(q.createdAt).toLocaleDateString()}),q.group?.group_users&&s.jsxs("p",{className:"text-white/50 text-xs",children:["Team: ",q.group.group_users.map(D=>D.user.login).join(", ")]})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("div",{className:`px-2 py-1 rounded text-sm font-medium ${q.grade>=1?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:q.grade>=1?"PASS":"FAIL"}),s.jsxs("p",{className:"text-white/60 text-xs mt-1",children:["Grade: ",q.grade.toFixed(2)]})]})]},q.id)):s.jsxs("div",{className:"text-center text-white/60 py-8",children:[s.jsx(gu,{className:"w-12 h-12 mx-auto mb-4 text-white/30"}),s.jsx("p",{children:"No audit history available"}),s.jsx("p",{className:"text-sm mt-2",children:"Start participating in peer reviews to see your audit activity here."})]})})]}),s.jsxs(pe,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Performance Insights"}),s.jsxs("div",{className:"space-y-3",children:[E<1&&s.jsx("div",{className:"p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg",children:s.jsxs("p",{className:"text-yellow-200 text-sm",children:["💡 ",s.jsx("strong",{children:"Tip:"})," Your audit ratio is below 1.0. Consider participating in more peer reviews to improve your standing."]})}),x>0&&x<.8&&s.jsx("div",{className:"p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg",children:s.jsxs("p",{className:"text-blue-200 text-sm",children:["📊 ",s.jsx("strong",{children:"Insight:"})," Your average grade given is ",x.toFixed(2),". Consider being more thorough in your evaluations."]})}),E>=1.5&&s.jsx("div",{className:"p-3 bg-green-500/20 border border-green-500/30 rounded-lg",children:s.jsxs("p",{className:"text-green-200 text-sm",children:["🌟 ",s.jsx("strong",{children:"Excellent!"})," You have an outstanding audit ratio of ",E.toFixed(2),". Keep up the great work!"]})})]})]})]})},Kx=({data:u,size:f=200})=>{const{passed:r,failed:c,inProgress:d,total:m}=u;if(m===0)return s.jsx("div",{className:"flex items-center justify-center h-48 text-white/60",children:s.jsx("p",{children:"No project data available"})});const y=f/2,v=f/2-20,p=20,x=r/m*100,E=c/m*100,M=d/m*100,T=r/m*360,q=c/m*360,R=d/m*360,D=(B,X,W)=>{const xe=L(y,y,W,X),ue=L(y,y,W,B),_e=X-B<=180?"0":"1";return["M",xe.x,xe.y,"A",W,W,0,_e,0,ue.x,ue.y].join(" ")},L=(B,X,W,xe)=>{const ue=(xe-90)*Math.PI/180;return{x:B+W*Math.cos(ue),y:X+W*Math.sin(ue)}};let U=0;const V=[];return r>0&&(V.push({path:D(U,U+T,v),color:"#10b981",label:"Passed",value:r,percent:x}),U+=T),c>0&&(V.push({path:D(U,U+q,v),color:"#ef4444",label:"Failed",value:c,percent:E}),U+=q),d>0&&V.push({path:D(U,U+R,v),color:"#f59e0b",label:"In Progress",value:d,percent:M}),s.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsxs("svg",{width:f,height:f,className:"transform -rotate-90",children:[s.jsx("circle",{cx:y,cy:y,r:v,fill:"none",stroke:"rgba(255,255,255,0.1)",strokeWidth:p}),V.map((B,X)=>s.jsx($.path,{d:B.path,fill:"none",stroke:B.color,strokeWidth:p,strokeLinecap:"round",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:1.5,delay:X*.3,ease:"easeInOut"}},X))]}),s.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:m}),s.jsx("div",{className:"text-sm text-white/60",children:"Projects"})]})]}),s.jsx("div",{className:"grid grid-cols-1 gap-2 w-full",children:V.map((B,X)=>s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.5+X*.1},className:"flex items-center justify-between p-2 bg-white/5 rounded-lg",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:B.color}}),s.jsx("span",{className:"text-white/80 text-sm",children:B.label})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("div",{className:"text-white font-medium",children:B.value}),s.jsxs("div",{className:"text-white/60 text-xs",children:[B.percent.toFixed(1),"%"]})]})]},X))}),s.jsxs($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},className:"text-center p-3 bg-white/10 rounded-lg w-full",children:[s.jsxs("div",{className:"text-lg font-bold text-primary-400",children:[r+c>0?(r/(r+c)*100).toFixed(1):0,"%"]}),s.jsx("div",{className:"text-sm text-white/60",children:"Success Rate"})]})]})},$x=({data:u,size:f=300})=>{if(!u||u.length===0)return s.jsx("div",{className:"flex items-center justify-center h-48 text-white/60",children:s.jsx("p",{children:"No skills data available"})});const r=f/2,c=f/2-40,d=5,m=u.map((p,x)=>{const E=x/u.length*2*Math.PI-Math.PI/2,M=p.level/p.maxLevel,T=M*c;return{x:r+T*Math.cos(E),y:r+T*Math.sin(E),labelX:r+(c+20)*Math.cos(E),labelY:r+(c+20)*Math.sin(E),skill:p.skill,level:p.level,maxLevel:p.maxLevel,category:p.category,projects:p.projects||0,angle:E,normalizedLevel:M}}),y=m.reduce((p,x,E)=>`${p} ${E===0?"M":"L"} ${x.x} ${x.y}`,"")+" Z",v={Frontend:"#3b82f6",Backend:"#10b981",Systems:"#f59e0b",DevOps:"#8b5cf6",Database:"#ef4444",General:"#6b7280"};return s.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[s.jsx("div",{className:"relative",children:s.jsxs("svg",{width:f,height:f,children:[Array.from({length:d},(p,x)=>{const E=(x+1)/d*c;return s.jsx("circle",{cx:r,cy:r,r:E,fill:"none",stroke:"rgba(255,255,255,0.1)",strokeWidth:"1"},x)}),m.map((p,x)=>s.jsx("line",{x1:r,y1:r,x2:r+c*Math.cos(p.angle),y2:r+c*Math.sin(p.angle),stroke:"rgba(255,255,255,0.1)",strokeWidth:"1"},x)),s.jsx($.path,{d:y,fill:"rgba(20, 184, 166, 0.2)",stroke:"#14b8a6",strokeWidth:"2",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:1},transition:{duration:2,ease:"easeInOut"}}),m.map((p,x)=>s.jsxs($.g,{children:[s.jsx($.circle,{cx:p.x,cy:p.y,r:"4",fill:v[p.category]||"#6b7280",stroke:"#ffffff",strokeWidth:"2",initial:{scale:0},animate:{scale:1},transition:{delay:.1*x,duration:.3},className:"hover:r-6 transition-all cursor-pointer"}),s.jsxs($.g,{initial:{opacity:0},whileHover:{opacity:1},className:"pointer-events-none",children:[s.jsx("rect",{x:p.x-40,y:p.y-35,width:"80",height:"25",fill:"rgba(0,0,0,0.8)",rx:"4"}),s.jsxs("text",{x:p.x,y:p.y-20,textAnchor:"middle",fill:"white",fontSize:"10",fontWeight:"bold",children:[p.level.toFixed(1),"/",p.maxLevel]})]})]},x)),m.map((p,x)=>s.jsx($.text,{x:p.labelX,y:p.labelY,textAnchor:p.labelX>r?"start":"end",dominantBaseline:"middle",fill:"white",fontSize:"12",fontWeight:"500",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5+x*.1},children:p.skill},x))]})}),s.jsx("div",{className:"w-full space-y-2",children:u.map((p,x)=>s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.8+x*.1},className:"flex items-center justify-between p-2 bg-white/5 rounded-lg",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:v[p.category]||"#6b7280"}}),s.jsxs("div",{children:[s.jsx("span",{className:"text-white/80 text-sm font-medium",children:p.skill}),s.jsx("div",{className:"text-white/50 text-xs",children:p.category})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("div",{className:"text-white font-medium",children:[p.level.toFixed(1),"/",p.maxLevel]}),p.projects&&s.jsxs("div",{className:"text-white/60 text-xs",children:[p.projects," project",p.projects!==1?"s":""]})]})]},x))}),s.jsx("div",{className:"grid grid-cols-2 gap-2 w-full text-xs",children:Object.entries(v).map(([p,x])=>u.some(M=>M.category===p)?s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-2 h-2 rounded-full mr-1",style:{backgroundColor:x}}),s.jsx("span",{className:"text-white/60",children:p})]},p):null)})]})},Ix=({user:u})=>{const{data:f,loading:r}=_l(ae`
    query GetUserProgress($userId: Int!) {
      progress(
        where: { userId: { _eq: $userId } }
        order_by: { createdAt: desc }
      ) {
        id
        grade
        isDone
        path
        createdAt
        object {
          name
          type
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"}),{data:c,loading:d}=_l(ae`
    query GetProgressStats($userId: Int!) {
      total_progress: progress_aggregate(
        where: { userId: { _eq: $userId } }
      ) {
        aggregate {
          count
        }
      }
      completed_progress: progress_aggregate(
        where: {
          userId: { _eq: $userId }
          isDone: { _eq: true }
        }
      ) {
        aggregate {
          count
          avg {
            grade
          }
        }
      }
      project_progress: progress_aggregate(
        where: {
          userId: { _eq: $userId }
          object: { type: { _eq: "project" } }
        }
      ) {
        aggregate {
          count
        }
      }
    }
  `,{variables:{userId:u.id},errorPolicy:"all"});if(r||d)return s.jsx(vt,{});const y=f?.progress||[];c?.total_progress?.aggregate?.count,c?.completed_progress?.aggregate?.count,c?.project_progress?.aggregate?.count;const v=c?.completed_progress?.aggregate?.avg?.grade||0,p=y.filter(R=>R.isDone&&R.grade>=1).length,x=y.filter(R=>R.isDone&&R.grade<1).length,E=y.filter(R=>!R.isDone).length,M=p+x>0?p/(p+x)*100:0,T=new Map;y.forEach(R=>{if(R.object?.type==="project"&&R.isDone){const D=R.object.name||"Unknown",L=kx(D,R.path);if(L){const U=T.get(L)||{count:0,totalGrade:0};T.set(L,{count:U.count+1,totalGrade:U.totalGrade+(R.grade||0)})}}});const q=Array.from(T.entries()).map(([R,D])=>({skill:R,level:D.totalGrade/D.count,maxLevel:2,category:Jx(R),projects:D.count}));return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Projects Passed"}),s.jsx("p",{className:"text-3xl font-bold text-green-400",children:p})]}),s.jsx("div",{className:"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center",children:s.jsx(jh,{className:"w-6 h-6 text-green-400"})})]})}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Success Rate"}),s.jsxs("p",{className:"text-3xl font-bold text-blue-400",children:[M.toFixed(1),"%"]})]}),s.jsx("div",{className:"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Gn,{className:"w-6 h-6 text-blue-400"})})]})}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"Avg Grade"}),s.jsx("p",{className:"text-3xl font-bold text-purple-400",children:v.toFixed(2)})]}),s.jsx("div",{className:"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center",children:s.jsx(Ic,{className:"w-6 h-6 text-purple-400"})})]})}),s.jsx(pe,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white/60 text-sm",children:"In Progress"}),s.jsx("p",{className:"text-3xl font-bold text-yellow-400",children:E})]}),s.jsx("div",{className:"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center",children:s.jsx(ch,{className:"w-6 h-6 text-yellow-400"})})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(ch,{className:"w-5 h-5 mr-2 text-primary-400"}),"Project Success Rate"]}),s.jsx(Kx,{data:{passed:p,failed:x,inProgress:E,total:p+x+E}})]}),s.jsxs(pe,{className:"p-6",children:[s.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[s.jsx(Ic,{className:"w-5 h-5 mr-2 text-primary-400"}),"Technology Skills"]}),q.length>0?s.jsx($x,{data:q}):s.jsx("div",{className:"text-center text-white/60 py-8",children:s.jsx("p",{children:"No skills data available"})})]})]}),s.jsxs(pe,{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Progress"}),s.jsx("div",{className:"space-y-3",children:y.slice(0,10).map((R,D)=>s.jsxs($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:D*.1},className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-white font-medium",children:R.object?.name||"Unknown Project"}),s.jsx("p",{className:"text-white/60 text-sm",children:new Date(R.createdAt).toLocaleDateString()}),s.jsxs("p",{className:"text-white/50 text-xs",children:["Type: ",R.object?.type||"Unknown"]})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("div",{className:`px-2 py-1 rounded text-sm font-medium ${R.isDone?R.grade>=1?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400"}`,children:R.isDone?R.grade>=1?"PASSED":"FAILED":"IN PROGRESS"}),R.isDone&&s.jsxs("p",{className:"text-white/60 text-xs mt-1",children:["Grade: ",R.grade?.toFixed(2)||"N/A"]})]})]},R.id))})]})]})},kx=(u,f)=>{const r=u.toLowerCase(),c=f?.toLowerCase()||"";return r.includes("go")||c.includes("go")?"Go":r.includes("js")||r.includes("javascript")||c.includes("js")?"JavaScript":r.includes("python")||c.includes("python")?"Python":r.includes("rust")||c.includes("rust")?"Rust":r.includes("c++")||c.includes("cpp")?"C++":r.includes("java")||c.includes("java")?"Java":r.includes("docker")||c.includes("docker")?"Docker":r.includes("sql")||c.includes("sql")?"SQL":r.includes("html")||c.includes("html")?"HTML/CSS":r.includes("react")||c.includes("react")?"React":null},Jx=u=>({Go:"Backend",JavaScript:"Frontend",Python:"Backend",Rust:"Systems","C++":"Systems",Java:"Backend",Docker:"DevOps",SQL:"Database","HTML/CSS":"Frontend",React:"Frontend"})[u]||"General",Wx=()=>{const{userId:u}=Rp(),f=_u(),{setCurrentProfile:r,setLoading:c,setError:d}=xx(),m=u?parseInt(u,10):null,{profile:y,isLoading:v,error:p,hasData:x}=bx(m);return f?!m||isNaN(m)?s.jsx(Ua,{to:"/dashboard",replace:!0}):(w.useEffect(()=>(m&&(r(m),!x&&!v&&(c(!0),setTimeout(()=>{d("User profile data not available")},1e3))),()=>{r(null)}),[m,x,v,r,c,d]),v?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center",children:s.jsx(vt,{})}):p||!y?.user?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("div",{className:"mb-8",children:s.jsxs(Oa,{to:"/dashboard",className:"inline-flex items-center text-white/70 hover:text-white transition-colors",children:[s.jsx(kc,{className:"w-5 h-5 mr-2"}),"Back to Dashboard"]})}),s.jsxs("div",{className:"text-center py-16",children:[s.jsx("div",{className:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(xu,{className:"w-12 h-12 text-white/40"})}),s.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"User Profile Not Found"}),s.jsx("p",{className:"text-white/60 mb-8",children:p||"The requested user profile could not be loaded."}),s.jsx(Oa,{to:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors",children:"Return to Dashboard"})]})]})}):s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs($.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"mb-8",children:[s.jsxs(Oa,{to:"/dashboard",className:"inline-flex items-center text-white/70 hover:text-white transition-colors mb-4",children:[s.jsx(kc,{className:"w-5 h-5 mr-2"}),"Back to Dashboard"]}),s.jsxs("div",{className:"text-center",children:[s.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:y.user.firstName&&y.user.lastName?`${y.user.firstName} ${y.user.lastName}`:y.user.login}),s.jsx("p",{className:"text-white/70 text-lg",children:"Student Profile & Performance Analytics"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",children:[s.jsx($.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.1},className:"lg:col-span-1 xl:col-span-1",children:s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:s.jsx(Yx,{user:y.user})})}),s.jsx($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"lg:col-span-1 xl:col-span-1",children:s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:s.jsx(Zx,{user:y.user})})}),s.jsx($.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:.3},className:"lg:col-span-2 xl:col-span-1",children:s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:s.jsx(Qx,{user:y.user})})})]}),s.jsx($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"mt-8",children:s.jsx(w.Suspense,{fallback:s.jsx(vt,{}),children:s.jsx(Ix,{user:y.user})})})]})})):s.jsx(Ua,{to:"/login",replace:!0})},Px=()=>s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center p-4",children:s.jsxs("div",{className:"text-center max-w-md mx-auto",children:[s.jsxs($.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.5,type:"spring",stiffness:200},className:"mb-8",children:[s.jsx("div",{className:"text-8xl font-bold text-primary-500 mb-4",children:"404"}),s.jsx("div",{className:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Ah,{className:"w-12 h-12 text-white/40"})})]}),s.jsxs($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-4",children:"Page Not Found"}),s.jsx("p",{className:"text-white/70 mb-8 leading-relaxed",children:"The page you're looking for doesn't exist or has been moved. Let's get you back on track."}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs(Oa,{to:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors w-full justify-center",children:[s.jsx(wh,{className:"w-5 h-5 mr-2"}),"Go to Dashboard"]}),s.jsxs("button",{onClick:()=>window.history.back(),className:"inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors w-full justify-center",children:[s.jsx(kc,{className:"w-5 h-5 mr-2"}),"Go Back"]})]})]}),s.jsx($.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.4},className:"mt-12",children:s.jsx("p",{className:"text-white/50 text-sm",children:"Reboot01 Student Dashboard"})})]})});class Fx extends w.Component{constructor(f){super(f),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(f){return{hasError:!0,error:f,errorInfo:null}}componentDidCatch(f,r){this.setState({error:f,errorInfo:r}),this.props.onError&&this.props.onError(f,r)}handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})};handleGoHome=()=>{window.location.href="/"};render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center p-4",children:s.jsxs($.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 text-center",children:[s.jsx($.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx($g,{className:"w-8 h-8 text-red-400"})}),s.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:"Oops! Something went wrong"}),s.jsx("p",{className:"text-white/70 mb-6",children:"We encountered an unexpected error. This has been logged and we'll look into it."}),!1,s.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[s.jsxs($.button,{onClick:this.handleRetry,whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1 bg-primary-500 hover:bg-primary-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center",children:[s.jsx(Ig,{className:"w-4 h-4 mr-2"}),"Try Again"]}),s.jsxs($.button,{onClick:this.handleGoHome,whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1 bg-white/10 hover:bg-white/20 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center border border-white/20",children:[s.jsx(wh,{className:"w-4 h-4 mr-2"}),"Go Home"]})]})]})}):this.props.children}}const ev=()=>{const u=_u(),f=Jh(),r=Wh(),[c,d]=mu.useState(!0);return w.useEffect(()=>{(async()=>{r(!0);try{const y=qy();y.user&&y.token&&f(y.user,y.token)}catch(y){console.error("Failed to initialize auth:",y)}finally{r(!1),d(!1)}})()},[]),c?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-surface-900 via-surface-800 to-primary-900 flex items-center justify-center",children:s.jsx(vt,{})}):s.jsx(my,{children:s.jsxs(Qp,{children:[s.jsx(Ma,{path:"/",element:u?s.jsx(Ua,{to:"/dashboard",replace:!0}):s.jsx(Ua,{to:"/login",replace:!0})}),s.jsx(Ma,{path:"/login",element:s.jsx(Sx,{})}),s.jsx(Ma,{path:"/dashboard",element:s.jsx(Mx,{})}),s.jsx(Ma,{path:"/profile/:userId",element:s.jsx(Wx,{})}),s.jsx(Ma,{path:"*",element:s.jsx(Px,{})})]})})},tv=()=>s.jsx(Fx,{children:s.jsx(Th,{client:$h,children:s.jsx(ev,{})})});ep.createRoot(document.getElementById("root")).render(s.jsx(mu.StrictMode,{children:s.jsx(Th,{client:$h,children:s.jsx(tv,{})})}));export{Qx as A,pe as C,sv as G,vt as L,Yx as P,Ix as S,Zx as X,Ux as a,uv as b,cv as c,Eh as f,Lx as g};
