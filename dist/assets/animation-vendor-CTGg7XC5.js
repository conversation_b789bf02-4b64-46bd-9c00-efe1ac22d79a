import{r as fr,g as dr}from"./react-vendor-Csw2ODfV.js";var ee={exports:{}},yt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xn;function mr(){if(xn)return yt;xn=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function n(s,i,o){var r=null;if(o!==void 0&&(r=""+o),i.key!==void 0&&(r=""+i.key),"key"in i){o={};for(var a in i)a!=="key"&&(o[a]=i[a])}else o=i;return i=o.ref,{$$typeof:t,type:s,key:r,ref:i!==void 0?i:null,props:o}}return yt.Fragment=e,yt.jsx=n,yt.jsxs=n,yt}var Tn;function pr(){return Tn||(Tn=1,ee.exports=mr()),ee.exports}var z=pr(),v=fr();const ec=dr(v),Be=v.createContext({});function Ie(t){const e=v.useRef(null);return e.current===null&&(e.current=t()),e.current}const je=typeof window<"u",Bs=je?v.useLayoutEffect:v.useEffect,Yt=v.createContext(null);function Oe(t,e){t.indexOf(e)===-1&&t.push(e)}function Ne(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const X=(t,e,n)=>n>e?e:n<t?t:n;let Ue=()=>{};const Y={},Is=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function js(t){return typeof t=="object"&&t!==null}const Os=t=>/^0[^.\s]+$/u.test(t);function We(t){let e;return()=>(e===void 0&&(e=t()),e)}const W=t=>t,gr=(t,e)=>n=>e(t(n)),Lt=(...t)=>t.reduce(gr),bt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class Ke{constructor(){this.subscriptions=[]}add(e){return Oe(this.subscriptions,e),()=>Ne(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const $=t=>t*1e3,_=t=>t/1e3;function Ns(t,e){return e?t*(1e3/e):0}const Us=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,yr=1e-7,vr=12;function xr(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Us(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>yr&&++a<vr);return r}function kt(t,e,n,s){if(t===e&&n===s)return W;const i=o=>xr(o,0,1,t,n);return o=>o===0||o===1?o:Us(i(o),e,s)}const Ws=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ks=t=>e=>1-t(1-e),$s=kt(.33,1.53,.69,.99),$e=Ks($s),_s=Ws($e),Gs=t=>(t*=2)<1?.5*$e(t):.5*(2-Math.pow(2,-10*(t-1))),_e=t=>1-Math.sin(Math.acos(t)),Hs=Ks(_e),zs=Ws(_e),Tr=kt(.42,0,1,1),Pr=kt(0,0,.58,1),Xs=kt(.42,0,.58,1),Sr=t=>Array.isArray(t)&&typeof t[0]!="number",Ys=t=>Array.isArray(t)&&typeof t[0]=="number",wr={linear:W,easeIn:Tr,easeInOut:Xs,easeOut:Pr,circIn:_e,circInOut:zs,circOut:Hs,backIn:$e,backInOut:_s,backOut:$s,anticipate:Gs},Ar=t=>typeof t=="string",Pn=t=>{if(Ys(t)){Ue(t.length===4);const[e,n,s,i]=t;return kt(e,n,s,i)}else if(Ar(t))return wr[t];return t},It=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Sn={value:null};function br(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(h){r.has(h)&&(c.schedule(h),t()),l++,h(a)}const c={schedule:(h,f=!1,d=!1)=>{const p=d&&i?n:s;return f&&r.add(h),p.has(h)||p.add(h),h},cancel:h=>{s.delete(h),r.delete(h)},process:h=>{if(a=h,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(u),e&&Sn.value&&Sn.value.frameloop[e].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(h))}};return c}const Vr=40;function qs(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=It.reduce((y,A)=>(y[A]=br(o,e?A:void 0),y),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:f,render:d,postRender:m}=r,p=()=>{const y=Y.useManualTiming?i.timestamp:performance.now();n=!1,Y.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(y-i.timestamp,Vr),1)),i.timestamp=y,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),f.process(i),d.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(p))},T=()=>{n=!0,s=!0,i.isProcessing||t(p)};return{schedule:It.reduce((y,A)=>{const P=r[A];return y[A]=(b,V=!1,w=!1)=>(n||T(),P.schedule(b,V,w)),y},{}),cancel:y=>{for(let A=0;A<It.length;A++)r[It[A]].cancel(y)},state:i,steps:r}}const{schedule:C,cancel:J,state:k,steps:ne}=qs(typeof requestAnimationFrame<"u"?requestAnimationFrame:W,!0);let Ut;function Cr(){Ut=void 0}const O={now:()=>(Ut===void 0&&O.set(k.isProcessing||Y.useManualTiming?k.timestamp:performance.now()),Ut),set:t=>{Ut=t,queueMicrotask(Cr)}},Zs=t=>e=>typeof e=="string"&&e.startsWith(t),Ge=Zs("--"),Mr=Zs("var(--"),He=t=>Mr(t)?Dr.test(t.split("/*")[0].trim()):!1,Dr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Vt={...mt,transform:t=>X(0,1,t)},jt={...mt,default:1},Tt=t=>Math.round(t*1e5)/1e5,ze=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Rr(t){return t==null}const Er=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Xe=(t,e)=>n=>!!(typeof n=="string"&&Er.test(n)&&n.startsWith(t)||e&&!Rr(n)&&Object.prototype.hasOwnProperty.call(n,e)),Js=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(ze);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},Lr=t=>X(0,255,t),se={...mt,transform:t=>Math.round(Lr(t))},st={test:Xe("rgb","red"),parse:Js("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+se.transform(t)+", "+se.transform(e)+", "+se.transform(n)+", "+Tt(Vt.transform(s))+")"};function kr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const pe={test:Xe("#"),parse:kr,transform:st.transform},Ft=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Z=Ft("deg"),G=Ft("%"),S=Ft("px"),Fr=Ft("vh"),Br=Ft("vw"),wn={...G,parse:t=>G.parse(t)/100,transform:t=>G.transform(t*100)},at={test:Xe("hsl","hue"),parse:Js("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+G.transform(Tt(e))+", "+G.transform(Tt(n))+", "+Tt(Vt.transform(s))+")"},E={test:t=>st.test(t)||pe.test(t)||at.test(t),parse:t=>st.test(t)?st.parse(t):at.test(t)?at.parse(t):pe.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?st.transform(t):at.transform(t),getAnimatableNone:t=>{const e=E.parse(t);return e.alpha=0,E.transform(e)}},Ir=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function jr(t){return isNaN(t)&&typeof t=="string"&&(t.match(ze)?.length||0)+(t.match(Ir)?.length||0)>0}const Qs="number",ti="color",Or="var",Nr="var(",An="${}",Ur=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ct(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(Ur,l=>(E.test(l)?(s.color.push(o),i.push(ti),n.push(E.parse(l))):l.startsWith(Nr)?(s.var.push(o),i.push(Or),n.push(l)):(s.number.push(o),i.push(Qs),n.push(parseFloat(l))),++o,An)).split(An);return{values:n,split:a,indexes:s,types:i}}function ei(t){return Ct(t).values}function ni(t){const{split:e,types:n}=Ct(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===Qs?o+=Tt(i[r]):a===ti?o+=E.transform(i[r]):o+=i[r]}return o}}const Wr=t=>typeof t=="number"?0:E.test(t)?E.getAnimatableNone(t):t;function Kr(t){const e=ei(t);return ni(t)(e.map(Wr))}const Q={test:jr,parse:ei,createTransformer:ni,getAnimatableNone:Kr};function ie(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function $r({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=ie(l,a,t+1/3),o=ie(l,a,t),r=ie(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function _t(t,e){return n=>n>0?e:t}const M=(t,e,n)=>t+(e-t)*n,re=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},_r=[pe,st,at],Gr=t=>_r.find(e=>e.test(t));function bn(t){const e=Gr(t);if(!e)return!1;let n=e.parse(t);return e===at&&(n=$r(n)),n}const Vn=(t,e)=>{const n=bn(t),s=bn(e);if(!n||!s)return _t(t,e);const i={...n};return o=>(i.red=re(n.red,s.red,o),i.green=re(n.green,s.green,o),i.blue=re(n.blue,s.blue,o),i.alpha=M(n.alpha,s.alpha,o),st.transform(i))},ge=new Set(["none","hidden"]);function Hr(t,e){return ge.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function zr(t,e){return n=>M(t,e,n)}function Ye(t){return typeof t=="number"?zr:typeof t=="string"?He(t)?_t:E.test(t)?Vn:qr:Array.isArray(t)?si:typeof t=="object"?E.test(t)?Vn:Xr:_t}function si(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>Ye(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function Xr(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ye(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function Yr(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const qr=(t,e)=>{const n=Q.createTransformer(e),s=Ct(t),i=Ct(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?ge.has(t)&&!i.values.length||ge.has(e)&&!s.values.length?Hr(t,e):Lt(si(Yr(s,i),i.values),n):_t(t,e)};function ii(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?M(t,e,n):Ye(t)(t,e)}const Zr=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>C.update(e,n),stop:()=>J(e),now:()=>k.isProcessing?k.timestamp:O.now()}},ri=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=Math.round(t(o/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Gt=2e4;function qe(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Gt;)e+=n,s=t.next(e);return e>=Gt?1/0:e}function Jr(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(qe(s),Gt);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:_(i)}}const Qr=5;function oi(t,e,n){const s=Math.max(e-Qr,0);return Ns(n-t(s),e-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},oe=.001;function to({duration:t=D.duration,bounce:e=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let i,o,r=1-e;r=X(D.minDamping,D.maxDamping,r),t=X(D.minDuration,D.maxDuration,_(t)),r<1?(i=u=>{const c=u*r,h=c*t,f=c-n,d=ye(u,r),m=Math.exp(-h);return oe-f/d*m},o=u=>{const h=u*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(u,2)*t,m=Math.exp(-h),p=ye(Math.pow(u,2),r);return(-i(u)+oe>0?-1:1)*((f-d)*m)/p}):(i=u=>{const c=Math.exp(-u*t),h=(u-n)*t+1;return-oe+c*h},o=u=>{const c=Math.exp(-u*t),h=(n-u)*(t*t);return c*h});const a=5/t,l=no(i,o,a);if(t=$(t),isNaN(l))return{stiffness:D.stiffness,damping:D.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:r*2*Math.sqrt(s*u),duration:t}}}const eo=12;function no(t,e,n){let s=n;for(let i=1;i<eo;i++)s=s-t(s)/e(s);return s}function ye(t,e){return t*Math.sqrt(1-e*e)}const so=["duration","bounce"],io=["stiffness","damping","mass"];function Cn(t,e){return e.some(n=>t[n]!==void 0)}function ro(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!Cn(t,io)&&Cn(t,so))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*X(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:D.mass,stiffness:i,damping:o}}else{const n=to(t);e={...e,...n,mass:D.mass},e.isResolvedFromDuration=!0}return e}function Ht(t=D.visualDuration,e=D.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:f,isResolvedFromDuration:d}=ro({...n,velocity:-_(n.velocity||0)}),m=f||0,p=u/(2*Math.sqrt(l*c)),T=r-o,g=_(Math.sqrt(l/c)),x=Math.abs(T)<5;s||(s=x?D.restSpeed.granular:D.restSpeed.default),i||(i=x?D.restDelta.granular:D.restDelta.default);let y;if(p<1){const P=ye(g,p);y=b=>{const V=Math.exp(-p*g*b);return r-V*((m+p*g*T)/P*Math.sin(P*b)+T*Math.cos(P*b))}}else if(p===1)y=P=>r-Math.exp(-g*P)*(T+(m+g*T)*P);else{const P=g*Math.sqrt(p*p-1);y=b=>{const V=Math.exp(-p*g*b),w=Math.min(P*b,300);return r-V*((m+p*g*T)*Math.sinh(w)+P*T*Math.cosh(w))/P}}const A={calculatedDuration:d&&h||null,next:P=>{const b=y(P);if(d)a.done=P>=h;else{let V=P===0?m:0;p<1&&(V=P===0?$(m):oi(y,P,b));const w=Math.abs(V)<=s,L=Math.abs(r-b)<=i;a.done=w&&L}return a.value=a.done?r:b,a},toString:()=>{const P=Math.min(qe(A),Gt),b=ri(V=>A.next(P*V).value,P,30);return P+"ms "+b},toTransition:()=>{}};return A}Ht.applyToOptions=t=>{const e=Jr(t,100,Ht);return t.ease=e.ease,t.duration=$(e.duration),t.type="keyframes",t};function ve({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],f={done:!1,value:h},d=w=>a!==void 0&&w<a||l!==void 0&&w>l,m=w=>a===void 0?l:l===void 0||Math.abs(a-w)<Math.abs(l-w)?a:l;let p=n*e;const T=h+p,g=r===void 0?T:r(T);g!==T&&(p=g-h);const x=w=>-p*Math.exp(-w/s),y=w=>g+x(w),A=w=>{const L=x(w),B=y(w);f.done=Math.abs(L)<=u,f.value=f.done?g:B};let P,b;const V=w=>{d(f.value)&&(P=w,b=Ht({keyframes:[f.value,m(f.value)],velocity:oi(y,w,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return V(0),{calculatedDuration:null,next:w=>{let L=!1;return!b&&P===void 0&&(L=!0,A(w),V(w)),P!==void 0&&w>=P?b.next(w-P):(!L&&A(w),f)}}}function oo(t,e,n){const s=[],i=n||Y.mix||ii,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||W:e;a=Lt(l,a)}s.push(a)}return s}function ao(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(Ue(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=oo(e,s,i),l=a.length,u=c=>{if(r&&c<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(c<t[h+1]);h++);const f=bt(t[h],t[h+1],c);return a[h](f)};return n?c=>u(X(t[0],t[o-1],c)):u}function lo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=bt(0,e,s);t.push(M(n,1,i))}}function uo(t){const e=[0];return lo(e,t.length-1),e}function co(t,e){return t.map(n=>n*e)}function ho(t,e){return t.map(()=>e||Xs).splice(0,t.length-1)}function Pt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Sr(s)?s.map(Pn):Pn(s),o={done:!1,value:e[0]},r=co(n&&n.length===e.length?n:uo(e),t),a=ao(r,e,{ease:Array.isArray(i)?i:ho(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const fo=t=>t!==null;function Ze(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(fo),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const mo={decay:ve,inertia:ve,tween:Pt,keyframes:Pt,spring:Ht};function ai(t){typeof t.type=="string"&&(t.type=mo[t.type])}class Je{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const po=t=>t/100;class Qe extends Je{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==O.now()&&this.tick(O.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ai(e);const{type:n=Pt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Pt;l!==Pt&&typeof a[0]!="number"&&(this.mixKeyframes=Lt(po,ii(a[0],a[1])),a=[0,100]);const u=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),u.calculatedDuration===null&&(u.calculatedDuration=qe(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:f,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:T}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let y=this.currentTime,A=s;if(h){const w=Math.min(this.currentTime,i)/a;let L=Math.floor(w),B=w%1;!B&&w>=1&&(B=1),B===1&&L--,L=Math.min(L,h+1),!!(L%2)&&(f==="reverse"?(B=1-B,d&&(B-=d/a)):f==="mirror"&&(A=r)),y=X(0,1,B)*a}const P=x?{done:!1,value:c[0]}:A.next(y);o&&(P.value=o(P.value));let{done:b}=P;!x&&l!==null&&(b=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const V=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&b);return V&&m!==ve&&(P.value=Ze(c,this.options,T,this.speed)),p&&p(P.value),V&&this.finish(),P}then(e,n){return this.finished.then(e,n)}get duration(){return _(this.calculatedDuration)}get time(){return _(this.currentTime)}set time(e){e=$(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(O.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=_(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=Zr,startTime:n}=this.options;this.driver||(this.driver=e(i=>this.tick(i))),this.options.onPlay?.();const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(O.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function go(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const it=t=>t*180/Math.PI,xe=t=>{const e=it(Math.atan2(t[1],t[0]));return Te(e)},yo={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:xe,rotateZ:xe,skewX:t=>it(Math.atan(t[1])),skewY:t=>it(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Te=t=>(t=t%360,t<0&&(t+=360),t),Mn=xe,Dn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Rn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),vo={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Dn,scaleY:Rn,scale:t=>(Dn(t)+Rn(t))/2,rotateX:t=>Te(it(Math.atan2(t[6],t[5]))),rotateY:t=>Te(it(Math.atan2(-t[2],t[0]))),rotateZ:Mn,rotate:Mn,skewX:t=>it(Math.atan(t[4])),skewY:t=>it(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Pe(t){return t.includes("scale")?1:0}function Se(t,e){if(!t||t==="none")return Pe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=vo,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=yo,i=a}if(!i)return Pe(e);const o=s[e],r=i[1].split(",").map(To);return typeof o=="function"?o(r):r[o]}const xo=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Se(n,e)};function To(t){return parseFloat(t.trim())}const pt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gt=new Set(pt),En=t=>t===mt||t===S,Po=new Set(["x","y","z"]),So=pt.filter(t=>!Po.has(t));function wo(t){const e=[];return So.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const rt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Se(e,"x"),y:(t,{transform:e})=>Se(e,"y")};rt.translateX=rt.x;rt.translateY=rt.y;const ot=new Set;let we=!1,Ae=!1,be=!1;function li(){if(Ae){const t=Array.from(ot).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=wo(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{s.getValue(o)?.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Ae=!1,we=!1,ot.forEach(t=>t.complete(be)),ot.clear()}function ui(){ot.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ae=!0)})}function Ao(){be=!0,ui(),li(),be=!1}class tn{constructor(e,n,s,i,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(ot.add(this),we||(we=!0,C.read(ui),C.resolveKeyframes(li))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i?.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}go(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ot.delete(this)}cancel(){this.state==="scheduled"&&(ot.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const bo=t=>t.startsWith("--");function Vo(t,e,n){bo(e)?t.style.setProperty(e,n):t.style[e]=n}const Co=We(()=>window.ScrollTimeline!==void 0),Mo={};function Do(t,e){const n=We(t);return()=>Mo[e]??n()}const ci=Do(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),xt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:xt([0,.65,.55,1]),circOut:xt([.55,0,1,.45]),backIn:xt([.31,.01,.66,-.59]),backOut:xt([.33,1.53,.69,.99])};function hi(t,e){if(t)return typeof t=="function"?ci()?ri(t,e):"ease-out":Ys(t)?xt(t):Array.isArray(t)?t.map(n=>hi(n,e)||Ln.easeOut):Ln[t]}function Ro(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=hi(a,i);Array.isArray(h)&&(c.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return u&&(f.pseudoElement=u),t.animate(c,f)}function fi(t){return typeof t=="function"&&"applyToOptions"in t}function Eo({type:t,...e}){return fi(t)&&ci()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Lo extends Je{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,Ue(typeof e.type!="string");const u=Eo(e);this.animation=Ro(n,s,i,u,o),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const c=Ze(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):Vo(n,s,c),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const e=this.animation.effect?.getComputedTiming?.().duration||0;return _(Number(e))}get time(){return _(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=$(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&Co()?(this.animation.timeline=e,W):n(this)}}const di={anticipate:Gs,backInOut:_s,circInOut:zs};function ko(t){return t in di}function Fo(t){typeof t.ease=="string"&&ko(t.ease)&&(t.ease=di[t.ease])}const kn=10;class Bo extends Lo{constructor(e){Fo(e),ai(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new Qe({...r,autoplay:!1}),l=$(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-kn).value,a.sample(l).value,kn),a.stop()}}const Fn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Q.test(t)||t==="0")&&!t.startsWith("url("));function Io(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function jo(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=Fn(i,e),a=Fn(o,e);return!r||!a?!1:Io(t)||(n==="spring"||fi(n))&&s}function en(t){return js(t)&&"offsetHeight"in t}const Oo=new Set(["opacity","clipPath","filter","transform"]),No=We(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Uo(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!en(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return No()&&n&&Oo.has(n)&&(n!=="transform"||!l)&&!a&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const Wo=40;class Ko extends Je{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:u,element:c,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=O.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:l,motionValue:u,element:c,...h},d=c?.KeyframeResolver||tn;this.keyframeResolver=new d(a,(m,p,T)=>this.onKeyframesResolved(m,p,f,!T),l,u,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:u,onUpdate:c}=s;this.resolvedAt=O.now(),jo(e,o,r,a)||((Y.instantAnimations||!l)&&c?.(Ze(e,s,n)),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Wo?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!u&&Uo(f)?new Bo({...f,element:f.motionValue.owner.current}):new Qe(f);d.finished.then(()=>this.notifyFinished()).catch(W),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Ao()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const $o=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function _o(t){const e=$o.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function mi(t,e,n=1){const[s,i]=_o(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Is(r)?parseFloat(r):r}return He(i)?mi(i,e,n+1):i}function nn(t,e){return t?.[e]??t?.default??t}const pi=new Set(["width","height","top","left","right","bottom",...pt]),Go={test:t=>t==="auto",parse:t=>t},gi=t=>e=>e.test(t),yi=[mt,S,G,Z,Br,Fr,Go],Bn=t=>yi.find(gi(t));function Ho(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Os(t):!0}const zo=new Set(["brightness","contrast","saturate","opacity"]);function Xo(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(ze)||[];if(!s)return t;const i=n.replace(s,"");let o=zo.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const Yo=/\b([a-z-]*)\(.*?\)/gu,Ve={...Q,getAnimatableNone:t=>{const e=t.match(Yo);return e?e.map(Xo).join(" "):t}},In={...mt,transform:Math.round},qo={rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scale:jt,scaleX:jt,scaleY:jt,scaleZ:jt,skew:Z,skewX:Z,skewY:Z,distance:S,translateX:S,translateY:S,translateZ:S,x:S,y:S,z:S,perspective:S,transformPerspective:S,opacity:Vt,originX:wn,originY:wn,originZ:S},sn={borderWidth:S,borderTopWidth:S,borderRightWidth:S,borderBottomWidth:S,borderLeftWidth:S,borderRadius:S,radius:S,borderTopLeftRadius:S,borderTopRightRadius:S,borderBottomRightRadius:S,borderBottomLeftRadius:S,width:S,maxWidth:S,height:S,maxHeight:S,top:S,right:S,bottom:S,left:S,padding:S,paddingTop:S,paddingRight:S,paddingBottom:S,paddingLeft:S,margin:S,marginTop:S,marginRight:S,marginBottom:S,marginLeft:S,backgroundPositionX:S,backgroundPositionY:S,...qo,zIndex:In,fillOpacity:Vt,strokeOpacity:Vt,numOctaves:In},Zo={...sn,color:E,backgroundColor:E,outlineColor:E,fill:E,stroke:E,borderColor:E,borderTopColor:E,borderRightColor:E,borderBottomColor:E,borderLeftColor:E,filter:Ve,WebkitFilter:Ve},vi=t=>Zo[t];function xi(t,e){let n=vi(t);return n!==Ve&&(n=Q),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Jo=new Set(["auto","none","0"]);function Qo(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Jo.has(o)&&Ct(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=xi(n,i)}class ta extends tn{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),He(u))){const c=mi(u,n.current);c!==void 0&&(e[l]=c),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!pi.has(s)||e.length!==2)return;const[i,o]=e,r=Bn(i),a=Bn(o);if(r!==a)if(En(r)&&En(a))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else rt[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Ho(e[i]))&&s.push(i);s.length&&Qo(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=rt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=rt[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,l])=>{e.getValue(a).set(l)}),this.resolveNoneKeyframes()}}function ea(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const Ti=(t,e)=>e&&typeof t=="number"?e.transform(t):t,jn=30,na=t=>!isNaN(parseFloat(t));class sa{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const o=O.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const r of this.dependents)r.dirty();i&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=O.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=na(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Ke);const s=this.events[e].add(n);return e==="change"?()=>{s(),C.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=O.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>jn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,jn);return Ns(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ft(t,e){return new sa(t,e)}const{schedule:rn}=qs(queueMicrotask,!1),K={x:!1,y:!1};function Pi(){return K.x||K.y}function ia(t){return t==="x"||t==="y"?K[t]?null:(K[t]=!0,()=>{K[t]=!1}):K.x||K.y?null:(K.x=K.y=!0,()=>{K.x=K.y=!1})}function Si(t,e){const n=ea(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function On(t){return!(t.pointerType==="touch"||Pi())}function ra(t,e,n={}){const[s,i,o]=Si(t,n),r=a=>{if(!On(a))return;const{target:l}=a,u=e(l,a);if(typeof u!="function"||!l)return;const c=h=>{On(h)&&(u(h),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const wi=(t,e)=>e?t===e?!0:wi(t,e.parentElement):!1,on=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,oa=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function aa(t){return oa.has(t.tagName)||t.tabIndex!==-1}const Wt=new WeakSet;function Nn(t){return e=>{e.key==="Enter"&&t(e)}}function ae(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const la=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=Nn(()=>{if(Wt.has(n))return;ae(n,"down");const i=Nn(()=>{ae(n,"up")}),o=()=>ae(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function Un(t){return on(t)&&!Pi()}function ua(t,e,n={}){const[s,i,o]=Si(t,n),r=a=>{const l=a.currentTarget;if(!Un(a))return;Wt.add(l);const u=e(l,a),c=(d,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Wt.has(l)&&Wt.delete(l),Un(d)&&typeof u=="function"&&u(d,{success:m})},h=d=>{c(d,l===window||l===document||n.useGlobalTarget||wi(l,d.target))},f=d=>{c(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),en(a)&&(a.addEventListener("focus",u=>la(u,i)),!aa(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Ai(t){return js(t)&&"ownerSVGElement"in t}function ca(t){return Ai(t)&&t.tagName==="svg"}const F=t=>!!(t&&t.getVelocity),ha=[...yi,E,Q],fa=t=>ha.find(gi(t)),an=v.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class da extends v.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=n.offsetParent,i=en(s)&&s.offsetWidth||0,o=this.props.sizeRef.current;o.height=n.offsetHeight||0,o.width=n.offsetWidth||0,o.top=n.offsetTop,o.left=n.offsetLeft,o.right=i-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function ma({children:t,isPresent:e,anchorX:n,root:s}){const i=v.useId(),o=v.useRef(null),r=v.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=v.useContext(an);return v.useInsertionEffect(()=>{const{width:l,height:u,top:c,left:h,right:f}=r.current;if(e||!o.current||!l||!u)return;const d=n==="left"?`left: ${h}`:`right: ${f}`;o.current.dataset.motionPopId=i;const m=document.createElement("style");a&&(m.nonce=a);const p=s??document.head;return p.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${l}px !important;
            height: ${u}px !important;
            ${d}px !important;
            top: ${c}px !important;
          }
        `),()=>{p.removeChild(m),p.contains(m)&&p.removeChild(m)}},[e]),z.jsx(da,{isPresent:e,childRef:o,sizeRef:r,children:v.cloneElement(t,{ref:o})})}const pa=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:o,mode:r,anchorX:a,root:l})=>{const u=Ie(ga),c=v.useId();let h=!0,f=v.useMemo(()=>(h=!1,{id:c,initial:e,isPresent:n,custom:i,onExitComplete:d=>{u.set(d,!0);for(const m of u.values())if(!m)return;s&&s()},register:d=>(u.set(d,!1),()=>u.delete(d))}),[n,u,s]);return o&&h&&(f={...f}),v.useMemo(()=>{u.forEach((d,m)=>u.set(m,!1))},[n]),v.useEffect(()=>{!n&&!u.size&&s&&s()},[n]),r==="popLayout"&&(t=z.jsx(ma,{isPresent:n,anchorX:a,root:l,children:t})),z.jsx(Yt.Provider,{value:f,children:t})};function ga(){return new Map}function bi(t=!0){const e=v.useContext(Yt);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=v.useId();v.useEffect(()=>{if(t)return i(o)},[t]);const r=v.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Ot=t=>t.key||"";function Wn(t){const e=[];return v.Children.forEach(t,n=>{v.isValidElement(n)&&e.push(n)}),e}const nc=({children:t,custom:e,initial:n=!0,onExitComplete:s,presenceAffectsLayout:i=!0,mode:o="sync",propagate:r=!1,anchorX:a="left",root:l})=>{const[u,c]=bi(r),h=v.useMemo(()=>Wn(t),[t]),f=r&&!u?[]:h.map(Ot),d=v.useRef(!0),m=v.useRef(h),p=Ie(()=>new Map),[T,g]=v.useState(h),[x,y]=v.useState(h);Bs(()=>{d.current=!1,m.current=h;for(let b=0;b<x.length;b++){const V=Ot(x[b]);f.includes(V)?p.delete(V):p.get(V)!==!0&&p.set(V,!1)}},[x,f.length,f.join("-")]);const A=[];if(h!==T){let b=[...h];for(let V=0;V<x.length;V++){const w=x[V],L=Ot(w);f.includes(L)||(b.splice(V,0,w),A.push(w))}return o==="wait"&&A.length&&(b=A),y(Wn(b)),g(h),null}const{forceRender:P}=v.useContext(Be);return z.jsx(z.Fragment,{children:x.map(b=>{const V=Ot(b),w=r&&!u?!1:h===x||f.includes(V),L=()=>{if(p.has(V))p.set(V,!0);else return;let B=!0;p.forEach(q=>{q||(B=!1)}),B&&(P?.(),y(m.current),r&&c?.(),s&&s())};return z.jsx(pa,{isPresent:w,initial:!d.current||n?void 0:!1,custom:e,presenceAffectsLayout:i,mode:o,root:l,onExitComplete:w?void 0:L,anchorX:a,children:b},V)})})},Vi=v.createContext({strict:!1}),Kn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},dt={};for(const t in Kn)dt[t]={isEnabled:e=>Kn[t].some(n=>!!e[n])};function ya(t){for(const e in t)dt[e]={...dt[e],...t[e]}}const va=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function zt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||va.has(t)}let Ci=t=>!zt(t);function xa(t){typeof t=="function"&&(Ci=e=>e.startsWith("on")?!zt(e):t(e))}try{xa(require("@emotion/is-prop-valid").default)}catch{}function Ta(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ci(i)||n===!0&&zt(i)||!e&&!zt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function Pa(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const qt=v.createContext({});function Zt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Mt(t){return typeof t=="string"||Array.isArray(t)}const ln=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],un=["initial",...ln];function Jt(t){return Zt(t.animate)||un.some(e=>Mt(t[e]))}function Mi(t){return!!(Jt(t)||t.variants)}function Sa(t,e){if(Jt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Mt(n)?n:void 0,animate:Mt(s)?s:void 0}}return t.inherit!==!1?e:{}}function wa(t){const{initial:e,animate:n}=Sa(t,v.useContext(qt));return v.useMemo(()=>({initial:e,animate:n}),[$n(e),$n(n)])}function $n(t){return Array.isArray(t)?t.join(" "):t}const Aa=Symbol.for("motionComponentSymbol");function lt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function ba(t,e,n){return v.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):lt(n)&&(n.current=s))},[e])}const cn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Va="framerAppearId",Di="data-"+cn(Va),Ri=v.createContext({});function Ca(t,e,n,s,i){const{visualElement:o}=v.useContext(qt),r=v.useContext(Vi),a=v.useContext(Yt),l=v.useContext(an).reducedMotion,u=v.useRef(null);s=s||r.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,h=v.useContext(Ri);c&&!c.projection&&i&&(c.type==="html"||c.type==="svg")&&Ma(u.current,n,i,h);const f=v.useRef(!1);v.useInsertionEffect(()=>{c&&f.current&&c.update(n,a)});const d=n[Di],m=v.useRef(!!d&&!window.MotionHandoffIsComplete?.(d)&&window.MotionHasOptimisedAnimation?.(d));return Bs(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),rn.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),v.useEffect(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(d)}),m.current=!1))}),c}function Ma(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ei(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&lt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:c,layoutScroll:l,layoutRoot:u})}function Ei(t){if(t)return t.options.allowProjection!==!1?t.projection:Ei(t.parent)}function Da({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&ya(t);function o(a,l){let u;const c={...v.useContext(an),...a,layoutId:Ra(a)},{isStatic:h}=c,f=wa(a),d=s(a,h);if(!h&&je){Ea();const m=La(c);u=m.MeasureLayout,f.visualElement=Ca(i,d,c,e,m.ProjectionNode)}return z.jsxs(qt.Provider,{value:f,children:[u&&f.visualElement?z.jsx(u,{visualElement:f.visualElement,...c}):null,n(i,a,ba(d,f.visualElement,l),d,h,f.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const r=v.forwardRef(o);return r[Aa]=i,r}function Ra({layoutId:t}){const e=v.useContext(Be).id;return e&&t!==void 0?e+"-"+t:t}function Ea(t,e){v.useContext(Vi).strict}function La(t){const{drag:e,layout:n}=dt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Dt={};function ka(t){for(const e in t)Dt[e]=t[e],Ge(e)&&(Dt[e].isCSSVariable=!0)}function Li(t,{layout:e,layoutId:n}){return gt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Dt[t]||t==="opacity")}const Fa={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ba=pt.length;function Ia(t,e,n){let s="",i=!0;for(let o=0;o<Ba;o++){const r=pt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=Ti(a,sn[r]);if(!l){i=!1;const c=Fa[r]||r;s+=`${c}(${u}) `}n&&(e[r]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function hn(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const u=e[l];if(gt.has(l)){r=!0;continue}else if(Ge(l)){i[l]=u;continue}else{const c=Ti(u,sn[l]);l.startsWith("origin")?(a=!0,o[l]=c):s[l]=c}}if(e.transform||(r||n?s.transform=Ia(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=o;s.transformOrigin=`${l} ${u} ${c}`}}const fn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ki(t,e,n){for(const s in e)!F(e[s])&&!Li(s,n)&&(t[s]=e[s])}function ja({transformTemplate:t},e){return v.useMemo(()=>{const n=fn();return hn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Oa(t,e){const n=t.style||{},s={};return ki(s,n,t),Object.assign(s,ja(t,e)),s}function Na(t,e){const n={},s=Oa(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const Ua={offset:"stroke-dashoffset",array:"stroke-dasharray"},Wa={offset:"strokeDashoffset",array:"strokeDasharray"};function Ka(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Ua:Wa;t[o.offset]=S.transform(-s);const r=S.transform(e),a=S.transform(n);t[o.array]=`${r} ${a}`}function Fi(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(hn(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:f}=t;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=c?.transformBox??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&Ka(h,i,o,r,!1)}const Bi=()=>({...fn(),attrs:{}}),Ii=t=>typeof t=="string"&&t.toLowerCase()==="svg";function $a(t,e,n,s){const i=v.useMemo(()=>{const o=Bi();return Fi(o,e,Ii(s),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};ki(o,t.style,t),i.style={...o,...i.style}}return i}const _a=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function dn(t){return typeof t!="string"||t.includes("-")?!1:!!(_a.indexOf(t)>-1||/[A-Z]/u.test(t))}function Ga(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(dn(n)?$a:Na)(s,o,r,n),u=Ta(s,typeof n=="string",t),c=n!==v.Fragment?{...u,...l,ref:i}:{},{children:h}=s,f=v.useMemo(()=>F(h)?h.get():h,[h]);return v.createElement(n,{...c,children:f})}}function _n(t){const e=[{},{}];return t?.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function mn(t,e,n,s){if(typeof e=="function"){const[i,o]=_n(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=_n(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function Kt(t){return F(t)?t.get():t}function Ha({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:za(n,s,i,t),renderState:e()}}const ji=t=>(e,n)=>{const s=v.useContext(qt),i=v.useContext(Yt),o=()=>Ha(t,e,s,i);return n?o():Ie(o)};function za(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Kt(o[f]);let{initial:r,animate:a}=t;const l=Jt(t),u=Mi(t);e&&u&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let c=n?n.initial===!1:!1;c=c||r===!1;const h=c?a:r;if(h&&typeof h!="boolean"&&!Zt(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const m=mn(t,f[d]);if(m){const{transitionEnd:p,transition:T,...g}=m;for(const x in g){let y=g[x];if(Array.isArray(y)){const A=c?y.length-1:0;y=y[A]}y!==null&&(i[x]=y)}for(const x in p)i[x]=p[x]}}}return i}function pn(t,e,n){const{style:s}=t,i={};for(const o in s)(F(s[o])||e.style&&F(e.style[o])||Li(o,t)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=s[o]);return i}const Xa={useVisualState:ji({scrapeMotionValuesFromProps:pn,createRenderState:fn})};function Oi(t,e,n){const s=pn(t,e,n);for(const i in t)if(F(t[i])||F(e[i])){const o=pt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const Ya={useVisualState:ji({scrapeMotionValuesFromProps:Oi,createRenderState:Bi})};function qa(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...dn(s)?Ya:Xa,preloadedFeatures:t,useRender:Ga(i),createVisualElement:e,Component:s};return Da(r)}}function Rt(t,e,n){const s=t.getProps();return mn(s,e,n!==void 0?n:s.custom,t)}const Ce=t=>Array.isArray(t);function Za(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ft(n))}function Ja(t){return Ce(t)?t[t.length-1]||0:t}function Qa(t,e){const n=Rt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=Ja(o[r]);Za(t,r,a)}}function tl(t){return!!(F(t)&&t.add)}function Me(t,e){const n=t.getValue("willChange");if(tl(n))return n.add(e);if(!n&&Y.WillChange){const s=new Y.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Ni(t){return t.props[Di]}const el=t=>t!==null;function nl(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(el),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[o]}const sl={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),rl={type:"keyframes",duration:.8},ol={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},al=(t,{keyframes:e})=>e.length>2?rl:gt.has(t)?t.startsWith("scale")?il(e[1]):sl:ol;function ll({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const gn=(t,e,n,s={},i,o)=>r=>{const a=nn(s,t)||{},l=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-$(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};ll(a)||Object.assign(c,al(t,c)),c.duration&&(c.duration=$(c.duration)),c.repeatDelay&&(c.repeatDelay=$(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),(Y.instantAnimations||Y.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const f=nl(c.keyframes,a);if(f!==void 0){C.update(()=>{c.onUpdate(f),c.onComplete()});return}}return a.isSync?new Qe(c):new Ko(c)};function ul({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Ui(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const c in a){const h=t.getValue(c,t.latestValues[c]??null),f=a[c];if(f===void 0||u&&ul(u,c))continue;const d={delay:n,...nn(o||{},c)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const g=Ni(t);if(g){const x=window.MotionHandoffAnimation(g,c,C);x!==null&&(d.startTime=x,p=!0)}}Me(t,c),h.start(gn(c,h,f,t.shouldReduceMotion&&pi.has(c)?{type:!1}:d,t,p));const T=h.animation;T&&l.push(T)}return r&&Promise.all(l).then(()=>{C.update(()=>{r&&Qa(t,r)})}),l}function De(t,e,n={}){const s=Rt(t,e,n.type==="exit"?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(Ui(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:h}=i;return cl(t,e,l,u,c,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[o,r]:[r,o];return l().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function cl(t,e,n=0,s=0,i=0,o=1,r){const a=[],l=t.variantChildren.size,u=(l-1)*i,c=typeof s=="function",h=c?f=>s(f,l):o===1?(f=0)=>f*i:(f=0)=>u-f*i;return Array.from(t.variantChildren).sort(hl).forEach((f,d)=>{f.notify("AnimationStart",e),a.push(De(f,e,{...r,delay:n+(c?0:s)+h(d)}).then(()=>f.notify("AnimationComplete",e)))}),Promise.all(a)}function hl(t,e){return t.sortNodePosition(e)}function fl(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>De(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=De(t,e,n);else{const i=typeof e=="function"?Rt(t,e,n.custom):e;s=Promise.all(Ui(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Wi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const dl=un.length;function Ki(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Ki(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<dl;n++){const s=un[n],i=t.props[s];(Mt(i)||i===!1)&&(e[s]=i)}return e}const ml=[...ln].reverse(),pl=ln.length;function gl(t){return e=>Promise.all(e.map(({animation:n,options:s})=>fl(t,n,s)))}function yl(t){let e=gl(t),n=Gn(),s=!0;const i=l=>(u,c)=>{const h=Rt(t,c,l==="exit"?t.presenceContext?.custom:void 0);if(h){const{transition:f,transitionEnd:d,...m}=h;u={...u,...m,...d}}return u};function o(l){e=l(t)}function r(l){const{props:u}=t,c=Ki(t.parent)||{},h=[],f=new Set;let d={},m=1/0;for(let T=0;T<pl;T++){const g=ml[T],x=n[g],y=u[g]!==void 0?u[g]:c[g],A=Mt(y),P=g===l?x.isActive:null;P===!1&&(m=T);let b=y===c[g]&&y!==u[g]&&A;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...d},!x.isActive&&P===null||!y&&!x.prevProp||Zt(y)||typeof y=="boolean")continue;const V=vl(x.prevProp,y);let w=V||g===l&&x.isActive&&!b&&A||T>m&&A,L=!1;const B=Array.isArray(y)?y:[y];let q=B.reduce(i(g),{});P===!1&&(q={});const{prevResolvedValues:yn={}}=x,hr={...yn,...q},vn=I=>{w=!0,f.has(I)&&(L=!0,f.delete(I)),x.needsAnimating[I]=!0;const H=t.getValue(I);H&&(H.liveStyle=!1)};for(const I in hr){const H=q[I],Qt=yn[I];if(d.hasOwnProperty(I))continue;let te=!1;Ce(H)&&Ce(Qt)?te=!Wi(H,Qt):te=H!==Qt,te?H!=null?vn(I):f.add(I):H!==void 0&&f.has(I)?vn(I):x.protectedKeys[I]=!0}x.prevProp=y,x.prevResolvedValues=q,x.isActive&&(d={...d,...q}),s&&t.blockInitialAnimation&&(w=!1),w&&(!(b&&V)||L)&&h.push(...B.map(I=>({animation:I,options:{type:g}})))}if(f.size){const T={};if(typeof u.initial!="boolean"){const g=Rt(t,Array.isArray(u.initial)?u.initial[0]:u.initial);g&&g.transition&&(T.transition=g.transition)}f.forEach(g=>{const x=t.getBaseTarget(g),y=t.getValue(g);y&&(y.liveStyle=!0),T[g]=x??null}),h.push({animation:T})}let p=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(p=!1),s=!1,p?e(h):Promise.resolve()}function a(l,u){if(n[l].isActive===u)return Promise.resolve();t.variantChildren?.forEach(h=>h.animationState?.setActive(l,u)),n[l].isActive=u;const c=r(l);for(const h in n)n[h].protectedKeys={};return c}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Gn(),s=!0}}}function vl(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Wi(e,t):!1}function et(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Gn(){return{animate:et(!0),whileInView:et(),whileHover:et(),whileTap:et(),whileDrag:et(),whileFocus:et(),exit:et()}}class tt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class xl extends tt{constructor(e){super(e),e.animationState||(e.animationState=yl(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Zt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Tl=0;class Pl extends tt{constructor(){super(...arguments),this.id=Tl++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Sl={animation:{Feature:xl},exit:{Feature:Pl}};function Et(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Bt(t){return{point:{x:t.pageX,y:t.pageY}}}const wl=t=>e=>on(e)&&t(e,Bt(e));function St(t,e,n,s){return Et(t,e,wl(n),s)}function $i({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Al({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function bl(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const _i=1e-4,Vl=1-_i,Cl=1+_i,Gi=.01,Ml=0-Gi,Dl=0+Gi;function j(t){return t.max-t.min}function Rl(t,e,n){return Math.abs(t-e)<=n}function Hn(t,e,n,s=.5){t.origin=s,t.originPoint=M(e.min,e.max,t.origin),t.scale=j(n)/j(e),t.translate=M(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Vl&&t.scale<=Cl||isNaN(t.scale))&&(t.scale=1),(t.translate>=Ml&&t.translate<=Dl||isNaN(t.translate))&&(t.translate=0)}function wt(t,e,n,s){Hn(t.x,e.x,n.x,s?s.originX:void 0),Hn(t.y,e.y,n.y,s?s.originY:void 0)}function zn(t,e,n){t.min=n.min+e.min,t.max=t.min+j(e)}function El(t,e,n){zn(t.x,e.x,n.x),zn(t.y,e.y,n.y)}function Xn(t,e,n){t.min=e.min-n.min,t.max=t.min+j(e)}function At(t,e,n){Xn(t.x,e.x,n.x),Xn(t.y,e.y,n.y)}const Yn=()=>({translate:0,scale:1,origin:0,originPoint:0}),ut=()=>({x:Yn(),y:Yn()}),qn=()=>({min:0,max:0}),R=()=>({x:qn(),y:qn()});function U(t){return[t("x"),t("y")]}function le(t){return t===void 0||t===1}function Re({scale:t,scaleX:e,scaleY:n}){return!le(t)||!le(e)||!le(n)}function nt(t){return Re(t)||Hi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Hi(t){return Zn(t.x)||Zn(t.y)}function Zn(t){return t&&t!=="0%"}function Xt(t,e,n){const s=t-n,i=e*s;return n+i}function Jn(t,e,n,s,i){return i!==void 0&&(t=Xt(t,i,s)),Xt(t,n,s)+e}function Ee(t,e=0,n=1,s,i){t.min=Jn(t.min,e,n,s,i),t.max=Jn(t.max,e,n,s,i)}function zi(t,{x:e,y:n}){Ee(t.x,e.translate,e.scale,e.originPoint),Ee(t.y,n.translate,n.scale,n.originPoint)}const Qn=.999999999999,ts=1.0000000000001;function Ll(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ht(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,zi(t,r)),s&&nt(o.latestValues)&&ht(t,o.latestValues))}e.x<ts&&e.x>Qn&&(e.x=1),e.y<ts&&e.y>Qn&&(e.y=1)}function ct(t,e){t.min=t.min+e,t.max=t.max+e}function es(t,e,n,s,i=.5){const o=M(t.min,t.max,i);Ee(t,e,n,o,s)}function ht(t,e){es(t.x,e.x,e.scaleX,e.scale,e.originX),es(t.y,e.y,e.scaleY,e.scale,e.originY)}function Xi(t,e){return $i(bl(t.getBoundingClientRect(),e))}function kl(t,e,n){const s=Xi(t,n),{scroll:i}=e;return i&&(ct(s.x,i.offset.x),ct(s.y,i.offset.y)),s}const Yi=({current:t})=>t?t.ownerDocument.defaultView:null,ns=(t,e)=>Math.abs(t-e);function Fl(t,e){const n=ns(t.x,e.x),s=ns(t.y,e.y);return Math.sqrt(n**2+s**2)}class qi{constructor(e,n,{transformPagePoint:s,contextWindow:i=window,dragSnapToOrigin:o=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=ce(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,m=Fl(f.offset,{x:0,y:0})>=this.distanceThreshold;if(!d&&!m)return;const{point:p}=f,{timestamp:T}=k;this.history.push({...p,timestamp:T});const{onStart:g,onMove:x}=this.handlers;d||(g&&g(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),x&&x(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ue(d,this.transformPagePoint),C.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:m,onSessionEnd:p,resumeAnimation:T}=this.handlers;if(this.dragSnapToOrigin&&T&&T(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=ce(f.type==="pointercancel"?this.lastMoveEventInfo:ue(d,this.transformPagePoint),this.history);this.startEvent&&m&&m(f,g),p&&p(f,g)},!on(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.distanceThreshold=r,this.contextWindow=i||window;const a=Bt(e),l=ue(a,this.transformPagePoint),{point:u}=l,{timestamp:c}=k;this.history=[{...u,timestamp:c}];const{onSessionStart:h}=n;h&&h(e,ce(l,this.history)),this.removeListeners=Lt(St(this.contextWindow,"pointermove",this.handlePointerMove),St(this.contextWindow,"pointerup",this.handlePointerUp),St(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),J(this.updatePoint)}}function ue(t,e){return e?{point:e(t.point)}:t}function ss(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ce({point:t},e){return{point:t,delta:ss(t,Zi(e)),offset:ss(t,Bl(e)),velocity:Il(e,.1)}}function Bl(t){return t[0]}function Zi(t){return t[t.length-1]}function Il(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Zi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>$(e)));)n--;if(!s)return{x:0,y:0};const o=_(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function jl(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?M(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?M(n,t,s.max):Math.min(t,n)),t}function is(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Ol(t,{top:e,left:n,bottom:s,right:i}){return{x:is(t.x,n,i),y:is(t.y,e,s)}}function rs(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Nl(t,e){return{x:rs(t.x,e.x),y:rs(t.y,e.y)}}function Ul(t,e){let n=.5;const s=j(t),i=j(e);return i>s?n=bt(e.min,e.max-s,t.min):s>i&&(n=bt(t.min,t.max-i,e.min)),X(0,1,n)}function Wl(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Le=.35;function Kl(t=Le){return t===!1?t=0:t===!0&&(t=Le),{x:os(t,"left","right"),y:os(t,"top","bottom")}}function os(t,e,n){return{min:as(t,e),max:as(t,n)}}function as(t,e){return typeof t=="number"?t:t[e]||0}const $l=new WeakMap;class _l{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=R(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:n=!1,distanceThreshold:s}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Bt(h).point)},r=(h,f)=>{const{drag:d,dragPropagation:m,onDragStart:p}=this.getProps();if(d&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ia(d),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=f,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),U(g=>{let x=this.getAxisMotionValue(g).get()||0;if(G.test(x)){const{projection:y}=this.visualElement;if(y&&y.layout){const A=y.layout.layoutBox[g];A&&(x=j(A)*(parseFloat(x)/100))}}this.originPoint[g]=x}),p&&C.postRender(()=>p(h,f)),Me(this.visualElement,"transform");const{animationState:T}=this.visualElement;T&&T.setActive("whileDrag",!0)},a=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f;const{dragPropagation:d,dragDirectionLock:m,onDirectionLock:p,onDrag:T}=this.getProps();if(!d&&!this.openDragLock)return;const{offset:g}=f;if(m&&this.currentDirection===null){this.currentDirection=Gl(g),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",f.point,g),this.updateAxis("y",f.point,g),this.visualElement.render(),T&&T(h,f)},l=(h,f)=>{this.latestPointerEvent=h,this.latestPanInfo=f,this.stop(h,f),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>U(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:c}=this.getProps();this.panSession=new qi(e,{onSessionStart:o,onStart:r,onMove:a,onSessionEnd:l,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,distanceThreshold:s,contextWindow:Yi(this.visualElement)})}stop(e,n){const s=e||this.latestPointerEvent,i=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!i||!s)return;const{velocity:r}=i;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&C.postRender(()=>a(s,i))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Nt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=jl(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;e&&lt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Ol(s.layoutBox,e):this.constraints=!1,this.elastic=Kl(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&U(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Wl(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!lt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=kl(s,i.root,this.visualElement.getTransformPagePoint());let r=Nl(i.layout.layoutBox,o);if(n){const a=n(Al(r));this.hasMutatedConstraints=!!a,a&&(r=$i(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=U(c=>{if(!Nt(c,n,this.currentDirection))return;let h=l&&l[c]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[c]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(c,m)});return Promise.all(u).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Me(this.visualElement,e),s.start(gn(e,s,0,n,this.visualElement,!1))}stopAnimation(){U(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){U(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){U(n=>{const{drag:s}=this.getProps();if(!Nt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-M(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!lt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};U(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=Ul({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),U(r=>{if(!Nt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:u}=this.constraints[r];a.set(M(l,u,i[r]))})}addListeners(){if(!this.visualElement.current)return;$l.set(this.visualElement,this);const e=this.visualElement.current,n=St(e,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();lt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),C.read(s);const r=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(U(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=Le,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Nt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Gl(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Hl extends tt{constructor(e){super(e),this.removeGroupControls=W,this.removeListeners=W,this.controls=new _l(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}const ls=t=>(e,n)=>{t&&C.postRender(()=>t(e,n))};class zl extends tt{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(e){this.session=new qi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Yi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:ls(e),onStart:ls(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&C.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=St(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const $t={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function us(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const vt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(S.test(t))t=parseFloat(t);else return t;const n=us(t,e.target.x),s=us(t,e.target.y);return`${n}% ${s}%`}},Xl={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Q.parse(t);if(i.length>5)return s;const o=Q.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const u=M(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=u),typeof i[3+r]=="number"&&(i[3+r]/=u),o(i)}};let cs=!1;class Yl extends v.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;ka(ql),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),cs&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),$t.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,cs=!0,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||C.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ji(t){const[e,n]=bi(),s=v.useContext(Be);return z.jsx(Yl,{...t,layoutGroup:s,switchLayoutGroup:v.useContext(Ri),isPresent:e,safeToRemove:n})}const ql={borderRadius:{...vt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:vt,borderTopRightRadius:vt,borderBottomLeftRadius:vt,borderBottomRightRadius:vt,boxShadow:Xl};function Zl(t,e,n){const s=F(t)?t:ft(t);return s.start(gn("",s,e,n)),s.animation}const Jl=(t,e)=>t.depth-e.depth;class Ql{constructor(){this.children=[],this.isDirty=!1}add(e){Oe(this.children,e),this.isDirty=!0}remove(e){Ne(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Jl),this.isDirty=!1,this.children.forEach(e)}}function tu(t,e){const n=O.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(J(s),t(o-e))};return C.setup(s,!0),()=>J(s)}const Qi=["TopLeft","TopRight","BottomLeft","BottomRight"],eu=Qi.length,hs=t=>typeof t=="string"?parseFloat(t):t,fs=t=>typeof t=="number"||S.test(t);function nu(t,e,n,s,i,o){i?(t.opacity=M(0,n.opacity??1,su(s)),t.opacityExit=M(e.opacity??1,0,iu(s))):o&&(t.opacity=M(e.opacity??1,n.opacity??1,s));for(let r=0;r<eu;r++){const a=`border${Qi[r]}Radius`;let l=ds(e,a),u=ds(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||fs(l)===fs(u)?(t[a]=Math.max(M(hs(l),hs(u),s),0),(G.test(u)||G.test(l))&&(t[a]+="%")):t[a]=u}(e.rotate||n.rotate)&&(t.rotate=M(e.rotate||0,n.rotate||0,s))}function ds(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const su=tr(0,.5,Hs),iu=tr(.5,.95,W);function tr(t,e,n){return s=>s<t?0:s>e?1:n(bt(t,e,s))}function ms(t,e){t.min=e.min,t.max=e.max}function N(t,e){ms(t.x,e.x),ms(t.y,e.y)}function ps(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function gs(t,e,n,s,i){return t-=e,t=Xt(t,1/n,s),i!==void 0&&(t=Xt(t,1/i,s)),t}function ru(t,e=0,n=1,s=.5,i,o=t,r=t){if(G.test(e)&&(e=parseFloat(e),e=M(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=M(o.min,o.max,s);t===o&&(a-=e),t.min=gs(t.min,e,n,a,i),t.max=gs(t.max,e,n,a,i)}function ys(t,e,[n,s,i],o,r){ru(t,e[n],e[s],e[i],e.scale,o,r)}const ou=["x","scaleX","originX"],au=["y","scaleY","originY"];function vs(t,e,n,s){ys(t.x,e,ou,n?n.x:void 0,s?s.x:void 0),ys(t.y,e,au,n?n.y:void 0,s?s.y:void 0)}function xs(t){return t.translate===0&&t.scale===1}function er(t){return xs(t.x)&&xs(t.y)}function Ts(t,e){return t.min===e.min&&t.max===e.max}function lu(t,e){return Ts(t.x,e.x)&&Ts(t.y,e.y)}function Ps(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nr(t,e){return Ps(t.x,e.x)&&Ps(t.y,e.y)}function Ss(t){return j(t.x)/j(t.y)}function ws(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class uu{constructor(){this.members=[]}add(e){Oe(this.members,e),e.scheduleRender()}remove(e){if(Ne(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function cu(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:f,skewX:d,skewY:m}=n;u&&(s=`perspective(${u}px) ${s}`),c&&(s+=`rotate(${c}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const he=["","X","Y","Z"],hu=1e3;let fu=0;function fe(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ni(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",C,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&sr(s)}function ir({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e?.()){this.id=fu++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(pu),this.nodes.forEach(xu),this.nodes.forEach(Tu),this.nodes.forEach(gu)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ql)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new Ke),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Ai(r)&&!ca(r),this.instance=r;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let c,h=0;const f=()=>this.root.updateBlockedByResize=!1;C.read(()=>{h=window.innerWidth}),t(r,()=>{const d=window.innerWidth;d!==h&&(h=d,this.root.updateBlockedByResize=!0,c&&c(),c=tu(f,250),$t.hasAnimatedSinceResize&&($t.hasAnimatedSinceResize=!1,this.nodes.forEach(Vs)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||u.getDefaultTransition()||bu,{onLayoutAnimationStart:p,onLayoutAnimationComplete:T}=u.getProps(),g=!this.targetLayout||!nr(this.targetLayout,d),x=!h&&f;if(this.options.layoutRoot||this.resumeFrom||x||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const y={...nn(m,"layout"),onPlay:p,onComplete:T};(u.shouldReduceMotion||this.options.layoutRoot)&&(y.delay=0,y.type=!1),this.startAnimation(y),this.setAnimationOrigin(c,x)}else h||Vs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),J(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Pu),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&sr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(As);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(bs);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(vu),this.nodes.forEach(du),this.nodes.forEach(mu)):this.nodes.forEach(bs),this.clearAllSnapshots();const a=O.now();k.delta=X(0,1e3/60,a-k.timestamp),k.timestamp=a,k.isProcessing=!0,ne.update.process(k),ne.preRender.process(k),ne.render.process(k),k.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yu),this.sharedNodes.forEach(Su)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,C.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){C.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!j(this.snapshot.measuredBox.x)&&!j(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=R(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!er(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;r&&this.instance&&(a||nt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),Vu(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:r}=this.options;if(!r)return R();const a=r.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Cu))){const{scroll:u}=this.root;u&&(ct(a.x,u.offset.x),ct(a.y,u.offset.y))}return a}removeElementScroll(r){const a=R();if(N(a,r),this.scroll?.wasRoot)return a;for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:h}=u;u!==this.root&&c&&h.layoutScroll&&(c.wasRoot&&N(a,r),ct(a.x,c.offset.x),ct(a.y,c.offset.y))}return a}applyTransform(r,a=!1){const l=R();N(l,r);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&ht(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),nt(c.latestValues)&&ht(l,c.latestValues)}return nt(this.latestValues)&&ht(l,this.latestValues),l}removeTransform(r){const a=R();N(a,r);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!nt(u.latestValues))continue;Re(u.latestValues)&&u.updateSnapshot();const c=R(),h=u.measurePageBox();N(c,h),vs(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return nt(this.latestValues)&&vs(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==k.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:h}=this.options;if(!(!this.layout||!(c||h))){if(this.resolvedRelativeTargetAt=k.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=R(),this.targetWithTransforms=R()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),El(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):N(this.target,this.layout.layoutBox),zi(this.target,this.targetDelta)):N(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.target,f.target),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Re(this.parent.latestValues)||Hi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===k.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;N(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;Ll(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=R());const{target:d}=r;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ps(this.prevProjectionDelta.x,this.projectionDelta.x),ps(this.prevProjectionDelta.y,this.projectionDelta.y)),wt(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ws(this.projectionDelta.x,this.prevProjectionDelta.x)||!ws(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){if(this.options.visualElement?.scheduleRender(),r){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ut(),this.projectionDelta=ut(),this.projectionDeltaWithTransform=ut()}setAnimationOrigin(r,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=ut();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=R(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,T=this.getStack(),g=!T||T.members.length<=1,x=!!(p&&!g&&this.options.crossfade===!0&&!this.path.some(Au));this.animationProgress=0;let y;this.mixTargetDelta=A=>{const P=A/1e3;Cs(h.x,r.x,P),Cs(h.y,r.y,P),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(At(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),wu(this.relativeTarget,this.relativeTargetOrigin,f,P),y&&lu(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=R()),N(y,this.relativeTarget)),p&&(this.animationValues=c,nu(c,u,this.latestValues,P,x,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=P},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(J(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=C.update(()=>{$t.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ft(0)),this.currentAnimation=Zl(this.motionValue,[0,1e3],{...r,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hu),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=r;if(!(!a||!l||!u)){if(this!==r&&this.layout&&u&&rr(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||R();const h=j(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=j(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}N(a,l),ht(a,c),wt(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new uu),this.sharedNodes.get(r).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){const{layoutId:r}=this.options;return r?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:r}=this.options;return r?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&fe("z",r,u,this.animationValues);for(let c=0;c<he.length;c++)fe(`rotate${he[c]}`,r,u,this.animationValues),fe(`skew${he[c]}`,r,u,this.animationValues);r.render();for(const c in u)r.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);r.scheduleRender()}applyProjectionStyles(r,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){r.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,r.visibility="",r.opacity="",r.pointerEvents=Kt(a?.pointerEvents)||"",r.transform=l?l(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(r.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,r.pointerEvents=Kt(a?.pointerEvents)||""),this.hasProjected&&!nt(this.latestValues)&&(r.transform=l?l({},""):"none",this.hasProjected=!1);return}r.visibility="";const c=u.animationValues||u.latestValues;this.applyTransformsToTarget();let h=cu(this.projectionDeltaWithTransform,this.treeScale,c);l&&(h=l(c,h)),r.transform=h;const{x:f,y:d}=this.projectionDelta;r.transformOrigin=`${f.origin*100}% ${d.origin*100}% 0`,u.animationValues?r.opacity=u===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:r.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const m in Dt){if(c[m]===void 0)continue;const{correct:p,applyTo:T,isCSSVariable:g}=Dt[m],x=h==="none"?c[m]:p(c[m],u);if(T){const y=T.length;for(let A=0;A<y;A++)r[T[A]]=x}else g?this.options.visualElement.renderState.vars[m]=x:r[m]=x}this.options.layoutId&&(r.pointerEvents=u===this?Kt(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>r.currentAnimation?.stop()),this.root.nodes.forEach(As),this.root.sharedNodes.clear()}}}function du(t){t.updateLayout()}function mu(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:s}=t.layout,{animationType:i}=t.options,o=e.source!==t.layout.source;i==="size"?U(c=>{const h=o?e.measuredBox[c]:e.layoutBox[c],f=j(h);h.min=n[c].min,h.max=h.min+f}):rr(i,e.layoutBox,n)&&U(c=>{const h=o?e.measuredBox[c]:e.layoutBox[c],f=j(n[c]);h.max=h.min+f,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[c].max=t.relativeTarget[c].min+f)});const r=ut();wt(r,n,e.layoutBox);const a=ut();o?wt(a,t.applyTransform(s,!0),e.measuredBox):wt(a,n,e.layoutBox);const l=!er(r);let u=!1;if(!t.resumeFrom){const c=t.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:h,layout:f}=c;if(h&&f){const d=R();At(d,e.layoutBox,h.layoutBox);const m=R();At(m,n,f.layoutBox),nr(d,m)||(u=!0),c.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=d,t.relativeParent=c)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:n}=t.options;n&&n()}t.options.transition=void 0}function pu(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function gu(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function yu(t){t.clearSnapshot()}function As(t){t.clearMeasurements()}function bs(t){t.isLayoutDirty=!1}function vu(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Vs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function xu(t){t.resolveTargetDelta()}function Tu(t){t.calcProjection()}function Pu(t){t.resetSkewAndRotation()}function Su(t){t.removeLeadSnapshot()}function Cs(t,e,n){t.translate=M(e.translate,0,n),t.scale=M(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ms(t,e,n,s){t.min=M(e.min,n.min,s),t.max=M(e.max,n.max,s)}function wu(t,e,n,s){Ms(t.x,e.x,n.x,s),Ms(t.y,e.y,n.y,s)}function Au(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const bu={duration:.45,ease:[.4,0,.1,1]},Ds=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Rs=Ds("applewebkit/")&&!Ds("chrome/")?Math.round:W;function Es(t){t.min=Rs(t.min),t.max=Rs(t.max)}function Vu(t){Es(t.x),Es(t.y)}function rr(t,e,n){return t==="position"||t==="preserve-aspect"&&!Rl(Ss(e),Ss(n),.2)}function Cu(t){return t!==t.root&&t.scroll?.wasRoot}const Mu=ir({attachResizeListener:(t,e)=>Et(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),de={current:void 0},or=ir({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!de.current){const t=new Mu({});t.mount(window),t.setOptions({layoutScroll:!0}),de.current=t}return de.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Du={pan:{Feature:zl},drag:{Feature:Hl,ProjectionNode:or,MeasureLayout:Ji}};function Ls(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&C.postRender(()=>o(e,Bt(e)))}class Ru extends tt{mount(){const{current:e}=this.node;e&&(this.unmount=ra(e,(n,s)=>(Ls(this.node,s,"Start"),i=>Ls(this.node,i,"End"))))}unmount(){}}class Eu extends tt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Lt(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ks(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&C.postRender(()=>o(e,Bt(e)))}class Lu extends tt{mount(){const{current:e}=this.node;e&&(this.unmount=ua(e,(n,s)=>(ks(this.node,s,"Start"),(i,{success:o})=>ks(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ke=new WeakMap,me=new WeakMap,ku=t=>{const e=ke.get(t.target);e&&e(t)},Fu=t=>{t.forEach(ku)};function Bu({root:t,...e}){const n=t||document;me.has(n)||me.set(n,{});const s=me.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Fu,{root:t,...e})),s[i]}function Iu(t,e,n){const s=Bu(e);return ke.set(t,n),s.observe(t),()=>{ke.delete(t),s.unobserve(t)}}const ju={some:0,all:1};class Ou extends tt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:ju[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),f=u?c:h;f&&f(l)};return Iu(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Nu(e,n))&&this.startObserver()}unmount(){}}function Nu({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Uu={inView:{Feature:Ou},tap:{Feature:Lu},focus:{Feature:Eu},hover:{Feature:Ru}},Wu={layout:{ProjectionNode:or,MeasureLayout:Ji}},Fe={current:null},ar={current:!1};function Ku(){if(ar.current=!0,!!je)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Fe.current=t.matches;t.addEventListener("change",e),e()}else Fe.current=!1}const $u=new WeakMap;function _u(t,e,n){for(const s in e){const i=e[s],o=n[s];if(F(i))t.addValue(s,i);else if(F(o))t.addValue(s,ft(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,ft(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Fs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Gu{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=O.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,C.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Jt(n),this.isVariantNode=Mi(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&F(d)&&d.set(l[f],!1)}}mount(e){this.current=e,$u.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),ar.current||Ku(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Fe.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),J(this.notifyUpdate),J(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=gt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&C.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in dt){const n=dt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):R()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Fs.length;s++){const i=Fs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=_u(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ft(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Is(s)||Os(s))?s=parseFloat(s):!fa(s)&&Q.test(n)&&(s=xi(e,n)),this.setBaseTarget(e,F(s)?s.get():s)),F(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const o=mn(this.props,n,this.presenceContext?.custom);o&&(s=o[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!F(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Ke),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class lr extends Gu{constructor(){super(...arguments),this.KeyframeResolver=ta}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;F(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function ur(t,{style:e,vars:n},s,i){const o=t.style;let r;for(r in e)o[r]=e[r];i?.applyProjectionStyles(o,s);for(r in n)o.setProperty(r,n[r])}function Hu(t){return window.getComputedStyle(t)}class zu extends lr{constructor(){super(...arguments),this.type="html",this.renderInstance=ur}readValueFromInstance(e,n){if(gt.has(n))return this.projection?.isProjecting?Pe(n):xo(e,n);{const s=Hu(e),i=(Ge(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Xi(e,n)}build(e,n,s){hn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return pn(e,n,s)}}const cr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Xu(t,e,n,s){ur(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(cr.has(i)?i:cn(i),e.attrs[i])}class Yu extends lr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=R}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(gt.has(n)){const s=vi(n);return s&&s.default||0}return n=cr.has(n)?n:cn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Oi(e,n,s)}build(e,n,s){Fi(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Xu(e,n,s,i)}mount(e){this.isSVGTag=Ii(e.tagName),super.mount(e)}}const qu=(t,e)=>dn(t)?new Yu(e):new zu(e,{allowProjection:t!==v.Fragment}),Zu=qa({...Sl,...Uu,...Du,...Wu},qu),ic=Pa(Zu);export{nc as A,ec as R,z as j,ic as m,v as r};
