import{r as l,j as e,m as p}from"./animation-vendor-CTGg7XC5.js";import{C as n}from"./index-BJ7kZvEe.js";import{x as g,y as w,w as y,d as S,b as C,z as P}from"./ui-vendor-DuyO0ClB.js";import"./react-vendor-Csw2ODfV.js";import"./apollo-vendor-CuB5uN0C.js";const x={theme:"dark",notifications:{xpGains:!0,auditReminders:!0,projectDeadlines:!0,achievements:!0},dashboard:{compactMode:!1,showAnimations:!0,defaultTab:"dashboard",chartsEnabled:!0},privacy:{showProfile:!0,showProgress:!0,showStats:!0}},$=({userId:o,onClose:b})=>{const[c,d]=l.useState(x),[f,h]=l.useState(!1),[r,j]=l.useState("appearance");l.useEffect(()=>{const s=localStorage.getItem(`user-preferences-${o}`);if(s)try{const a=JSON.parse(s);d({...x,...a})}catch(a){console.error("Failed to parse preferences:",a)}},[o]);const i=(s,a,t)=>{d(m=>({...m,[s]:{...m[s],[a]:t}})),h(!0)},u=()=>{localStorage.setItem(`user-preferences-${o}`,JSON.stringify(c)),h(!1),console.log("Preferences saved:",c)},N=()=>{d(x),h(!0)},v=[{id:"appearance",label:"Appearance",icon:w},{id:"notifications",label:"Notifications",icon:y},{id:"dashboard",label:"Dashboard",icon:S},{id:"privacy",label:"Privacy",icon:C}];return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsxs(p.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsx("div",{className:"p-6 border-b border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-white",children:"User Preferences"}),e.jsx("p",{className:"text-white/60 mt-1",children:"Customize your dashboard experience"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[f&&e.jsxs(p.button,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},onClick:u,className:"flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors",children:[e.jsx(g,{className:"w-4 h-4 mr-2"}),"Save Changes"]}),e.jsx("button",{onClick:b,className:"text-white/60 hover:text-white transition-colors",children:"✕"})]})]})}),e.jsxs("div",{className:"flex h-[600px]",children:[e.jsxs("div",{className:"w-64 bg-gray-900/50 border-r border-gray-700 p-4",children:[e.jsx("nav",{className:"space-y-2",children:v.map(s=>{const a=s.icon,t=r===s.id;return e.jsxs("button",{onClick:()=>j(s.id),className:`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${t?"bg-primary-600 text-white":"text-white/70 hover:text-white hover:bg-white/10"}`,children:[e.jsx(a,{className:"w-5 h-5 mr-3"}),s.label]},s.id)})}),e.jsx("div",{className:"mt-8 pt-4 border-t border-gray-700",children:e.jsxs("button",{onClick:N,className:"w-full flex items-center px-3 py-2 text-white/60 hover:text-white transition-colors",children:[e.jsx(P,{className:"w-4 h-4 mr-3"}),"Reset to Defaults"]})})]}),e.jsxs("div",{className:"flex-1 p-6 overflow-y-auto",children:[r==="appearance"&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Appearance Settings"}),e.jsxs(n,{className:"p-4",children:[e.jsx("label",{className:"block text-white font-medium mb-3",children:"Theme"}),e.jsx("div",{className:"space-y-2",children:["dark","light","auto"].map(s=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"theme",value:s,checked:c.theme===s,onChange:a=>i("theme","theme",a.target.value),className:"mr-3"}),e.jsx("span",{className:"text-white/80 capitalize",children:s})]},s))})]})]}),r==="notifications"&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Notification Settings"}),e.jsx(n,{className:"p-4",children:e.jsx("div",{className:"space-y-4",children:Object.entries(c.notifications).map(([s,a])=>e.jsxs("label",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-white/80 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),e.jsx("input",{type:"checkbox",checked:a,onChange:t=>i("notifications",s,t.target.checked),className:"ml-3"})]},s))})})]}),r==="dashboard"&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Dashboard Settings"}),e.jsx(n,{className:"p-4",children:e.jsx("div",{className:"space-y-4",children:Object.entries(c.dashboard).map(([s,a])=>s==="defaultTab"?e.jsxs("div",{children:[e.jsx("label",{className:"block text-white/80 mb-2",children:"Default Tab"}),e.jsxs("select",{value:a,onChange:t=>i("dashboard",s,t.target.value),className:"w-full bg-gray-700 text-white rounded px-3 py-2",children:[e.jsx("option",{value:"dashboard",children:"Dashboard"}),e.jsx("option",{value:"leaderboard",children:"Leaderboard"}),e.jsx("option",{value:"search",children:"Search"}),e.jsx("option",{value:"export",children:"Export"})]})]},s):e.jsxs("label",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-white/80 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),e.jsx("input",{type:"checkbox",checked:a,onChange:t=>i("dashboard",s,t.target.checked),className:"ml-3"})]},s))})})]}),r==="privacy"&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Privacy Settings"}),e.jsx(n,{className:"p-4",children:e.jsx("div",{className:"space-y-4",children:Object.entries(c.privacy).map(([s,a])=>e.jsxs("label",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-white/80 capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),e.jsx("input",{type:"checkbox",checked:a,onChange:t=>i("privacy",s,t.target.checked),className:"ml-3"})]},s))})})]})]})]})]})})};export{$ as default};
