graph TB
    %% Main System Groups
    subgraph Events["🕒 Events System"]
        E[event]
        R[registration]
        RU[registration_user]
        EU[event_user]
    end
    
    subgraph Objects["📚 Objects System"]
        O[object]
        OC[object_child]
    end
    
    subgraph Users["👥 Users System"]
        U[user]
        RL[role]
        UR[user_role]
        G[group]
        GU[group_user]
        T[transaction]
        RC[record]
    end
    
    subgraph Results["📊 Results System"]
        P[progress]
        RS[result]
        A[audit]
        M[match]
    end
    
    %% Key Relationships
    E --> R
    E --> O
    E --> EU
    R --> RU
    O --> OC
    U --> UR
    U --> GU
    U --> EU
    U --> RU
    RL --> UR
    G --> GU
    U --> P
    U --> RS
    U --> A
    U --> M
    G --> A
    O --> P
    O --> RS
    E --> P
    E --> RS
    
    %% Styling
    classDef eventStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef objectStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef userStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef resultStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class E,R,RU,EU eventStyle
    class O,OC objectStyle
    class U,RL,UR,G,GU,T,RC userStyle
    class P,RS,A,M resultStyle