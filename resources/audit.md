#### Functional

##### Try to log in with invalid credentials

###### Is an appropriate error shown?

##### Ask the student to login with valid credentials

###### Does the profile page consist of three sections as required?

##### Verify the accuracy of the content/information in the three Sections using **GraphiQL**.

###### Are the details presented in these sections accurate and correspond to the expected data?

###### Does the profile include a fourth section dedicated to graphical statistics?

###### Does this section contain at least two different graphs created using SVG as specified in the project requirements?

##### Try to validate the accuracy of the information presented in the graphs.

###### Do the graphs display the expected data accurately?

##### Try to access the profile from the host domain.

###### Is the profile successfully accessible and hosted online?

##### Log out

###### Is the logout functionality successful in logging the authenticated user out?

#### General

###### Does the project have at least the mandatory queries (_nested_, _normal_ and using _arguments_)?

#### Bonus

###### +Does the profile showcase additional information beyond the three mandatory sections?

###### +Are there additional graphs featured apart from the required two?

###### +Has the student created and utilized their own GraphiQL?

###### +Does the UI respect the [good practices](../../good-practices/README.md)?