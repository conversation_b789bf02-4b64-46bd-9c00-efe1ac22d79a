.LoginPage {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.ring {
    position: relative;
    width: 500px;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ring i {
    position: absolute;
    inset: 0;
    border: 2px solid #B9B4C7;
    transition: 0.5s;
}

.ring i:nth-child(1) {
    border-radius: 38% 62% 63% 37% / 41% 44% 56% 59%;
    animation: animate 6s linear infinite;
}

.ring i:nth-child(2) {
    border-radius: 41% 44% 56% 59% / 38% 62% 63% 37%;
    animation: animate 4s linear infinite;
}

.ring i:nth-child(3) {
    border-radius: 41% 44% 56% 59% / 38% 62% 63% 37%;
    animation: animate2 10s linear infinite;
}

.ring:hover i {
    border: 6px solid var(--clr);
    filter: drop-shadow(0 0 20px var(--clr));
}

@keyframes animate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes animate2 {
    0% {
        transform: rotate(360deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

.login {
    position: absolute;
    width: 300px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 20px;
}

.login h2 {
    font-size: 2em;
    color: #B9B4C7;
}

.inputBx {
    margin-bottom: 20px;
}

.inputBx input[type="text"],
.inputBx input[type="password"],
.inputBx input[type="submit"] {
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    font-size: 16px;
}

.login .inputBx input {
    position: relative;
    width: 100%;
    padding: 12px 20px;
    background: transparent;
    border: 2px solid #B9B4C7;
    border-radius: 40px;
    font-size: 1.2em;
    color: #fff;
    box-shadow: none;
    outline: none;
}

.login .inputBx input[type="submit"] {
    width: 100%;
    background: #0078ff;
    background: linear-gradient(45deg, #4070f4, #bc68ec);
    border: none;
    cursor: pointer;
}

.login .inputBx input::placeholder {
    color: rgba(255, 255, 255, 0.75);
}

.ErrorOutput {
    display: block;
    color: rgb(0, 238, 255);
    font-size: 14px;
    margin-top: 1px;
    text-align: center;
}

.login .inputBx input[type="submit"]:hover {
    border: 2px solid #B9B4C7;
    background: transparent;
}

@media (min-width: 1024px) {
    .LoginPage {
        padding: 20px;
    }
}

@media (max-width: 1024px) {
    .LoginPage {
        padding: 15px;
    }

    .ring {
        width: 500px;
        height: 500px;
    }

    .login {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .LoginPage {
        flex-direction: column;
        padding: 10px;
    }

    .ring {
        width: 400px;
        height: 400px;
        margin-bottom: 20px;
    }

    .login {
        width: 250px;
    }

    .login h2 {
        font-size: 1.8em;
    }

    .login .inputBx input {
        font-size: 1em;
        padding: 10px 15px;
    }

    .ErrorOutput {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .LoginPage {
        padding: 5px;
    }

    .ring {
        width: 300px;
        height: 300px;
        margin-bottom: 15px;
    }

    .login {
        width: 200px;
    }

    .login h2 {
        font-size: 1.5em;
    }

    .login .inputBx input {
        font-size: 0.9em;
        padding: 8px 12px;
    }

    .ErrorOutput {
        font-size: 10px;
    }
}