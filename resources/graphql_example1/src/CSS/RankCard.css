.rank-card {
    height: 410px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    background-color: #1c1c1e;
    padding: 20px;
    border-radius: 10px;
    width: 320px;
    margin: 0 auto;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    font-family: Arial, sans-serif;
    color: #333;
}

.current-rank {
    color: #B9B4C7;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.next-rank {
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;
}

.level-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    color: #6200ea;
}

.level-circle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 120px;
    background-color: #1c1c1e;
    border-radius: 50%;
    border: 6px solid #bc68ec;
    font-size: 20px;
    color: #bc68ec;
    font-weight: bold;
    position: relative;
    box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.rank-card button {
    padding: 10px 15px;
    background: linear-gradient(45deg, #4070f4, #bc68ec);
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.rank-card button:hover {
    border: 2px solid #ffffff;
    background: transparent;
}

.timeline {
    position: relative;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.timeline-step {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.timeline-step.current .timeline-content {
    background-color: #bc68ec;
    color: #fff;
    border: 2px solid #bc68ec;
}

.timeline-content {
    background-color: #f3f3f3;
    padding: 10px 20px;
    border-radius: 10px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    max-width: 350px;
    width: 100%;
    text-align: center;
}

.timeline-line {
    width: 2px;
    height: 30px;
    background-color: #bc68ec;
    margin: 0 auto;
}

.popup-card {
    background-color: #FAF0E6;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 80%;
    overflow-y: auto;
}

.popup-card h3 {
    margin-top: 0;
    text-align: center;
}

.popup-card button {
    margin-top: 15px;
    padding: 8px 12px;
    background-color: #bc68ec;
    color: #fff;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}