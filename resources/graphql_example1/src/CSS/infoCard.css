.welcome-message {
    font-size: 30px;
    font-weight: bold;
    color: #B9B4C7;
}

#welcome_info {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.wrapper {
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    padding: 16px;
    background: #1c1c1e;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.left {
    flex: 1.2;
    text-align: left;
}

.left h4 {
    font-size: 1.2rem;
    margin: 8px 0;
    color: #6200ea;
}

.left p {
    margin: 4px 0;
    font-size: 1rem;
    color: #B9B4C7;
}

.right {
    flex: 1;
}

.info h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    border-bottom: 2px solid #6200ea;
    padding-bottom: 4px;
    color: #B9B4C7;
}

.info_data {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.info_data div {
    flex: 1 1 45%;
    background: #B9B4C7;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #B9B4C7;
}

.logout-button {
    padding: 2px 7px;
    background-color: #ff0000;
    color: #B9B4C7;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, transform 0.2s ease-in-out;
    border-radius: 5px;
}

.logout-button i {
    margin-right: 8px;
}

.logout-button:hover {
    background-color: #d10000;
    transform: scale(1.05);
}

.logout-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}