:root {
  --primary-bg: #000000;
  --secondary-bg: #333333;
  --accent: #FFD700;
  --text-primary: #FFD700;
  --text-secondary: #CCCCCC;
  --hover-color: #B8860B;
  --border-color: #444444;
  --accent-hover: #FFC107;
}

/* General styles */
body {
  font-family: 'Montserrat', sans-serif;
  background: var(--primary-bg);
  margin: 0;
  padding: 0;
  color: var(--text-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header styles */
header, footer {
  background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
  color: var(--accent);
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(255, 215, 0, 0.1);
}

footer button {
  all: unset; /* Removes all default styles */
  cursor: pointer; /* Ensures it still looks clickable */
}


/* Main content styles */
main {
  background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
  border-radius: 10px;
  padding: 30px;
  margin-top: 30px;
  box-shadow: 0 10px 20px rgba(255, 215, 0, 0.05);
}

/* Sign-in form styles */
#signinForm {
  display: flex;
  flex-direction: column;
  max-width: 300px;
  margin: 0 auto;
}

#signinForm label {
  margin-top: 15px;
  font-weight: 500;
  color: var(--text-secondary);
}

#signinForm input {
  padding: 10px;
  margin-top: 5px;
  border: 1px solid var(--secondary-bg);
  border-radius: 5px;
  background-color: var(--primary-bg);
  color: var(--text-primary);
}

#signinForm input:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

#signinForm button {
  margin-top: 20px;
  padding: 12px;
  background: linear-gradient(135deg, var(--accent) 0%, var(--hover-color) 100%);
  color: var(--primary-bg);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
}

#signinForm button:hover {
  background: linear-gradient(135deg, var(--hover-color) 0%, var(--accent) 100%);
  box-shadow: 0 4px 6px rgba(255, 215, 0, 0.1);
}

/* Signed-in content styles */
.flexContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.section {
  flex: 1;
  min-width: 250px;
  background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 10px 20px rgba(255, 215, 0, 0.05);
  position: relative;
  overflow: hidden;
  background-color: #1a1a1a;  /* Very dark gray, almost black */
  color: #ffffff;  /* White text for other elements */
}

.title {
  font-size: 1.4em;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffd700;  /* Gold color for the title */
  border-bottom: 2px solid #ffd700;  /* Gold underline */
  padding-bottom: 10px;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.namelvlrank {
  margin-bottom: 15px;
}

.name {
  font-weight: 600;
  color: var(--text-primary);
}

.info {
  font-size: 0.95em;
  line-height: 1.6;
  color: var(--text-secondary);
}

.users-list-container {
  max-height: 400px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
}

.users-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.users-list li {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.users-list li:last-child {
  border-bottom: none;
}

/* Webkit browsers like Chrome, Safari */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--primary-bg);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-hover);
}

/* Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--accent) var(--primary-bg);
}

/* Custom scrollbar styles for all scrollable elements */
.scrollable-element {
  scrollbar-width: thin;
  scrollbar-color: var(--accent) var(--primary-bg);
}

.scrollable-element::-webkit-scrollbar {
  width: 10px;
}

.scrollable-element::-webkit-scrollbar-track {
  background: var(--primary-bg);
  border-radius: 5px;
}

.scrollable-element::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 5px;
}

.scrollable-element::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Footer styles */
footer {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
  color: var(--accent);
  margin-top: 30px;
  font-size: 0.9em;
}

/* D3.js chart styles */
.bar-background {
  fill: url(#bg-gradient);
}

.bar.up {
  fill: url(#up-gradient);
}

.label {
  fill: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  filter: url(#glow);
}

.label.up {
  fill: var(--text-primary);
}

.ratio-text {
  fill: var(--text-secondary);
  font-size: 14px;
  font-weight: 600;
  filter: url(#glow);
}

/* New styles for the timeline section */
.section svg {
  background-color: var(--secondary-bg);
  border-radius: 8px;
  position: relative;
}

.section svg text {
  fill: var(--text-primary);
}

.section svg line,
.section svg path {
  stroke: var(--text-secondary);
}

/* New styles for the rankings section */
.section button {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: var(--accent);
  color: var(--primary-bg);
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.section ul {
  margin-top: 10px;
  padding-left: 20px;
}

.section li {
  margin-bottom: 5px;
}

.info ul {
  margin-top: 10px;
  padding-left: 20px;
  max-height: 400px;
}

.info li strong {
  color: var(--accent);
}

.info button {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 5px 10px;
  background-color: var(--accent);
  color: var(--primary-bg);
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.info ul {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.info ul.show {
  max-height: 100000px;
  transition: max-height 0.5s ease-in;
  max-height: 400px;
  overflow-y: auto;
}

/* Section title styles */
.section-title {
  text-align: left;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--accent);
  margin-bottom: 10px;
}

.section-title-underlined {
  border-bottom: 2px solid var(--accent);
  padding-bottom: 5px;
}

/* Tooltip styles */
.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 16px;
  pointer-events: none;
}

/* Error message styles */
.error-message {
  color: #ff4444;
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(255, 68, 68, 0.1);
  text-align: center;
}

/* Team Grid styles */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
    margin-top: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.team-card {
    background: linear-gradient(145deg, var(--secondary-bg), var(--primary-bg));
    border: 2px solid var(--accent);
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
    border-color: var(--accent-hover);
}

.team-card h3 {
    color: var(--accent);
    margin: 0 0 15px 0;
    font-size: 1.2em;
}

.team-members {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.team-member {
    color: var(--text-secondary);
    padding: 5px;
    border-radius: 5px;
    background: rgba(255, 215, 0, 0.1);
    transition: all 0.2s ease;
}

.team-member:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: translateX(5px);
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }

/* Form element styles */
.form-element {
  margin: 10px 0; /* Space between elements */
  padding: 8px; /* Padding inside elements */
  width: 100%; /* Full width for better alignment */
  box-sizing: border-box; /* Include padding in width */
}

.button {
  background-color: gold; /* Changed to gold */
  color: black; /* Text color */
  border: none; /* No border */
  padding: 10px 15px; /* Padding for button */
  cursor: pointer; /* Pointer cursor on hover */
}

@media (max-width: 400px) {
  .section {
    min-width: 95px;
  }
}

@media screen and (max-width: 500px) {
    .teams-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 10px;
        margin-top: 15px;
        max-height: 70vh;
    }

    .team-card {
        padding: 15px;
        font-size: 0.9em;
    }

    .team-card h3 {
        font-size: 1.1em;
        margin-bottom: 10px;
    }

    .team-members {
        font-size: 0.85em;
    }
}
