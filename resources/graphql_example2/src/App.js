import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as d3 from 'd3';
import './App.css';

function App() {
    const [jwt, setJwt] = useState("");
    const [userInfo, setUserInfo] = useState(null);
    const [userLevel, setUserLevel] = useState(null);
    const [userXP, setUserXP] = useState(null);
    const [isSignedIn, setIsSignedIn] = useState(false);
    const [headerMessage, setHeaderMessage] = useState("Please trust me and use your reboot credentials thank you");
    const [footerToggle, setFooterToggle] = useState(false);
    const [skillsData, setSkillsData] = useState([]);
    const skillsChartRef = useRef(null);
    const [usersAboveLevel, setUsersAboveLevel] = useState(null);
    const [usersAboveLevelInCohort, setUsersAboveLevelInCohort] = useState(null);
    const [cohort, setCohort] = useState(null);
    const [cohortRank, setCohortRank] = useState(null);
    const [showUsersAbove, setShowUsersAbove] = useState(false);
    const [showUsersAboveInCohort, setShowUsersAboveInCohort] = useState(false);
    const [usersAboveList, setUsersAboveList] = useState([]);
    const [usersAboveInCohortList, setUsersAboveInCohortList] = useState([]);
    const [leadershipCount, setLeadershipCount] = useState(null);
    const [leadershipProjects, setLeadershipProjects] = useState([]);
    const [showLeadershipProjects, setShowLeadershipProjects] = useState(false);
    const [teamLeaderProjects, setTeamLeaderProjects] = useState([]);
    const [showTeamLeaderProjects, setShowTeamLeaderProjects] = useState(false);
    const [mostFrequentLeader, setMostFrequentLeader] = useState(null);
    const [auditRatioRanking, setAuditRatioRanking] = useState([]);
    const [cohortAuditRatioRanking, setCohortAuditRatioRanking] = useState([]);
    const [showAuditRatioRanking, setShowAuditRatioRanking] = useState(false);
    const [showCohortAuditRatioRanking, setShowCohortAuditRatioRanking] = useState(false);
    const [userAuditRatioRank, setUserAuditRatioRank] = useState(null);
    const [userCohortAuditRatioRank, setUserCohortAuditRatioRank] = useState(null);
    const [xpForProjects, setXpForProjects] = useState([]);
    const [error, setError] = useState(null);
    const [selectedEventId, setSelectedEventId] = useState(72);
    const [searchPath, setSearchPath] = useState("");
    const [groupStatus, setGroupStatus] = useState("working");
    const [groupSearchResults, setGroupSearchResults] = useState([]);
    const [hasSearched, setHasSearched] = useState(false);

    const queries = {
        basicInformation: `query User1 {
      user {
        auditRatio
        firstName
        lastName
        totalDown
        totalUp
      }
    }`,
        skillsDistribution: `
      query skills {
        transaction(
          where: {
            type: {
              _iregex: "(^|[^[:alnum:]_])[[:alnum:]_]*skill_[[:alnum:]_]*($|[^[:alnum:]_])"
            }
          }
        ) {
          amount
          type
        }
      }
    `,
        userInfo: `
      query UserInfo {
        user {
          login
          firstName
          lastName
          auditRatio
          totalUp
          totalDown
        }
      }
    `,
        userLevel: `
      query UserDetails($userLogin: String!) {
        event_user(
          where: { event: { path: { _eq: "/bahrain/bh-module" } }, userLogin: { _eq: $userLogin } }
        ) {
          userLogin
          level
          event {
            id
          }
        }
      }
    `,
        userXP: `query {
      transaction_aggregate(
        where: {
          event: { path: { _eq: "/bahrain/bh-module" } }
          type: { _eq: "xp" }
        }
      ) {
        aggregate {
          sum {
            amount
          }
        }
      }
    }
    `,
        usersAboveInAllReboot: `
      query UsersLevelGreaterThanInAll($level: Int!) {
        event_user(
          where: { 
            event: { 
              path: { _eq: "/bahrain/bh-module" }
            },
            level: { _gte: $level }
          }
          order_by: { level: desc }
        ) {
          userLogin
          level
          event {
            campus
            id
          }
        }
      }
    `,
        usersAboveInCohort: `
      query UsersLevelGreaterThanInCohort($level: Int!, $eventId: Int!) {
        event_user(
          where: { 
            event: { 
              path: { _eq: "/bahrain/bh-module" },
              id: { _eq: $eventId }
            },
            level: { _gte: $level }
          }
          order_by: { level: desc }
        ) {
          userLogin
          level
          event {
            campus
            id
          }
        }
      }
    `,
        leadershipCount: `
      query LeadershipCount($userLogin: String!) {
        group_aggregate(where: { 
          _and: [
            { captainLogin: { _eq: $userLogin } },
            { object: { type: { _eq: "project" } } },
            { status: { _eq: finished } }
          ] 
        }) {
          aggregate {
            count
          }
        }
      }
    `,
        leadershipProjects: `
      query LeadershipProjects($userLogin: String!) {
        group(where: { 
          _and: [
            { captainLogin: { _eq: $userLogin } },
            { object: { type: { _eq: "project" } } },
            { status: { _eq: finished } }
          ] 
        }) {
          members {
            userLogin
          }
          object {
            name
          }
          path
        }
      }
    `,
        teamLeaders: `
      query TeamLeaders($userLogin: String!) {
        group(where: { 
          _and: [
            { captainLogin: { _neq: $userLogin } },
            { object: { type: { _eq: "project" } } },
            { status: { _eq: finished } },
            { members: { userLogin: {_eq: $userLogin} } },
          ] 
        }) {
          captainLogin
          object {
            name
          }
          members {
            userLogin
          }
        }
      }
    `,
        usersLevelGreaterThanInAll: `
        query UsersLevelGreaterThanInAll($level: Int!) {
          event_user(
            where: { 
              event: { 
                path: { _eq: "/bahrain/bh-module" }
              },
              level: { _gte: $level }
            }
            order_by: { level: desc }
          ) {
            userLogin
            level
            event {
              campus
              id
            }
            userAuditRatio
          }
        }
      `, xpForProjects: `
      query Transaction($userLogin: String!) {
  transaction(
    where: { 
      userLogin: { _eq: $userLogin }, 
      type: { _eq: "xp" }, 
      path: { _regex: "^(?!.*(piscine|checkpoint|check-in|bh-onboarding)).*$" } 
    }
    order_by: { amount: desc }
    limit: 10
  ) {
    amount
    createdAt
    path
  }
}

`, groupSearch: `query GroupSearch($eventId: Int!, $pathSearch: String!, $status: group_status_enum!) {
          group(
            where: { 
              eventId: { _eq: $eventId }, 
              path: { _like: $pathSearch }, 
              status: { _eq: $status }
            },
            order_by: [
              { status: asc }, 
              { updatedAt: desc }
            ]
          ) {
            status
            path
            members {
              userLogin
              user {
                firstName
                lastName
              }
            }
          }
        }`
    };

    const parseCookies = () => {
        return document.cookie.split('; ').reduce((acc, cookie) => {
            const [key, value] = cookie.split('=');
            if (key) {
                acc[key] = value;
            }
            return acc;
        }, {});
    };


    const handleLogout = () => {
        document.cookie = "jwt=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        setJwt("");
        setIsSignedIn(false);
        setUserInfo(null);
        setUserLevel(null);
        setUserXP(null);
        setSkillsData([]);
        setError(null); // Clear error message

        setUsersAboveLevel(null);
        setUsersAboveLevelInCohort(null);
        setCohort(null);
        setCohortRank(null);
        setUsersAboveList([]);
        setUsersAboveInCohortList([]);
        setLeadershipCount(null);
        setLeadershipProjects([]);
        setTeamLeaderProjects([]);
        setMostFrequentLeader(null);
        setAuditRatioRanking([]);
        setCohortAuditRatioRanking([]);
        setUserAuditRatioRank(null);
        setUserCohortAuditRatioRank(null);
        setHeaderMessage("Please trust me and use your reboot credentials thank you");
        setFooterToggle(false);
    };

    const handleSignIn = async (event) => {
        event.preventDefault();
        setError(null); // Clear previous errors
        const username = event.target.login.value;
        const password = event.target.password.value;

        const credentials = btoa(`${username}:${password}`);

        try {
            const response = await fetch('https://learn.reboot01.com/api/auth/signin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Basic ${credentials}`
                },
                body: JSON.stringify({})
            });

            if (response.ok) {
                const data = await response.json();
                setJwt(data);
                setIsSignedIn(true);
                setFooterToggle(true);
                setHeaderMessage("You shouldn't share your password with strangers.");

                // Set cookie
                const expirationDate = new Date();
                expirationDate.setDate(expirationDate.getDate() + 7); // Cookie expires in 7 days
                document.cookie = `jwt=${data}; expires=${expirationDate.toUTCString()}; path=/`;

                const result = await queryData(queries.basicInformation);
                setUserInfo({
                    ...userInfo,
                    firstAndLastName: `${result.data.user[0].firstName} ${result.data.user[0].lastName}`
                });
                setHeaderMessage(userInfo?.firstAndLastName + headerMessage);
            } else {
                setError('Invalid username or password');
            }
        } catch (error) {
            setError('Something went wrong. Please try again.');
        }
    };

    const queryData = useCallback(async (query, variables = {}) => {
        try {
            const response = await fetch('https://learn.reboot01.com/api/graphql-engine/v1/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${jwt}`
                },
                body: JSON.stringify({ query, variables })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('GraphQL response:', result);
                return result;
            } else {
                const errorData = await response.json();
                console.error('GraphQL query failed:', errorData.message || response.statusText);
            }
        } catch (error) {
            console.error('Error:', error);
        }
    }, [jwt]);

    const fetchSkillsData = useCallback(async () => {
        const result = await queryData(queries.skillsDistribution);
        console.log('Raw query result:', result);

        if (result && result.data && result.data.transaction) {
            const skillsMap = result.data.transaction.reduce((acc, item) => {
                const skill = item.type.replace(/^skill_/, '');
                // Use Math.max to keep the highest amount for each skill
                acc[skill] = Math.max(acc[skill] || 0, item.amount);
                return acc;
            }, {});

            const processedData = Object.entries(skillsMap)
                .map(([skill, amount]) => ({skill, amount}))
                .sort((a, b) => b.amount - a.amount);

            console.log('Processed skills data with maximum values:', processedData);
            setSkillsData(processedData);
        } else {
            console.log('No transaction data found in the query result');
            setSkillsData([]);
        }
    }, [queryData, queries.skillsDistribution]);

    const fetchUserData = useCallback(async () => {
        try {
            // Fetch user info first
            const infoResult = await queryData(queries.userInfo);
            if (infoResult && infoResult.data && infoResult.data.user && infoResult.data.user[0]) {
                const userInfo = infoResult.data.user[0];
                setUserInfo(userInfo);

                // Now that we have the user's login, fetch the level and cohort
                const levelResult = await queryData(queries.userLevel, {userLogin: userInfo.login});
                if (levelResult && levelResult.data && levelResult.data.event_user && levelResult.data.event_user[0]) {
                    const level = levelResult.data.event_user[0].level;
                    const eventId = levelResult.data.event_user[0].event.id;
                    setUserLevel(level);
                    setCohort(eventId);

                    // Count users above this level in all of Reboot01
                    const countAllResult = await queryData(queries.usersAboveInAllReboot, {level: level});
                    if (countAllResult && countAllResult.data && countAllResult.data.event_user) {
                        setUsersAboveLevel(countAllResult.data.event_user.length);
                    } else {
                        console.error('Failed to fetch count of users above level in all of Reboot01');
                        setUsersAboveLevel(null);
                    }

                    // Count users above this level in the same cohort
                    const countCohortResult = await queryData(queries.usersAboveInCohort, {
                        level: level,
                        eventId: eventId
                    });
                    if (countCohortResult && countCohortResult.data && countCohortResult.data.event_user) {
                        setUsersAboveLevelInCohort(countCohortResult.data.event_user.length);
                    } else {
                        console.error('Failed to fetch count of users above level in cohort');
                        setUsersAboveLevelInCohort(null);
                    }

                    // Fetch users in the same cohort with level >= user's level
                    const cohortResult = await queryData(queries.usersAboveInCohort, {
                        eventId: eventId,
                        level: level
                    });
                    if (cohortResult && cohortResult.data && cohortResult.data.event_user) {
                        const rank = cohortResult.data.event_user.findIndex(user => user.userLogin === userInfo.login) + 1;
                        setCohortRank(rank);
                    } else {
                        console.error('Failed to fetch cohort ranking');
                        setCohortRank(null);
                    }
                } else {
                    console.error('Failed to fetch user level');
                    setUserLevel(null);
                    setCohort(null);
                }

                // Fetch XP
                const xpResult = await queryData(queries.userXP);
                if (xpResult && xpResult.data && xpResult.data.transaction_aggregate) {
                    setUserXP(xpResult.data.transaction_aggregate.aggregate.sum.amount);
                } else {
                    console.error('Failed to fetch user XP');
                    setUserXP(null);
                }

                // Fetch leadership count
                const leadershipResult = await queryData(queries.leadershipCount, {userLogin: userInfo.login});
                if (leadershipResult && leadershipResult.data && leadershipResult.data.group_aggregate) {
                    setLeadershipCount(leadershipResult.data.group_aggregate.aggregate.count);
                } else {
                    console.error('Failed to fetch leadership count');
                    setLeadershipCount(null);
                }
            } else {
                console.error('Failed to fetch user info');
                handleLogout();
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
        }
    }, [queryData, queries.userInfo, queries.userLevel, queries.usersAboveInAllReboot, queries.usersAboveInCohort, queries.userXP, queries.leadershipCount]);

    const fetchUsersAbove = async (inCohort = false) => {
        const query = inCohort ? queries.usersAboveInCohort : queries.usersAboveInAllReboot;
        const variables = inCohort
            ? {level: userLevel, eventId: cohort}
            : {level: userLevel};

        try {
            const result = await queryData(query, variables);
            if (result && result.data && result.data.event_user) {
                console.log(`Fetched ${result.data.event_user.length} users above`);
                if (inCohort) {
                    setUsersAboveInCohortList(result.data.event_user);
                    setShowUsersAboveInCohort(true);
                } else {
                    setUsersAboveList(result.data.event_user);
                    setShowUsersAbove(true);
                }
            } else {
                console.error('Failed to fetch users above');
            }
        } catch (error) {
            console.error('Error fetching users above:', error);
        }
    };

    const fetchXpProjects = useCallback(async () => {
        try {
            const result = await queryData(queries.xpForProjects, {userLogin: userInfo.login});
            console.log('Fetched XP projects:', result);
            if (result && result.data && result.data.transaction) {
                setXpForProjects(result.data.transaction);
            } else {
                console.error('Failed to fetch XP projects');
                setXpForProjects([]);
            }
        } catch (error) {
            console.error('Error fetching XP projects:', error);
            setXpForProjects([]);
        }
    }, [queryData, queries.xpForProjects, userInfo]);

    const fetchLeadershipProjects = useCallback(async () => {
        try {
            const result = await queryData(queries.leadershipProjects, {userLogin: userInfo.login});
            if (result && result.data && result.data.group) {
                setLeadershipProjects(result.data.group);
            } else {
                console.error('Failed to fetch leadership projects');
            }
        } catch (error) {
            console.error('Error fetching leadership projects:', error);
        }
    }, [queryData, queries.leadershipProjects, userInfo]);

    const fetchTeamLeaderData = useCallback(async () => {
        try {
            const result = await queryData(queries.teamLeaders, {userLogin: userInfo.login});
            if (result && result.data && result.data.group) {
                const leaderCounts = result.data.group.reduce((acc, project) => {
                    acc[project.captainLogin] = (acc[project.captainLogin] || 0) + 1;
                    return acc;
                }, {});

                const mostFrequent = Object.entries(leaderCounts).reduce((a, b) => a[1] > b[1] ? a : b);
                setMostFrequentLeader({
                    login: mostFrequent[0],
                    count: mostFrequent[1]
                });

                setTeamLeaderProjects(result.data.group.filter(project => project.captainLogin === mostFrequent[0]));
            } else {
                console.error('Failed to fetch team leader data');
            }
        } catch (error) {
            console.error('Error fetching team leader data:', error);
        }
    }, [queryData, queries.teamLeaders, userInfo]);

    const fetchAuditRatioRankings = useCallback(async (inCohort = false) => {
        try {
            const result = await queryData(queries.usersLevelGreaterThanInAll, {level: 0});
            if (result && result.data && result.data.event_user) {
                const sortedUsers = result.data.event_user
                    .sort((a, b) => {
                        const ratioA = parseFloat(a.userAuditRatio) || 0;
                        const ratioB = parseFloat(b.userAuditRatio) || 0;
                        return ratioB - ratioA;
                    });

                const userRank = sortedUsers.findIndex(user => user.userLogin === userInfo.login) + 1;
                setUserAuditRatioRank(userRank);

                if (inCohort) {
                    const cohortUsers = sortedUsers.filter(user => user.event.id === cohort);
                    setCohortAuditRatioRanking(cohortUsers);
                    const userCohortRank = cohortUsers.findIndex(user => user.userLogin === userInfo.login) + 1;
                    setUserCohortAuditRatioRank(userCohortRank);
                } else {
                    setAuditRatioRanking(sortedUsers);
                }
            } else {
                console.error('Failed to fetch audit ratio rankings');
            }
        } catch (error) {
            console.error('Error fetching audit ratio rankings:', error);
        }
    }, [queryData, queries.usersLevelGreaterThanInAll, cohort, userInfo]);

    const handleGroupSearch = async () => {
        try {
            const pathWithWildcards = `%${searchPath}%`;
            const result = await queryData(queries.groupSearch, {
                eventId: selectedEventId,
                pathSearch: pathWithWildcards,
                status: groupStatus
            });
            
            if (result && result.data) {
                setGroupSearchResults(result.data.group);
                setHasSearched(true);
            } else if (result && result.errors) {
                setError('Error in group search: ' + result.errors[0].message);
            }
        } catch (error) {
            setError('Error performing group search: ' + error.message);
        }
    };

    useEffect(() => {
        const cookies = parseCookies();
        const savedJwt = cookies.jwt;
        if (savedJwt) {
            setJwt(savedJwt);
            setIsSignedIn(true);
            setHeaderMessage("You shouldn't share your password with strangers.");
            setFooterToggle(true);
        }
    }, []);


    useEffect(() => {
        if (isSignedIn) {
            fetchSkillsData();
            fetchUserData();
        }
    }, [isSignedIn, fetchSkillsData, fetchUserData]);

    useEffect(() => {
        if (isSignedIn && userInfo) {
            fetchLeadershipProjects();
            fetchTeamLeaderData();
            fetchXpProjects();
        }
    }, [isSignedIn, userInfo, fetchLeadershipProjects, fetchTeamLeaderData, fetchXpProjects]);

    const createRadarChart = useCallback(() => {
        const svg = d3.select(skillsChartRef.current);
        svg.selectAll("*").remove();

        const container = d3.select(skillsChartRef.current.parentNode);
        const containerWidth = container.node().getBoundingClientRect().width;
        const containerHeight = Math.min(containerWidth, 400);

        svg.attr("width", "100%")
            .attr("height", containerHeight)
            .attr("viewBox", `0 0 ${containerWidth} ${containerHeight}`)
            .attr("preserveAspectRatio", "xMidYMid meet");

        const margin = {top: 40, right: 50, bottom: 50, left: 50};
        const width = containerWidth - margin.left - margin.right;
        const height = containerHeight - margin.top - margin.bottom;
        const radius = Math.min(width, height) / 2 * 0.8;

        const g = svg.append("g")
            .attr("transform", `translate(${containerWidth / 2}, ${containerHeight / 2})`);

        const data = skillsData.slice(0, 10);
        const angleSlice = Math.PI * 2 / data.length;

        const rScale = d3.scaleLinear()
            .range([0, radius])
            .domain([0, d3.max(data, d => d.amount)]);

        // Draw the radar chart blobs
        const radarLine = d3.lineRadial()
            .curve(d3.curveLinearClosed)
            .radius(d => rScale(d.amount))
            .angle((d, i) => i * angleSlice);

        const radarArea = g.selectAll(".radarWrapper")
            .data([data])
            .enter().append("g")
            .attr("class", "radarWrapper");

        const radarPath = radarArea.append("path")
            .attr("class", "radarArea")
            .attr("d", radarLine)
            .style("fill", "rgb(116, 119, 191)")
            .style("fill-opacity", 0.5)
            .style("stroke", "rgb(116, 119, 191)")
            .style("stroke-width", "2px");

        // Create a unique breathing/expanding animation
        function breatheAnimation() {
            const originalScale = 1;
            const expandedScale = 1.05;
            const duration = 3000;

            function updatePath(scale) {
                const scaledRadarLine = d3.lineRadial()
                    .curve(d3.curveLinearClosed)
                    .radius(d => rScale(d.amount) * scale)
                    .angle((d, i) => i * angleSlice);

                return scaledRadarLine(data);
            }

            function animate() {
                radarPath
                    .transition()
                    .duration(duration)
                    .ease(d3.easeSinInOut)
                    .attrTween("d", function() {
                        return function(t) {
                            // Create a smooth sine wave between original and expanded scale
                            const scale = originalScale + (Math.sin(t * Math.PI) * (expandedScale - originalScale));
                            return updatePath(scale);
                        };
                    })
                    .on("end", animate); // Make it continuous
            }

            animate();
        }

        breatheAnimation();

        // Draw the circular grid with staggered appearance
        const axisGrid = g.append("g").attr("class", "axisWrapper");

        axisGrid.selectAll(".levels")
            .data(d3.range(1, 6).reverse())
            .enter()
            .append("circle")
            .attr("class", "gridCircle")
            .attr("r", 0)
            .style("fill", "none")
            .style("stroke", "#CDCDCD")
            .style("stroke-width", "0.5px")
            .transition()
            .duration(1000)
            .delay((d, i) => i * 200)
            .attr("r", d => radius / 5 * d);

        // Draw the axes
        const axis = axisGrid.selectAll(".axis")
            .data(data)
            .enter()
            .append("g")
            .attr("class", "axis");

        axis.append("line")
            .attr("x1", 0)
            .attr("y1", 0)
            .attr("x2", (d, i) => rScale(d3.max(data, d => d.amount)) * Math.cos(angleSlice * i - Math.PI / 2))
            .attr("y2", (d, i) => rScale(d3.max(data, d => d.amount)) * Math.sin(angleSlice * i - Math.PI / 2))
            .attr("class", "line")
            .style("stroke", "#CDCDCD")
            .style("stroke-width", "0.5px");

        // Append the labels and interactive circles
        axis.append("text")
            .attr("class", "legend")
            .style("font-size", "11px")
            .attr("text-anchor", "middle")
            .attr("dy", "0.35em")
            .attr("x", (d, i) => rScale(d3.max(data, d => d.amount) * 1.15) * Math.cos(angleSlice * i - Math.PI / 2))
            .attr("y", (d, i) => rScale(d3.max(data, d => d.amount) * 1.15) * Math.sin(angleSlice * i - Math.PI / 2))
            .text(d => d.skill)
            .style("fill", "#CCCCCC");

        // Add interactive circles
        axis.append("circle")
            .attr("class", "radarCircle")
            .attr("r", 5)
            .attr("cx", (d, i) => rScale(d.amount) * Math.cos(angleSlice * i - Math.PI / 2))
            .attr("cy", (d, i) => rScale(d.amount) * Math.sin(angleSlice * i - Math.PI / 2))
            .style("fill", "rgb(116, 119, 191)")
            .style("fill-opacity", 0.8)
            .style("stroke", "#fff")
            .style("stroke-width", "2px")
            .on("mouseover", function (event, d) {
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", 8)
                    .style("fill", "rgb(255, 215, 0)");

                // Show tooltip
                // const [x, y] = d3.pointer(event, svg.node());
                d3.select(".tooltip")
                    .style("opacity", 1)
                    .html(`${d.skill}: ${d.amount}`)
                // .style("left", `${x + 10}px`)
                // .style("top", `${y - 10}px`);
            })
            .on("mouseout", function () {
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", 5)
                    .style("fill", "rgb(116, 119, 191)");

                // Hide tooltip
                d3.select(".tooltip").style("opacity", 0);
            });

        // Add a title
        svg.append("text")
            .attr("x", containerWidth / 2)
            .attr("y", 20)
            .attr("text-anchor", "middle")
            .style("font-size", "16px")
            .style("fill", "#FFFFFF")
            .text("Top 10 Skills");

        // Add tooltip div if it doesn't exist
        if (d3.select(".tooltip").empty()) {
            d3.select(".skills-chart-container").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);
        }

    }, [skillsData]);

    useEffect(() => {
        if (skillsData.length > 0) {
            createRadarChart();
        }
    }, [skillsData, createRadarChart]);

    const createXPChart = useCallback((data) => {
        // Clear existing content
        const svg = d3.select("#xpChart");
        svg.selectAll("*").remove();

        // Get container dimensions
        const container = d3.select("#xpChart");
        const containerWidth = container.node().getBoundingClientRect().width;
        const containerHeight = Math.min(containerWidth, 400);

        // Create responsive SVG
        const mainSvg = svg.append("svg")
            .attr("width", "100%")
            .attr("height", containerHeight)
            .attr("viewBox", `0 0 ${containerWidth} ${containerHeight}`)
            .attr("preserveAspectRatio", "xMidYMid meet");

        // Calculate dimensions
        const radius = Math.min(containerWidth, containerHeight) / 2 * 0.8;

        // Create main group element
        const g = mainSvg.append("g")
            .attr("transform", `translate(${containerWidth / 2}, ${containerHeight / 2})`);

        g.append("g").attr("class", "slices");
        g.append("g").attr("class", "labels");

        const pie = d3.pie()
            .sort(null)
            .value(d => d.amount);

        const arc = d3.arc()
            .outerRadius(radius * 0.8)
            .innerRadius(radius * 0.4);

        const arcHover = d3.arc()
            .outerRadius(radius * 0.9)
            .innerRadius(radius * 0.4);

        const outerArc = d3.arc()
            .innerRadius(radius * 0.9)
            .outerRadius(radius * 0.9);

        const colorPalette = [
            '#FF6B6B', // Coral Red
            '#4ECDC4', // Turquoise
            '#45B7D1', // Sky Blue
            '#96CEB4', // Mint
            '#FFEEAD', // Cream
            '#FFD93D', // Yellow
            '#6C5CE7', // Purple
            '#A8E6CF', // Mint Green
            '#FF8B94', // Pink
            '#A8D8EA'  // Light Blue
        ];

        const color = d3.scaleOrdinal(colorPalette)
            .domain(data.map(d => d.name));

        // Add gradient definitions
        const defs = mainSvg.append("defs");
        
        // Create gradients for each color
        colorPalette.forEach((color, i) => {
            const gradient = defs.append("linearGradient")
                .attr("id", `gradient-${i}`)
                .attr("x1", "0%")
                .attr("y1", "0%")
                .attr("x2", "100%")
                .attr("y2", "100%");

            gradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", color)
                .attr("stop-opacity", 0.8);

            gradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", d3.color(color).brighter(0.5))
                .attr("stop-opacity", 0.9);
        });

        const tooltip = d3.select("body").append("div")
            .attr("class", "tooltip")
            .style("opacity", 0);

        // Add subtle rotation animation
        g.transition()
            .ease(d3.easeLinear)
            .duration(100000)
            .attrTween("transform", () => {
                return (t) => {
                    const currentAngle = 360 * t;
                    return `translate(${containerWidth / 2}, ${containerHeight / 2}) rotate(${currentAngle})`;
                };
            })
            .on("end", function() {
                d3.select(this).call(() => g.transition());
            });

        function change(data) {
            const pieData = pie(data);

            const slice = g.select(".slices").selectAll("path.slice")
                .data(pieData, d => d.data.name);

            // Enter selection with enhanced styling
            const enterSlices = slice.enter()
                .append("path")
                .attr("class", "slice")
                .style("fill", (d, i) => `url(#gradient-${i % colorPalette.length})`)
                .style("filter", "url(#glow)")
                .style("stroke", "#fff")
                .style("stroke-width", "2px")
                .style("stroke-opacity", 0.5);

            // Add glow effect
            const filter = defs.append("filter")
                .attr("id", "glow");

            filter.append("feGaussianBlur")
                .attr("stdDeviation", "3")
                .attr("result", "coloredBlur");

            const feMerge = filter.append("feMerge");
            feMerge.append("feMergeNode")
                .attr("in", "coloredBlur");
            feMerge.append("feMergeNode")
                .attr("in", "SourceGraphic");

            // Enhanced hover effects
            enterSlices.on("mouseover", function(event, d) {
                const slice = d3.select(this);
                
                // Pulse animation
                slice.transition()
                    .duration(200)
                    .attr("d", arcHover)
                    .style("filter", "url(#glow)")
                    .style("stroke-width", "3px")
                    .style("stroke-opacity", 1);

                // Enhanced tooltip
                tooltip.transition()
                    .duration(200)
                    .style("opacity", 0.9)
                    .style("background", d3.color(color(d.data.name)).brighter(0.5))
                    .style("box-shadow", "0 0 15px rgba(0,0,0,0.2)");
                
                tooltip.html(`
                    <strong>${d.data.path.split('/').pop()}</strong><br/>
                    <span style="font-size: 1.2em">${d.data.amount} XP</span>
                `)
                    .style("left", (event.pageX + 5) + "px")
                    .style("top", (event.pageY - 28) + "px");
            })
            .on("mouseout", function() {
                const slice = d3.select(this);
                
                slice.transition()
                    .duration(500)
                    .attr("d", arc)
                    .style("filter", null)
                    .style("stroke-width", "2px")
                    .style("stroke-opacity", 0.5);

                tooltip.transition()
                    .duration(500)
                    .style("opacity", 0);
            });

            // Update selection
            enterSlices.merge(slice)
                .transition()
                .duration(1000)
                .attrTween("d", function(d) {
                    this._current = this._current || d;
                    const interpolate = d3.interpolate(this._current, d);
                    this._current = interpolate(0);
                    return t => arc(interpolate(t));
                });

            slice.exit()
                .transition()
                .duration(500)
                .attrTween("d", function(d) {
                    const end = {...d, startAngle: d.endAngle};
                    const interpolate = d3.interpolate(d, end);
                    return t => arc(interpolate(t));
                })
                .remove();

            // Enhanced labels
            const text = g.select(".labels").selectAll("text")
                .data(pieData, d => d.data.name);

            const enterText = text.enter()
                .append("text")
                .attr("dy", ".35em")
                .attr("class", "label")
                .style("opacity", 0)
                .style("font-size", "12px")
                .style("font-weight", "bold")
                .style("fill", "#fff");

            enterText.merge(text)
                .transition()
                .duration(1000)
                .style("opacity", 1)
                .attrTween("transform", function(d) {
                    this._current = this._current || d;
                    const interpolate = d3.interpolate(this._current, d);
                    this._current = interpolate(0);
                    return t => {
                        const d2 = interpolate(t);
                        const pos = outerArc.centroid(d2);
                        pos[0] = radius * (midAngle(d2) < Math.PI ? 1.2 : -1.2);
                        return `translate(${pos})`;
                    };
                })
                .styleTween("text-anchor", function(d) {
                    this._current = this._current || d;
                    const interpolate = d3.interpolate(this._current, d);
                    this._current = interpolate(0);
                    return t => {
                        const d2 = interpolate(t);
                        return midAngle(d2) < Math.PI ? "start" : "end";
                    };
                });

            text.exit()
                .transition()
                .duration(500)
                .style("opacity", 0)
                .remove();
        }

        function midAngle(d) {
            return d.startAngle + (d.endAngle - d.startAngle) / 2;
        }

        change(data);
    }, []);

    useEffect(() => {
        if (xpForProjects.length > 0) {
            createXPChart(xpForProjects);
        }
    }, [xpForProjects, createXPChart]);

    const getRank = (level) => {
        if (level < 10) return "Aspiring developer";
        if (level < 20) return "Beginner developer";
        if (level < 30) return "Apprentice developer";
        if (level < 40) return "Assistant developer";
        if (level < 50) return "Basic developer";
        if (level < 55) return "Junior developer";
        if (level < 60) return "Confirmed developer";
        return "Full-Stack developer";
    };

    const getCohortNumber = (eventId) => {
      switch (eventId) {
          case 72:
              return 2;
          case 20:
              return 1;
          default:
              return 3;
      }
  };

  const formatAuditRatio = (ratio) => {
    return typeof ratio === 'number' ? ratio.toFixed(2) : "Invalid";
};


  return (
      <div className="container">
          <header>{headerMessage}</header>
          <main>
              {!isSignedIn ? (
                  <form id="signinForm" onSubmit={handleSignIn}>
                      <label htmlFor="login">Username or Email:</label>
                      <input id="login" name="login" type="text" required/>
                      <label htmlFor="password">Password:</label>
                      <input id="password" name="password" type="password" required/>
                      {error && <div className="error-message">{error}</div>}
                      <button type="submit">Sign in</button>
                  </form>
              ) : (
                  <div className="flexContainer" id="signedInContent">
                      <div className="section">
                          <div className="title">Achievements</div>
                          {userInfo && userLevel !== null && userXP !== null && usersAboveLevel !== null && usersAboveLevelInCohort !== null && cohort !== null && cohortRank !== null ? (
                              <div>
                                  <div className="namelvlrank">
                                      <div className="name">{`${userInfo.firstName} ${userInfo.lastName}`}</div>
                                      <div className="lvl">{`Level ${userLevel}`}</div>
                                      <div className="rank">{getRank(userLevel)}</div>
                                  </div>
                                  <div className="info">
                                      <p>Total XP: {userXP}</p>
                                      <p>Audit Ratio: {formatAuditRatio(userInfo.auditRatio)}</p>
                                      <p>Total Up: {userInfo.totalUp}</p>
                                      <p>Total Down: {userInfo.totalDown}</p>
                                      <p>You are top {usersAboveLevel} in all of Reboot01</p>
                                      <p>You are in Cohort {getCohortNumber(cohort)}</p>
                                      <p>You are top {usersAboveLevelInCohort} in your Reboot01 cohort</p>
                                  </div>
                              </div>
                          ) : (
                              <p>Loading user information...</p>
                          )}
                      </div>
                      <div className="section">
                          <div className="title">Leadership Information</div>
                          <div className="info">
                              {leadershipCount !== null && (
                                  <>
                                      <p>You were team leader for {leadershipCount} projects</p>
                                      <button onClick={() => {
                                          setShowLeadershipProjects(!showLeadershipProjects);
                                          if (!showLeadershipProjects && leadershipProjects.length === 0) {
                                              fetchLeadershipProjects();
                                          }
                                      }}>
                                          {showLeadershipProjects ? 'Hide projects' : 'Which projects?'}
                                      </button>
                                      {showLeadershipProjects && (
                                          <ul className={showLeadershipProjects ? 'show' : ''}>
                                              {leadershipProjects.map((project, index) => (
                                                  <li key={index}>
                                                      <strong>{project.object.name}</strong>
                                                      <br/>
                                                      Members: {project.members.map(member => member.userLogin).join(', ')}
                                                  </li>
                                              ))}
                                          </ul>
                                      )}
                                  </>
                              )}
                          </div>
                      </div>
                      <div className="section">
                          <div className="title">Team Leader Information</div>
                          <div className="info">
                              {mostFrequentLeader ? (
                                  <>
                                      <p>{mostFrequentLeader.login} was your team leader for {mostFrequentLeader.count} projects</p>
                                      <button onClick={() => {
                                          setShowTeamLeaderProjects(!showTeamLeaderProjects);
                                          if (!showTeamLeaderProjects && teamLeaderProjects.length === 0) {
                                              fetchTeamLeaderData();
                                          }
                                      }}>
                                          {showTeamLeaderProjects ? 'Hide projects' : 'Which projects?'}
                                      </button>
                                      {showTeamLeaderProjects && (
                                          <div className="users-list-container">
                                              <ul className={showTeamLeaderProjects ? 'show users-list' : 'users-list'}>
                                                  {teamLeaderProjects.map((project, index) => (
                                                      <li key={index}>
                                                          <strong>{project.object.name}</strong>
                                                          <br/>
                                                          Members: {project.members.map(member => member.userLogin).join(', ')}
                                                      </li>
                                                  ))}
                                              </ul>
                                          </div>
                                      )}
                                  </>
                              ) : (
                                  <p>You have never had a team leader - you've always been the project leader!</p>
                              )}
                          </div>
                      </div>
                      <div className="section">
                          <div className="title">Top 10 Skills</div>
                          <div className="section-content">
                              <div className="chart-container skills-chart-container">
                                  <svg ref={skillsChartRef}></svg>
                              </div>
                          </div>
                      </div>
                      <div className="section">
                          <div className="title">XP Projects</div>
                          <div className="section-content">
                              <div className="chart-container xp-chart-container">
                                  <div id="xpChart"></div>
                              </div>
                          </div>
                      </div>
                      <div className="section">
                          <div className="title">Rankings</div>
                          <div className="info">
                              <p>You are top {usersAboveLevel } in all of Reboot01</p>
                              <button onClick={() => {
                                  setShowUsersAbove(!showUsersAbove);
                                  if (!showUsersAbove && usersAboveList.length === 0) {
                                      console.log('Fetching users above');
                                      fetchUsersAbove(false);
                                  }
                              }}>
                                  {showUsersAbove ? 'Hide users above you' : 'Want to see who is above you?'}
                              </button>
                              {showUsersAbove && (
                                  <>
                                      <div className="users-list-container">
                                          <ul className="show users-list">
                                              {usersAboveList
                                                .sort((a, b) => b.level - a.level)
                                                .map((user, index, array) => {
                                                    let rank;
                                                    if (index === 0) {
                                                        rank = 1;
                                                    } else {
                                                        rank = user.level === array[index - 1].level ? 
                                                            array[index - 1].rank : 
                                                            index + 1;
                                                    }
                                                    user.rank = rank;
                                                    return (
                                                        <li key={user.userLogin}>{rank}. {user.userLogin} (Cohort {getCohortNumber(user.event.id)}) - Level {user.level}</li>
                                                    );
                                                })}
                                          </ul>
                                      </div>
                                  </>
                              )}
                          </div>
                      </div>
                      <div className="section">
                          <div className="title">Cohort Ranking</div>
                          <div className="info">
                              <p>You are in Cohort {getCohortNumber(cohort)}</p>
                              <p>You are top {usersAboveLevelInCohort} in your Reboot01 cohort</p>
                              <button onClick={() => {
                                  setShowUsersAboveInCohort(!showUsersAboveInCohort);
                                  if (!showUsersAboveInCohort && usersAboveInCohortList.length === 0) {
                                      fetchUsersAbove(true);
                                  }
                              }}>
                                      {showUsersAboveInCohort ? 'Hide users above you in cohort' : 'Want to see who is above you in your cohort?'}
                                </button>
                                {showUsersAboveInCohort && (
                                    <div className="users-list-container">
                                        <ul className="show users-list">
                                            {usersAboveInCohortList
                                                .sort((a, b) => b.level - a.level)
                                                .map((user, index, array) => {
                                                    let rank;
                                                    if (index === 0) {
                                                        rank = 1;
                                                    } else {
                                                        rank = user.level === array[index - 1].level ? 
                                                            array[index - 1].rank : 
                                                            index + 1;
                                                    }
                                                    user.rank = rank;
                                                    return (
                                                        <li key={user.userLogin}>{rank}. {user.userLogin} - Level {user.level}</li>
                                                    );
                                                })}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        </div>
                         <div className="section">
                            <div className="title">Audit Ratio Ranking</div>
                            <div className="info">
                                <p>Your Audit Ratio: {formatAuditRatio(userInfo?.auditRatio)}</p>
                                <p>You are in Cohort {getCohortNumber(cohort)}</p>
                                {userAuditRatioRank && <p>Your Audit Ratio Rank: {userAuditRatioRank} out of {auditRatioRanking.length}</p>}
                                <button onClick={() => {
                                    setShowAuditRatioRanking(!showAuditRatioRanking);
                                    if (!showAuditRatioRanking && auditRatioRanking.length === 0) {
                                        fetchAuditRatioRankings(false);
                                    }
                                }}>
                                    {showAuditRatioRanking ? 'Hide Audit Ratio Ranking' : 'Show Audit Ratio Ranking'}
                                </button>
                                {showAuditRatioRanking && (
                                    <div className="users-list-container">
                                        <ul className="show users-list">
                                            {auditRatioRanking.map((user, index) => (
                                                <li key={user.userLogin}>
                                                    {index + 1}. {user.userLogin} (Cohort {getCohortNumber(user.event.id)}) - Audit Ratio: {formatAuditRatio(user.userAuditRatio)}
                                                    {user.userLogin === userInfo.login && " (You)"}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="section">
                            <div className="title">Cohort Audit Ratio Ranking</div>
                            <div className="info">
                                <p>Your Cohort: {getCohortNumber(cohort)}</p>
                                {userCohortAuditRatioRank && <p>Your Cohort Audit Ratio Rank: {userCohortAuditRatioRank} out of {cohortAuditRatioRanking.length}</p>}
                                <button onClick={() => {
                                    setShowCohortAuditRatioRanking(!showCohortAuditRatioRanking);
                                    if (!showCohortAuditRatioRanking && cohortAuditRatioRanking.length === 0) {
                                        fetchAuditRatioRankings(true);
                                    }
                                }}>
                                    {showCohortAuditRatioRanking ? 'Hide Cohort Audit Ratio Ranking' : 'Show Cohort Audit Ratio Ranking'}
                                </button>
                                {showCohortAuditRatioRanking && (
                                    <div className="users-list-container">
                                        <ul className="show users-list">
                                            {cohortAuditRatioRanking.map((user, index) => (
                                                <li key={user.userLogin}>
                                                    {index + 1}. {user.userLogin} - Audit Ratio: {formatAuditRatio(user.userAuditRatio)}
                                                    {user.userLogin === userInfo.login && " (You)"}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="group-search-section section">
                            <div className="title">Group Search</div>
                            <div className="search-controls">
                                <select 
                                    className="form-element"
                                    value={selectedEventId} 
                                    onChange={(e) => setSelectedEventId(parseInt(e.target.value))}
                                >
                                    <option value={20}>Cohort 1</option>
                                    <option value={72}>Cohort 2</option>
                                    <option value={250}>Cohort 3</option>
                                </select>
                                
                                <input
                                    className="form-element"
                                    type="text"
                                    placeholder="Search project path..."
                                    value={searchPath}
                                    onChange={(e) => setSearchPath(e.target.value)}
                                />
                                
                                <select 
                                    className="form-element"
                                    value={groupStatus} 
                                    onChange={(e) => setGroupStatus(e.target.value)}
                                >
                                    <option value="working">Working</option>
                                    <option value="audit">Audit</option>
                                    <option value="setup">Setup</option>
                                    <option value="finished">Finished</option>
                                </select>
                                
                                <button className="button" onClick={handleGroupSearch}>Search Groups</button>
                                
                                {hasSearched && (
                                    groupSearchResults.length > 0 ? (
                                        <div className="teams-grid">
                                            {groupSearchResults.map((group, index) => (
                                                <div key={index} className="team-card">
                                                    <h3>Team {index + 1} ({group.path.split('/').pop()}) </h3>
                                                    <div className="team-members">
                                                        {group.members.map((member, memberIndex) => (
                                                            <div key={memberIndex} className="team-member">
                                                                {member.userLogin} ({member.user.firstName} {member.user.lastName})
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div>No teams were found. Please try adjusting your search criteria.</div>
                                    )
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </main>
            {footerToggle ? (<footer><button onClick={handleLogout} className="logout-button">Too late I got your password, but you can still logout</button></footer>
) : (
  <footer>Thank you</footer>
)}
        </div>
    );
}

export default App;