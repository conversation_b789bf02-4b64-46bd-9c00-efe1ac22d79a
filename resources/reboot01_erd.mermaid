erDiagram
    %% Core Entities
    USER {
        int id PK
        string githubLogin
        string profile
        json attrs
        timestamp createdAt
        timestamp updatedAt
        string campus
    }
    
    ROLE {
        int id PK
        string slug
        string name
        string description
        timestamp createdAt
        timestamp updatedAt
    }
    
    OBJECT {
        int id PK
        string name
        string type
        string status
        json attrs
        json childrenAttrs
        timestamp createdAt
        timestamp updatedAt
        string campus
        int referenceId FK
        timestamp referencedAt
        int authorId FK
    }
    
    EVENT {
        int id PK
        timestamp createdAt
        timestamp endAt
        int registrationId FK
        int objectId FK
        int parentId FK
        string status
        string path
        string campus
        string code
    }
    
    REGISTRATION {
        int id PK
        timestamp createdAt
        timestamp startAt
        timestamp endAt
        timestamp eventStartAt
        int objectId FK
        int parentId FK
        json attrs
        string path
        string campus
    }
    
    GROUP {
        int id PK
        int objectId FK
        int eventId FK
        int captainId FK
        timestamp createdAt
        timestamp updatedAt
        string status
        string path
        string campus
    }
    
    PROGRESS {
        int id PK
        timestamp createdAt
        timestamp updatedAt
        int userId FK
        int groupId FK
        int eventId FK
        string version
        float grade
        boolean isDone
        string path
        string campus
        int objectId FK
    }
    
    RESULT {
        int id PK
        timestamp createdAt
        timestamp updatedAt
        float grade
        json attrs
        string type
        int userId FK
        int groupId FK
        int objectId FK
        string path
        string version
        int eventId FK
        boolean isLast
        string campus
    }
    
    AUDIT {
        int id PK
        int groupId FK
        int auditorId FK
        json attrs
        float grade
        timestamp createdAt
        timestamp updatedAt
        string code
        int resultId FK
        string version
        timestamp endAt
        boolean private
    }
    
    MATCH {
        int id PK
        timestamp createdAt
        timestamp updatedAt
        int objectId FK
        int userId FK
        int matchId FK
        boolean confirmed
        boolean bet
        boolean result
        string path
        string campus
        int eventId FK
    }
    
    TRANSACTION {
        int id PK
        string type
        float amount
        int userId FK
        json attrs
        timestamp createdAt
        string path
        int objectId FK
        int eventId FK
        string campus
    }
    
    RECORD {
        int id PK
        int userId FK
        int authorId FK
        string message
        timestamp banEndAt
        timestamp createdAt
    }
    
    %% Junction Tables
    USER_ROLE {
        int id PK
        int userId FK
        int roleId FK
    }
    
    GROUP_USER {
        int id PK
        int userId FK
        int groupId FK
        boolean confirmed
        timestamp createdAt
        timestamp updatedAt
    }
    
    EVENT_USER {
        int id PK
        int userId FK
        int eventId FK
        timestamp createdAt
    }
    
    REGISTRATION_USER {
        int id PK
        int registrationId FK
        int userId FK
        timestamp createdAt
    }
    
    OBJECT_CHILD {
        int id PK
        int parentId FK
        int childId FK
        json attrs
        string key
        int index
    }
    
    %% Relationships
    
    %% User System
    USER ||--o{ USER_ROLE : "has roles"
    ROLE ||--o{ USER_ROLE : "assigned to users"
    USER ||--o{ GROUP_USER : "belongs to groups"
    GROUP ||--o{ GROUP_USER : "contains users"
    USER ||--o{ EVENT_USER : "participates in events"
    EVENT ||--o{ EVENT_USER : "has participants"
    USER ||--o{ REGISTRATION_USER : "registers for"
    REGISTRATION ||--o{ REGISTRATION_USER : "has registrants"
    
    %% Object System
    OBJECT ||--o{ OBJECT_CHILD : "has children"
    OBJECT ||--o{ OBJECT_CHILD : "is child of"
    OBJECT ||--o{ EVENT : "instantiated as"
    OBJECT ||--o{ REGISTRATION : "registered for"
    USER ||--o{ OBJECT : "authors"
    OBJECT ||--o{ OBJECT : "references"
    
    %% Event System
    EVENT ||--|| REGISTRATION : "requires"
    EVENT ||--o{ EVENT : "has parent"
    
    %% Group System
    USER ||--o{ GROUP : "captains"
    OBJECT ||--o{ GROUP : "worked on by"
    EVENT ||--o{ GROUP : "contains"
    
    %% Progress & Results
    USER ||--o{ PROGRESS : "makes"
    GROUP ||--o{ PROGRESS : "tracked for"
    EVENT ||--o{ PROGRESS : "occurs in"
    OBJECT ||--o{ PROGRESS : "progressed through"
    
    USER ||--o{ RESULT : "achieves"
    GROUP ||--o{ RESULT : "produces"
    OBJECT ||--o{ RESULT : "evaluated for"
    EVENT ||--o{ RESULT : "generated in"
    
    %% Audit System
    GROUP ||--o{ AUDIT : "audited"
    USER ||--o{ AUDIT : "conducts"
    AUDIT ||--o{ RESULT : "generates"
    
    %% Match System
    USER ||--o{ MATCH : "requests"
    USER ||--o{ MATCH : "matched with"
    OBJECT ||--o{ MATCH : "requires"
    EVENT ||--o{ MATCH : "contains"
    
    %% Transactions & Records
    USER ||--o{ TRANSACTION : "receives"
    OBJECT ||--o{ TRANSACTION : "rewards for"
    EVENT ||--o{ TRANSACTION : "earned in"
    
    USER ||--o{ RECORD : "has records"
    USER ||--o{ RECORD : "creates records"