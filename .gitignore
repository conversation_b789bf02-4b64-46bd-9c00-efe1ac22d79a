# Backend
/backend/bin/
/backend/vendor/
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
/backend/.env

# Frontend
/frontend/node_modules/
/frontend/dist/
/frontend/build/
/frontend/.env
/frontend/.env.local
/frontend/.env.*.local
/frontend/coverage/
/frontend/.DS_Store
/frontend/.vite
/frontend/*.log
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*
/frontend/.pnpm-debug.log*

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.pem

# Vercel
.vercel
.env*.local

# Logs
logs
*.log

# Testing
coverage/
.nyc_output/

# Cache directories
.npm/
.eslintcache
.stylelintcache
.parcel-cache/
.cache/
resources/Screenshot 2025-07-08 at 04.55.30.png
resources/Screenshot 2025-07-08 at 04.55.34.png
resources/Screenshot 2025-07-08 at 04.55.36.png
resources/Screenshot 2025-07-08 at 04.55.40.png
resources/Screenshot 2025-07-08 at 04.55.43.png
resources/Screenshot 2025-07-08 at 04.55.48.png
resources/Screenshot 2025-07-08 at 04.55.51.png
resources/Screenshot 2025-07-08 at 04.56.08.png
resources/Screenshot 2025-07-08 at 04.56.12.png
resources/Screenshot 2025-07-08 at 04.56.19.png
resources/Screenshot 2025-07-08 at 04.56.22.png
resources/Screenshot 2025-07-08 at 04.56.26.png
resources/Screenshot 2025-07-08 at 04.56.30.png
resources/Screenshot 2025-07-08 at 04.56.33.png
resources/Screenshot 2025-07-08 at 04.56.37.png
resources/Screenshot 2025-07-08 at 04.56.39.png
resources/Screenshot 2025-07-08 at 04.56.43.png
frontend/src/hooks/useGraphQL.js
resources/Screenshot 2025-07-09 at 22.36.50.png
resources/Screenshot 2025-07-09 at 22.36.56.png
resources/Screenshot 2025-07-09 at 22.37.04.png
resources/Screenshot 2025-07-09 at 22.37.06.png
resources/Screenshot 2025-07-09 at 22.37.09.png
resources/Screenshot 2025-07-09 at 22.37.37.png
node_modules

# ---> Go
# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
# Test binary, built with `go test -c`
*.test
# Output of the go coverage tool, specifically when used with LiteIDE
*.out
# Dependency directories (remove the comment below to include it)
# vendor/
# Go workspace file
go.work
# ---> Linux
*~
# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*
# KDE directory preferences
.directory
# Linux trash folder which might appear on any partition or disk
.Trash-*
# .nfs files are created when an open file is removed but is still being accessed
.nfs*
# ---> macOS
# General
*.DS_Store
.AppleDouble
.LSOverride
# Icon must end with two \r
Icon
# Thumbnails
._*
# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
# ---> Windows
# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
# Dump file
*.stackdump
# Folder config file
[Dd]esktop.ini
# Recycle Bin used on file shares
$RECYCLE.BIN/
# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp
# Windows shortcuts
*.lnk

resources/exampleshoots/
resources/shoots/