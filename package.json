{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:auth": "vitest run tests/unit/auth/", "test:graphql": "vitest run tests/unit/graphql/", "test:utils": "vitest run tests/unit/utils/", "test:components": "vitest run tests/unit/components/", "test:integration": "vitest run tests/integration/", "test:unit": "vitest run tests/unit/", "test:performance": "vitest run tests/performance/", "analyze": "npx vite-bundle-analyzer dist", "clean": "rm -rf dist node_modules/.vite", "type-check": "tsc --noEmit"}, "dependencies": {"@apollo/client": "^3.13.8", "@types/d3": "^7.4.3", "@types/lodash": "^4.17.20", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "graphql": "^16.11.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^7.0.3", "vitest": "^2.1.8"}}